{"name": "iconverter", "version": "0.0.0", "scripts": {"dev": "vite --port 8080", "build": "vite build", "preview": "vite preview --port 4173", "server": "cd ../image-converter-server && yarn dev", "test:unit": "vitest --environment jsdom", "test:e2e": "start-server-and-test preview http://localhost:4173/ 'cypress open --e2e'", "test:e2e:ci": "start-server-and-test preview http://localhost:4173/ 'cypress run --e2e'", "build-only": "vite build-only", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "postinstall": "patch-package"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@haluo/util": "2.0.14", "@types/qs": "6.9.7", "axios": "0.21.1", "blueimp-md5": "^2.19.0", "day": "0.0.2", "dayjs": "^1.11.10", "cropperjs": "^1.6.2", "element-plus": "^2.8.1", "heic2any": "^0.0.4", "pinia": "^2.1.7", "qs": "^6.10.1", "sass": "1.32.8", "vue": "^3.4.38", "vue-masonry": "^0.16.0", "vue3-moveable": "^0.28.0", "vue-router": "^4.1.2", "vuedraggable": "4.1.0"}, "devDependencies": {"@rushstack/eslint-patch": "^1.1.0", "@types/js-cookie": "3.0.2", "@types/jsdom": "^16.2.14", "@types/node": "^20.11.0", "@types/qrcode": "^1.5.5", "@vitejs/plugin-vue": "^5.0.0", "@vitejs/plugin-vue-jsx": "^3.1.0", "@vue/eslint-config-prettier": "^7.0.0", "@vue/eslint-config-typescript": "^11.0.0", "@vue/test-utils": "^2.0.2", "@vue/tsconfig": "^0.1.3", "@vueuse/components": "^10.11.0", "@vueuse/core": "^10.11.0", "ali-oss": "^6.17.1", "compressorjs": "^1.2.1", "cypress": "^10.3.0", "eslint": "^8.5.0", "eslint-plugin-cypress": "^2.12.1", "eslint-plugin-vue": "^9.0.0", "fabric": "^5.4.0", "js-cookie": "3.0.1", "jsdom": "^20.0.0", "lodash-es": "^4.17.21", "lrz": "^4.9.41", "npm-run-all": "^4.1.5", "prettier": "^2.5.1", "qrcode": "^1.5.3", "start-server-and-test": "^1.14.0", "swiper": "^11.1.10", "typescript": "~5.2.0", "unplugin-auto-import": "^0.17.5", "unplugin-vue-components": "^0.26.0", "vanilla-colorful": "^0.7.2", "vite": "5.3.1", "vite-plugin-mock": "^3.0.1", "vite-plugin-svg-icons": "^1.1.0", "vite-plugin-vue-setup-extend": "^0.4.0", "vitest": "^0.18.1", "vue-demi": "^0.14.7", "vue-tsc": "^1.8.27"}}