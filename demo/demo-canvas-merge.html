<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <title>Merge Canvases</title>
  </head>
  <body>
    <canvas
      id="canvas1"
      width="100"
      height="100"
      style="border: 1px solid black"
    ></canvas>
    <canvas
      id="canvas2"
      width="100"
      height="100"
      style="border: 1px solid black"
    ></canvas>
    <canvas
      id="mergedCanvas"
      width="200"
      height="100"
      style="border: 1px solid red"
    ></canvas>

    <script>
      var canvas1 = document.getElementById('canvas1')
      var ctx1 = canvas1.getContext('2d')
      ctx1.fillStyle = 'blue'
      ctx1.fillRect(10, 10, 80, 80) // 绘制一个蓝色方块

      var canvas2 = document.getElementById('canvas2')
      var ctx2 = canvas2.getContext('2d')
      ctx2.fillStyle = 'green'
      ctx2.fillRect(10, 10, 80, 80) // 绘制一个绿色方块

      var mergedCanvas = document.getElementById('mergedCanvas')
      var ctxMerged = mergedCanvas.getContext('2d')

      // 将canvas1的内容绘制到mergedCanvas的左侧
      ctxMerged.drawImage(canvas1, 0, 0)

      // 将canvas2的内容绘制到mergedCanvas的右侧（这里假设我们想要它们并排）
      ctxMerged.drawImage(canvas2, 100, 0) // 注意x坐标是100，以便与canvas1的内容并排

      // 如果想要重叠，只需调整drawImage的x,y坐标即可
    </script>
  </body>
</html>
