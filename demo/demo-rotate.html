<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Rotate Element Demo</title>
    <style>
      .rotate {
        width: 100px;
        height: 100px;
        position: absolute;
        cursor: pointer;
        background-color: lightblue;
        display: flex;
        align-items: center;
        justify-content: center;
        user-select: none; /* 禁止文本选择 */
      }
    </style>
  </head>
  <body>
    <div id="rotate-container">
      <div
        class="rotate"
        style="left: 100px; top: 100px"
        onmousedown="startRotate(event)"
      >
        Rotate Me
      </div>
    </div>
    <script>
      let initialAngle = 0
      let initialMouseAngle = 0
      let rotateOrigin = { x: 0, y: 0 }
      let newAngle = 0 // 存储旋转角度
      let currentElement = null

      const normalizeAngle = (angle) => {
        return (angle + 360) % 360
      }

      const startRotate = (event) => {
        event.preventDefault()
        event.stopPropagation()

        currentElement = event.target

        const rect = currentElement.getBoundingClientRect()
        rotateOrigin = {
          x: rect.left + rect.width / 2,
          y: rect.top + rect.height / 2,
        }
        initialAngle = normalizeAngle(newAngle) // 使用上次旋转的最终角度

        const deltaX = event.clientX - rotateOrigin.x
        const deltaY = event.clientY - rotateOrigin.y
        initialMouseAngle = Math.atan2(deltaY, deltaX) * (180 / Math.PI)

        window.addEventListener('mousemove', rotateElement)
        window.addEventListener('mouseup', stopRotate)
      }

      const rotateElement = (event) => {
        const deltaX = event.clientX - rotateOrigin.x
        const deltaY = event.clientY - rotateOrigin.y

        const currentMouseAngle = Math.atan2(deltaY, deltaX) * (180 / Math.PI)
        const angleDifference = currentMouseAngle - initialMouseAngle
        newAngle = normalizeAngle(initialAngle + angleDifference)

        currentElement.style.transform = `rotate(${newAngle}deg)`
        console.log(
          'angle, initialAngle, newAngle',
          currentMouseAngle,
          initialAngle,
          newAngle
        )
      }

      const stopRotate = () => {
        window.removeEventListener('mousemove', rotateElement)
        window.removeEventListener('mouseup', stopRotate)
      }

      document.querySelectorAll('.rotate').forEach((element) => {
        element.addEventListener('mousedown', startRotate)
      })
    </script>
  </body>
</html>
