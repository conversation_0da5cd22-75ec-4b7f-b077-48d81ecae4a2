<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Fabric.js Demo</title>
    <script src="http://fabricjs.com/lib/fabric.js"></script>
  </head>
  <body>
    <canvas
      id="c"
      width="600"
      height="400"
      style="border: 1px solid #ccc"
    ></canvas>

    <script>
      // 初始化画布
      var canvas = new fabric.Canvas('c')

      // 添加图片
      fabric.Image.fromURL(
        'https://imgs2.58moto.com/essay_img/20240809/SAkTGb_1723203338077.jpeg!nowater600?_1080_607',
        function (img) {
          // 设置图片的大小和位置
          img.scale(0.5).set({
            left: 100,
            top: 100,
          })

          // 添加文字到图片上（确保文字在图片加载并设置位置后添加）
          var textSample = new fabric.Text('Hello Fabric.js!', {
            left: 250, // 根据图片大小调整文本位置
            top: 150, // 确保文本在图片上方或图片内部
            fontSize: 30,
            originX: 'left',
            originY: 'top',
          })

          //   canvas.add(textSample)
          //   canvas.add(img)

          // 如果你想要文本和图片作为一个整体被选中或移动，可以将它们组合起来
          var group = new fabric.Group([img, textSample], {
            left: 100,
            top: 100,
            // selectable: false
          })
          canvas.add(group)

          // 隐藏非边角控制点（这通常需要额外的逻辑，因为Fabric.js不直接支持）
          // 注意：以下代码是伪代码，用于说明目的
          group.on('moving', function (e) {
            // 隐藏非边角控制点的逻辑（需要自定义）
            console.log(e)
          })

          // 渲染画布
          canvas.renderAll()
        }
      )
    </script>
  </body>
</html>
