<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <title>Canvas</title>
  </head>
  <body>
    <canvas id="canvas1" style="border: 1px solid black"></canvas>

    <script>
      var canvas1 = document.getElementById('canvas1')
      var ctx1 = canvas1.getContext('2d')

      var img = new Image()
      img.onload = function () {
        ctx1.drawImage(img, 0, 0, 200, 200) // 绘制一个蓝色方块
        ctx1.font = 'bold 20px Arial' // 字体样式、大小和族
        ctx1.fillText('test', 50, 50)
      }
      img.src =
        'https://imgs.58moto.com/carport/20230310/20230310165600_encode.jpg!official300'
    </script>
  </body>
</html>
