<!DOCTYPE html>  
<html lang="en">  
<head>  
<meta charset="UTF-8">  
<title>Canvas 多图拼合（Base64）</title>  
<style>  
  canvas {  
    border: 1px solid black;  
  }  
</style>  
</head>  
<body>  
<canvas id="myCanvas" width="800" height="600"></canvas>  
  
<script>  
  // 假设这是你的Base64图片数据（这里仅作示例，使用实际数据替换）  
  var imageBase64s = [  
    'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAUA...', // 图片1的Base64  
    'data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD...', // 图片2的Base64  
    // 添加更多图片的Base64...  
  ];  
  
  var images = []; // 用于存储Image对象的数组  
  
  // 初始化Image对象并设置src为Base64  
  imageBase64s.forEach(function(base64, index) {  
    var img = new Image();  
    img.onload = function() {  
      // 所有图片加载完毕后执行的逻辑（如果需要的话）  
      if (images.length === imageBase64s.length) {  
        console.log('所有图片已加载完毕');  
        // 可以在这里调用一个函数来绘制所有图片  
        drawImages();  
      }  
    };  
    img.src = base64;  
    images.push(img);  
  });  
  
  function drawImages() {  
    var canvas = document.getElementById('myCanvas');  
    var ctx = canvas.getContext('2d');  
  
    // 绘制第一张图片，调整位置、宽高和缩放  
    ctx.drawImage(images[0], 50, 50, 200, 150); // x=50, y=50, width=200, height=150  
  
    // 绘制第二张图片，使用不同的位置、宽高和缩放  
    ctx.drawImage(images[1], 300, 100, images[1].width * 0.5, images[1].height * 0.5); // 缩放为原图的一半  
  
    // 如果需要，继续绘制更多图片...  
  }  
</script>  
</body>  
</html>