<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Watermark Grid Layout</title>
  <style>
    body {
      margin: 0;
      padding: 0;
      font-family: Arial, sans-serif;
    }

    .grid-container {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 20px; /* 设置网格项之间的间距 */
      padding: 20px;
    }

    .watermark {
      background-color: rgba(0, 0, 0, 0.1); /* 设置水印背景色，带透明度 */
      color: rgba(0, 0, 0, 0.5); /* 设置水印文字颜色，带透明度 */
      padding: 20px;
      text-align: center;
      border: 1px dashed rgba(0, 0, 0, 0.3); /* 设置水印边框 */
      border-radius: 5px;
    }

    .watermark:nth-child(odd) {
      background-color: rgba(0, 0, 0, 0.2); /* 交错效果 */
    }
  </style>
</head>
<body>
  <div class="grid-container">
    <!-- 水印信息的div -->
    <div class="watermark">Watermark 1</div>
    <div class="watermark">Watermark 2</div>
    <div class="watermark">Watermark 3</div>
    <div class="watermark">Watermark 4</div>
    <div class="watermark">Watermark 5</div>
    <div class="watermark">Watermark 6</div>
    <div class="watermark">Watermark 7</div>
    <div class="watermark">Watermark 8</div>
  </div>
</body>
</html>
