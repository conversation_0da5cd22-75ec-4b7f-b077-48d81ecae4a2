{"compilerOptions": {"target": "esnext", "module": "esnext", "lib": ["esnext", "dom", "DOM.Iterable"], "jsx": "preserve", "allowJs": false, "baseUrl": "./", "outDir": "./", "rootDir": "./", "resolveJsonModule": true, "esModuleInterop": true, "incremental": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "strict": true, "noImplicitAny": true, "sourceMap": true, "noUnusedLocals": true, "noUnusedParameters": false, "experimentalDecorators": true, "moduleResolution": "node", "types": ["vite/client", "jest"], "typeRoots": ["./node_modules/@types/", "./types"], "paths": {"@/*": ["src/*"], "#/*": ["types/*"]}, "suppressImplicitAnyIndexErrors": true}, "include": ["src/**/*.ts", "src/**/*.d.ts", "src/**/*.tsx", "src/**/*.vue", "types/**/*.d.ts", "types/**/*.ts", "mock/**/*.ts", "tests/**/*.ts", "vite.config.ts"], "exclude": ["node_modules", "dist", "**/*.js"]}