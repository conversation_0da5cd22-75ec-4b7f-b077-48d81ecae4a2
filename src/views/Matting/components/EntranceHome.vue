<template>
  <div v-show="!props.imgsMap.size" class="entrance-home">
    <div class="content">
      <div class="main-area">
        <div class="preview-area">
          <div class="portrait-area">
            <div class="upload-box">
              <div class="upload-content">
                <img src="/img/pic/second2/<EMAIL>" />
                <div class="upload-bottom">
                  <p class="title">人像抠图</p>
                  <p class="desc">证件照、旅拍、写真等各种人像场景</p>
                  <div class="picture-btns">
                    <!-- <img
                      class="btn btn-img"
                      src="/img/pic/second2/<EMAIL>"
                      @click="(e: any) => commonStore.webUpload(e)"
                    /> -->
                    <img
                      class="btn btn-img"
                      src="/img/pic/second2/<EMAIL>"
                      @click="(e: any) => commonStore.webUpload(e)"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="product-area">
            <div class="upload-box">
              <div class="upload-content">
                <img src="/img/pic/second2/<EMAIL>" />
                <div class="upload-bottom">
                  <p class="title">物品抠图</p>
                  <p class="desc">支持各类商品、衣服、动植物等</p>
                  <div class="picture-btns">
                    <!-- <img
                      class="btn btn-img"
                      src="/img/pic/second2/<EMAIL>"
                      @click="(e: any) => commonStore.webUpload(e)"
                    /> -->
                    <img
                      class="btn btn-img"
                      src="/img/pic/second2/<EMAIL>"
                      @click="(e: any) => commonStore.webUpload(e)"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { useCommonStore } from '@/pinia/common'
const commonStore = useCommonStore()

const props = defineProps({
  imgExts: {
    type: Array,
    default: [],
  },
  imgsMap: {
    type: Object,
    default: {},
  },
  callback: {
    // 添加图片回调，将选择后的图片传给回调
    type: Function,
    default: () => {},
  },
})
</script>

<style lang="scss" scoped>
.entrance-home {
  height: 100vh;
  background: #f5f6f7;

  .content {
    display: flex;
    height: 100%;
  }

  .main-area {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;

    .preview-area {
      display: flex;
      gap: 60px;
      justify-content: center;
      align-items: center;
      height: 100%;

      .portrait-area,
      .product-area {
        width: 370px;
        background: #fff;
        border-radius: 12px;
        transition: all 0.3s;
        user-select: none;
        overflow: hidden;

        &:hover {
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .upload-box {
          height: 100%;
          display: flex;
          align-items: center;
          justify-content: center;

          .upload-content {
            text-align: center;

            img {
              width: 370px;
              height: 425px;
              object-fit: contain;
            }

            .upload-bottom {
              height: 148px;
              .title {
                font-size: 20px;
                color: #333;
                font-weight: 500;
                padding: 16px 0 4px;
              }

              .desc {
                font-size: 14px;
                color: #999;
                font-weight: normal;
                padding-bottom: 16px;
              }

              .picture-btns {
                display: flex;
                justify-content: center;
                gap: 20px;

                .btn-img {
                  width: 140px;
                  height: 40px;
                  cursor: pointer;
                  transition: opacity 0.3s;

                  &:hover {
                    opacity: 0.8;
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}
</style>
