// @import '@/styles/btn.scss';
.picturePdf {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  height: 100%;
  background: #fff;

  @keyframes spin {
    0% {
      transform: rotate(0deg);
    } /* 动画开始时，图片旋转0度 */
    100% {
      transform: rotate(360deg);
    } /* 动画结束时，图片旋转360度 */
  }

  section {
    -webkit-box-flex: 1;
    -ms-flex: 1;
    flex: 1;
    height: 100%;
  }

  .list {
    padding: 0 20px;
    min-width: 943px;

    .item-wrap-web {
      // 计算
      max-height: calc(100% - 70px - 59px);
      // width: calc(100% + 32px);
      flex-wrap: wrap;
      // justify-content: space-between;
      // margin-right: 0;
      overflow-y: auto;

      .item {
        position: relative;
        width: 200px;
        height: 232px;
        margin-bottom: 20px;
        overflow: hidden;

        .img-wrap {
          width: 200px;
          height: 200px;
          border-radius: 2px;
          margin-bottom: 12px;
          justify-content: center;
          align-items: center;
          // background: #ededed;
          overflow: hidden;

          img {
            max-width: 200px;
            max-height: 200px;
          }

          .delete {
            display: none;
            position: absolute;
            right: 0;
            top: 0;
            width: 26px;
            height: 26px;
            cursor: pointer;
            background-image: url('/img/pic/index/<EMAIL>');
            background-size: contain;

            &:hover {
              background-image: url('/img/pic/index/<EMAIL>');
            }
          }

          .open-img {
            position: absolute;
            top: 84px;
            left: 58.5px;
            visibility: hidden;

            width: 83px;
            height: 32px;
            background: #389bfd;
            border-radius: 4px;

            font-weight: 400;
            font-size: 14px;
            color: #ffffff;
            line-height: 32px;
            cursor: pointer;
          }

          .img-mask {
            position: absolute;
            width: 200px;
            height: 200px;
            background: rgba(0, 0, 0, 0.5);
            z-index: -1;
          }

          &:hover {
            // background-color: #fbfbfb;

            &::after {
              z-index: 1;
            }
            .delete {
              display: block;
              z-index: 3;
            }
            .img-mask,
            .open-img {
              visibility: visible;
              z-index: 1;
            }
            .open-img {
              z-index: 2;
            }
          }
        }
        .action {
          height: 20px;

          .el-checkbox {
            height: 20px;

            span {
              display: inline-block;
              line-height: 20px;
              color: #666;
            }
          }

          .el-button {
            padding: 2px 0;
            font-size: 14px;
            color: #006eff;

            &:hover,
            &:focus {
              opacity: 0.7;
            }
          }
        }
      }
    }

    .iten-wrap-electron {
      // 计算
      height: calc(100% - 70px - 97px);

      .item {
        .item-content {
          .img-wrap {
            &::before,
            &::after {
              position: absolute;
              content: '';
              top: 0;
              left: 0;
              width: 80px;
              height: 80px;
              background: #000000;
              border-radius: 4px;
              opacity: 0.1;
              z-index: -1;
            }
          }
        }
      }
    }
  }

  .list-group {
    display: flex;
    padding: 50px;

    .list-group-item {
      position: relative;
      width: 100px;
      height: 100px;
      margin-right: 10px;
      overflow: hidden;
      transition: transform 0.3s ease, opacity 0.3s ease;
      img {
        width: 100%;
        height: 100%;
      }
      p {
        text-align: center;
      }
    }

    .list-group-item.dragging {
      opacity: 0.1;
    }
  }
}
