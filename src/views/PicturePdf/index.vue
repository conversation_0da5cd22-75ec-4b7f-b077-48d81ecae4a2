<template>
  <div v-loading="loading" :element-loading-text="' '" class="picturePdf">
    <!-- 图片列表无内容 -->
    <PictureNoPicture :imgExts="imgToPdfExts"></PictureNoPicture>
    <section v-if="imgsMap.size" class="list">
      <!-- 顶部操作栏 -->
      <PictureHeader></PictureHeader>
      <!-- 图片列表 -->
      <draggable
        :list="list"
        :enabled="!enabled"
        item-key="key"
        class="item-wrap-web flex auto-scrollbar" v-scroll-effect
        :class="{
          'iten-wrap-electron': isElectron,
        }"
        style="z-index: 100"
        :move="checkMove"
        @start="dragStart"
        @end="dragEnd"
        @change="dragChange"
      >
        <template #item="{ element, index }">
          <div
            class="item"
            :class="{
              // mr24: true, // 均衡宽度
              // mr32: true,
              mr32: (index + 1) % marginRightZeroIndex,
            }"
          >
            <div class="img-wrap flex chessboard-bg">
              <img
                :style="{
                  transform: `rotate(${element[1].rotate}deg) scaleX(${element[1].scaleX}) scaleY(${element[1].scaleY})`,
                }"
                :src="
                  element[1].record.imgUrl ||
                  '/img/pic/index/<EMAIL>'
                "
                alt=""
              />
              <div class="delete btn" @click="deleteImg(element[1].key)"></div>
              <div v-if="isElectron" class="img-mask"></div>
              <div
                v-if="isElectron"
                class="btn open-img"
                @click="electronOpenFile($event, element[1].key)"
              >
                打开
              </div>
            </div>
            <div class="action flex">
              <el-checkbox class="flex1" v-model="element[1].checked">
                <span class="number">{{ index + 1 }}</span>
              </el-checkbox>
              <el-button type="primary" link @click="edit(element[1])"
                >编辑</el-button
              >
              <el-button
                v-if="index !== 0"
                type="primary"
                link
                @click="updatePartMap({ newIndex: index, oldIndex: index - 1 })"
                >上移</el-button
              >
              <el-button
                v-if="index !== list.length - 1"
                type="primary"
                link
                @click="updatePartMap({ newIndex: index, oldIndex: index + 1 })"
                >下移</el-button
              >
            </div>
          </div>
        </template>
      </draggable>
    </section>
    <!-- 底部操作栏 -->
    <PdfFooter></PdfFooter>
    <!-- web才使用 -->
    <InputHidden accept="image/*"></InputHidden>
    <PdfPictureEdit v-if="showPdfPictureEdit"></PdfPictureEdit>
    <PdfConvertEdit v-if="showPdfConvertEdit"></PdfConvertEdit>
    <ConvertSuccessDialog
      v-if="showConvertSuccessDialog"
      :url="url"
      :showOpen="outputType === 'single'"
    ></ConvertSuccessDialog>
  </div>
</template>

<script lang="ts" setup>
import { onMounted, onUnmounted, ref, computed } from 'vue'
import draggable from 'vuedraggable'
import { useRoute } from 'vue-router'
import { storeToRefs } from 'pinia'
import { usePictureStore } from '@/pinia/picture'
import { usePdfStore } from '@/pinia/pdf'
import { useCommonStore } from '@/pinia/common'
import { messageSimple } from '@/utils/message'
import PdfFooter from '@/components/Picture/PdfFooter.vue'
import PictureHeader from '@/components/Picture/PictureHeader.vue'
import InputHidden from '@/components/Common/InputHidden.vue'
import PictureNoPicture from '@/components/Picture/PictureNoPicture.vue'
import PdfPictureEdit from '@/components/Picture/PdfPictureEditDialog.vue'
import PdfConvertEdit from '@/components/Picture/PdfConvertEditDialog.vue'
import { imgToPdfExts } from '@/utils/enum/configData'

const { proxy } = getCurrentInstance()
const route = useRoute()
const pictureStore = usePictureStore()
const pdfStore = usePdfStore()
const commonStore = useCommonStore()
const isElectron = window.isElectron
let { imgsMap, loading } = storeToRefs(pictureStore)
let {
  showPdfPictureEdit,
  showPdfConvertEdit,
  outputType,
  checkedNums,
  callbackNums,
} = storeToRefs(pdfStore)
let { showConvertSuccessDialog } = storeToRefs(commonStore)

// 初始化
pictureStore.init(route.name)

const electronOpenFile = pictureStore.electronOpenFile

let list = computed(() => {
  getMarginRightZeroIndex()
  return Array.from(imgsMap.value.entries()) // entries 获取到的是可迭代对象，Map本身是不可以迭代的
})

let marginRightZeroIndex = ref(1) // 用于判断marginRight为0的情况
let itemWrapDom: any = null
const getMarginRightZeroIndex = () => {
  itemWrapDom = itemWrapDom || document.querySelector('.item-wrap-web')

  // 固定计算
  if (itemWrapDom?.getBoundingClientRect().width < 934) {
    marginRightZeroIndex.value = 4
  } else {
    marginRightZeroIndex.value = Number.MAX_VALUE
  }

  // 动态计算
  // marginRightZeroIndex.value = Math.ceil(itemWrapDom?.getBoundingClientRect().width / 232) || 1
  // console.log(itemWrapDom?.getBoundingClientRect().width / 232, marginRightZeroIndex.value);
}
onMounted(() => {
  window.addEventListener('resize', getMarginRightZeroIndex)
})
onUnmounted(() => {
  window.removeEventListener('resize', getMarginRightZeroIndex)
})

let enabled = ref(true)
let dragging = ref(false)
const dragStart = (e: any) => {
  // console.log('dragStart', e)
  dragging.value = true
  e.item.classList.add('dragging')
}
const dragEnd = (e: any) => {
  // console.log('dragEnd', e)
  dragging.value = false
  e.item.classList.remove('dragging')

  updatePartMap(e)
}
const dragChange = ({ moved }: any) => {
  // console.log('dragChange', moved)
}
const checkMove = (e: any) => {
  // window.console.log('checkMove', e.draggedContext.futureIndex)
}

/**
 * 交换 newIndex、oldIndex 里面的数据
 * @param {any} {newIndex
 * @param {any} oldIndex}:any
 * @returns {any}
 */
const updatePartMap = ({ newIndex, oldIndex }: any) => {
  const keys: any = [...imgsMap.value.keys()]
  const oldKey: any = keys[oldIndex]
  const newKey: any = keys[newIndex]
  const oldValue = imgsMap.value.get(oldKey)
  const newValue = imgsMap.value.get(newKey)
  if (oldValue && newValue) {
    imgsMap.value.set(oldKey, newValue)
    imgsMap.value.set(newKey, oldValue)
  }
}
const edit = (element: any) => {
  // console.log(element)
  pdfStore.setCurrent(element)
  showPdfPictureEdit.value = true
}

// 删除图片
const deleteImg = (key: any) => {
  proxy.$messageBox
    .confirm('只删除列表记录，不删除文件', '确认删除该图片？', {
      distinguishCancelAndClose: true,
      confirmButtonText: '确定', // 保留
      cancelButtonText: '取消', // 不保留
      customClass: 'customStyleByMessageBox',
      type: 'warning',
      // center: true
    })
    .then((e: any) => {
      if (e === 'confirm') {
        imgsMap.value.delete(key)
        messageSimple.success('删除成功')
      }
    })
    .catch((e: any) => {})
}

let url = ref('')
let loadingProgress = ref(0)
window.ipcRendererApi?.removeAllListeners('pdf-error')
window.ipcRendererApi?.removeAllListeners('pdf-complete')
window.ipcRendererApi?.on('pdf-error', (event: any, res: any) => {
  console.log(res)
  outputType.value === 'multiple' && callbackNums.value++

  loading.value = false
  loadingProgress.value = 0
  messageSimple.error('转换失败，请重试')
})
window.ipcRendererApi?.on('pdf-complete', async (event: any, res: any) => {
  // console.log('pdf-complete', res)

  if (outputType.value === 'multiple') {
    // 多个pdf
    callbackNums.value++
    // console.log('multiple pdf-complete', checkedNums.value, callbackNums.value)

    loadingProgress.value = Math.ceil(
      (callbackNums.value / checkedNums.value) * 100
    ) // 显示进度
    if (checkedNums.value === callbackNums.value) {
      loading.value = false
      loadingProgress.value = 0
      showConvertSuccessDialog.value = true
      messageSimple.success('转换成功')
    }

    console.log(loadingProgress.value)
    // 组件不支持动态，所以通过dom进行操作
    const text: any = document.querySelector('.el-loading-text')
    text && (text.innerHTML = `${loadingProgress.value}%`)
  } else {
    // 单个pdf
    loading.value = false
    loadingProgress.value = 0
    url.value = res
    showConvertSuccessDialog.value = true
    messageSimple.success('转换成功')
  }
})

onUnmounted(() => pictureStore.unBindEvent())
</script>

<style lang="scss" scoped>
.picturePdf {
  min-height: 80vh;

  .el-checkbox__inner {
    width: 17px;
    height: 17px;
  }
  .el-checkbox__inner::after,
  .el-checkbox__input.is-checked .el-checkbox__inner::after {
    top: 2.5px;
    left: 5px;
  }
  .pdf-footer .el-checkbox__inner {
    border-radius: 50%;
  }
  .el-checkbox__input.is-checked .el-checkbox__inner {
    color: #398afa;
    border-color: #398afa;
    background-color: #398afa;
  }
  .el-checkbox__input.is-checked + .el-checkbox__label {
    color: #398afa;
  }
}
</style>
<style lang="scss" scoped>
@import './index.scss';
</style>
