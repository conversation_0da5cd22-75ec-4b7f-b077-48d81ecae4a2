<template>
  <div v-loading="loading" class="picture-edit flex">
    <div class="left-tool-wrap">
      <ToolLeft></ToolLeft>
    </div>
    <div class="canvan-edit-wrap flex1">
      <Canvas v-if="!loading" ref="canvasRef"></Canvas>
    </div>
    <div class="tool-wrap">
      <ToolCanvas v-if="currentType === 'canvas'"></ToolCanvas>
      <ToolText v-if="currentType === 'text'"></ToolText>
      <ToolImg v-if="currentType === 'img'" @updateView="updateView"></ToolImg>
    </div>
  </div>
</template>

<script setup lang="ts">
import ToolLeft from '@/views/WatermarkEdit/components/ToolLeft.vue'
import Canvas from '@/views/WatermarkEdit/components/Canvas.vue'
import ToolCanvas from '@/views/WatermarkEdit/components/ToolCanvas.vue'
import ToolText from '@/views/WatermarkEdit/components/ToolText.vue'
import ToolImg from '@/views/WatermarkEdit/components/ToolImg.vue'

import { ref } from 'vue'
import { storeToRefs } from 'pinia'
import { useWatermarkEditStore } from '@/pinia/watermarkEdit'
const watermarkEditStore = useWatermarkEditStore()
const { list, currentType, canvasRef } = storeToRefs(watermarkEditStore)
list.value = []
let loading = ref(true)
watermarkEditStore.getDetail().finally(() => (loading.value = false))

const updateView = () => {
  canvasRef.value.updateView()
}

// 更新token、user
const loginInfo = JSON.parse(localStorage.loginInfo || '{}')
loginInfo.token && (localStorage.token = loginInfo.token)
loginInfo.user && (localStorage.user = JSON.stringify(loginInfo.user))
</script>

<style lang="scss" scoped>
@import './index.scss';
.picture-edit {
  height: 100%;
}
</style>
