<template>
  <div v-if="canvas" class="canvas-tool">
    <div class="title">画布</div>
    <div class="line"></div>

    <div class="title">大小</div>
    <div class="size flex">
      <span>宽</span>
      <el-input
        v-model="canvas.style.width"
        @change="canvasRef?.updateCanvasRect"
      />
      <span style="padding-right: 0">x</span>
      <span>高</span>
      <el-input
        v-model="canvas.style.height"
        @change="canvasRef?.updateCanvasRect"
      />
    </div>

    <div class="title margin-top10">样式</div>
    <el-color-picker v-model="color" @change="changeColor" />

    <div class="title mt30">位置操作</div>
    <div class="mt10">
      <el-button @click="resetAllPosition">重置所有位置</el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { storeToRefs } from 'pinia'
import { useWatermarkEditStore } from '@/pinia/watermarkEdit'
const { proxy } = getCurrentInstance()
const watermarkEditStore = useWatermarkEditStore()

let { canvas, list, canvasRef } = storeToRefs(watermarkEditStore)
let color = ref(canvas.value.style?.backgroundColor)
if (canvas.value.style?.backgroundColor === 'transparent') color.value = ''

const changeColor = (color: string) => {
  if (color) {
    canvas.value.style.backgroundColor = color
  } else {
    canvas.value.style.backgroundColor = 'transparent'
  }
}
const resetAllPosition = () => {
  proxy?.$messageBox
    .confirm(' ', '确认重置所有位置？', {
      distinguishCancelAndClose: true,
      confirmButtonText: '确定', // 保留
      cancelButtonText: '取消', // 不保留
      customClass: 'customStyleByMessageBox',
      type: 'warning',
      // center: true
    })
    .then((e: any) => {
      if (e === 'confirm') {
        list.value.forEach((item: any) => {
          item.style.left = 0
          item.style.top = 0
        })
        // 更新画布
        canvasRef.value?.updateView()
      }
    })
    .catch((e: any) => {})
}
</script>

<style lang="scss">
@import '../index.scss';

.canvas-tool {
  width: 200px;
  height: 100%;
  padding: 5px;

  .size {
    line-height: 30px;
    span {
      padding: 0 5px;
    }
  }
}
</style>
