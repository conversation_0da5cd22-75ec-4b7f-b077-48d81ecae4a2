<template>
  <div class="img-tool">
    <div class="flex">
      <div class="title flex1">图片</div>
      <el-button type="danger" text @click="deleteItem">删除</el-button>
    </div>
    <div class="line"></div>

    <el-button
      class="mt30"
      type="primary"
      @click="triggerChoose"
      :disabled="!currentItem.edit"
      >重新选择</el-button
    >

    <div class="title mt30">大小</div>
    <div class="slider-action rotate flex">
      <el-slider
        v-model="currentItem.style.zoom"
        :step="0.01"
        :show-tooltip="false"
        :min="0.1"
        :max="2"
        @change="changeZoom"
      />
      <div class="size">{{ (currentItem.style.zoom * 100).toFixed(0) }}%</div>
    </div>

    <div class="title mt30">图层顺序</div>
    <div class="mt10">
      <el-button @click="setTop">置顶</el-button>
      <el-button @click="setBottom">置底</el-button>
    </div>

    <div class="title mt30">位置操作</div>
    <div class="mt10">
      <el-button @click="canvasRef.updateCenter('horizontal')"
        >水平居中</el-button
      >
      <el-button @click="canvasRef.updateCenter('vertical')"
        >垂直居中</el-button
      >
    </div>
    <div style="margin-top: 10px; user-select: none">
      <p>left：{{ currentItem.style.left }}px</p>
      <p>top：{{ currentItem.style.top }}px</p>
    </div>

    <el-checkbox class="can-replace mt30" v-model="currentItem.edit">
      <p>可替换</p>
    </el-checkbox>

    <input
      class="hide"
      type="file"
      name="file"
      id="imgUploadInput"
      accept="image/*"
      @change="chooseImage($event)"
    />
  </div>
</template>

<script setup lang="ts">
import { storeToRefs } from 'pinia'
import { useWatermarkEditStore } from '@/pinia/watermarkEdit'
// import oss from '@/utils/aliOss'
import { messageSimple } from '@/utils/message'
import { uploadImage } from '@/api/watermark'
const { proxy } = getCurrentInstance()
const watermarkEditStore = useWatermarkEditStore()
let { list, canvasRef, currentIndex, currentType } =
  storeToRefs(watermarkEditStore)
let currentItem = list.value[currentIndex.value]

const emits = defineEmits(['updateView'])
const changeZoom = (value: number) => {
  emits('updateView')
}

const deleteItem = async () => {
  proxy?.$messageBox
    .confirm('', '确认删除该图片？', {
      distinguishCancelAndClose: true,
      confirmButtonText: '确定', // 保留
      cancelButtonText: '取消', // 不保留
      customClass: 'customStyleByMessageBox',
      type: 'warning',
      // center: true
    })
    .then((e: any) => {
      if (e === 'confirm') {
        // 删除相关节点
        list.value.splice(currentIndex.value, 1)
        // 指向canvas画布
        currentIndex.value = -1
        currentType.value = 'canvas'
        // 更新画布
        canvasRef.value?.updateView()
      }
    })
    .catch((e: any) => {})
}
const setTop = async () => {
  // 置顶目标节点
  const target = list.value.splice(currentIndex.value, 1)[0]
  list.value.push(target) // 添加到最后一个
  // 选中顶层元素
  const last = list.value.length - 1
  currentIndex.value = last
  currentType.value = list.value[last].type
  // 更新画布
  canvasRef.value?.updateView()
}
const setBottom = async () => {
  // 置底目标节点
  const target = list.value.splice(currentIndex.value, 1)[0]
  list.value.unshift(target) // 添加到第一个
  // 选中底层元素
  const first = 0
  currentIndex.value = first
  currentType.value = list.value[first].type
  // 更新画布
  canvasRef.value?.updateView()
}
const triggerChoose = (e: any) => {
  const choose = document.getElementById('imgUploadInput')
  if (choose) {
    // 解决文件不能重复上传问题
    choose.setAttribute('type', 'text')
    choose.setAttribute('type', 'file')
    choose.click()
  }
}
const chooseImage = (e: any) => {
  upload(e)
}
const upload = (e: any) => {
  // oss.aliOss
  //   ?.ossUploadImage({
  //     file: e.target.files[0],
  //     fileId: JSON.parse(localStorage.user || '{}').id || Date.now(),
  //   })
  //   .then((response: any) => {
  //     const url = response && response.url
  //     // console.log(url)
  //     list.value[currentIndex.value].img = url
  //     canvasRef.value.updateImageSize()
  //   })
  //   .catch((error: any) => {
  //     console.error('图片上传失败', error)
  //     messageSimple.error('图片上传失败', error.message)
  //   })

  uploadImage({
    file: e.target.files[0],
  })
    .then((response: any) => {
      if (response.data.code === 0) {
        list.value[
          currentIndex.value
        ].img = `https://static.mymind365.com/${response.data.content}`
        canvasRef.value.updateImageSize()
      } else {
        messageSimple.error('图片上传失败', response)
      }
    })
    .catch((error: any) => {
      console.error('图片上传异常', error)
      messageSimple.error('图片上传异常', error.message)
    })
}
</script>

<style lang="scss">
@import '../index.scss';
.img-tool {
  width: 200px;
  padding: 5px;

  .slider-action {
    margin-left: 10px;
    height: 20px;
    margin-bottom: 16px;
    user-select: none;

    .size {
      width: 33px;
      margin-left: 12px;
      font-weight: 400;
      font-size: 12px;
      color: #333333;
      height: 32px;
      line-height: 32px;
      text-align: left;
      flex-shrink: 0;
      overflow: hidden;
    }
  }
}
</style>
