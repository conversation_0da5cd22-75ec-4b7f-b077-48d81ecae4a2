<template>
  <div class="edit center flex1" @click="updateCurrentChoose(-1, 'canvas')">
    <!-- dragleave dragend 小于100ms，则添加 -->
    <!-- dragstart e.dataTransfer.setData("Text", e3.target.id) -->
    <!-- e.target.appendChild(document.getElementById(e.dataTransfer.getData("Text")) -->
    <!-- @drop="(e: any) => { console.log('drop', e); }"
    @dragenter="(e: any) => { console.log('dragenter', e) }"
    @dragover="(e: any) => { console.log('dragover', e); e.preventDefault(); }"
    @dragleave="(e: any) => { console.log('dragleave', e) }" -->
    <!-- @wheel="onWheel" -->
    <div
      class="canvas"
      :style="{
        position: 'relative',
        width: (canvas.style?.width || 0) + 'px',
        height: (canvas.style?.height || 0) + 'px',
        backgroundColor: canvas.style?.backgroundColor || 'transparent',
        // zoom: currentZoom,
        // overflow: 'hidden',
      }"
      @click="updateCurrentChoose(-1, 'canvas')"
    >
      <template v-for="(item, index) in list" :key="index">
        <p
          v-if="item.type === 'text'"
          class="text"
          :class="{
            'no-result': !item.text,
            selected: currentIndex === index,
          }"
          :ref="(el: any) => (watermarkRefs[index] = el)"
          :style="{
            width: 'max-content',
            fontFamily: item.style.fontFamily,
            fontSize: `${item.style.fontSize}px`,
            color: item.style.color,
            fontWeight: item.style.fontWeight,
            fontStyle: item.style.fontStyle,
            textDecoration: item.style.textDecoration,
            rotate: `${item.style.rotate}deg`,
            left: `${draggables[index]?.position?.x - canvasRect?.left}px`,
            top: `${draggables[index]?.position?.y - canvasRect?.top}px`,
            zIndex: index,
          }"
          :index="index"
          @click.stop="updateCurrentChoose(index, item.type)"
        >
          {{ item.text }}
        </p>
        <img
          v-else-if="item.type === 'img'"
          class="img"
          :class="{
            selected: currentIndex === index,
          }"
          :ref="(el: any) => (watermarkRefs[index] = el)"
          :style="{
            width: `${item.style.width * item.style.zoom}px`,
            height: `${item.style.height * item.style.zoom}px`,
            left: `${draggables[index]?.position?.x - canvasRect?.left}px`,
            top: `${draggables[index]?.position?.y - canvasRect?.top}px`,
            zIndex: index,
          }"
          :index="index"
          :src="item.img"
          @click.stop="updateCurrentChoose(index, item.type)"
        />
      </template>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted, nextTick } from 'vue'
import { useDraggable } from '@vueuse/core'
import { storeToRefs } from 'pinia'
import { useWatermarkEditStore } from '@/pinia/watermarkEdit'

const watermarkEditStore = useWatermarkEditStore()
let { canvas, list, currentIndex, currentType } =
  storeToRefs(watermarkEditStore)

let canvasDom: any = null // canvas dom
let canvasRect: any = ref(null) // canvas dom 矩形信息
let draggables: any = reactive([]) // canvas dom 矩形信息
let watermarkRefs: any = reactive([]) // canvas dom 矩形信息

/**
 * 根据list建立：watermarkRefs、draggables
 */
const updateView = async () => {
  canvasDom = canvasDom || document.querySelector('.edit .canvas')
  canvasRect.value = canvasDom?.getBoundingClientRect()

  // 1、重置：draggables、watermarkRefs
  // 方法一
  // draggables = reactive([])
  // watermarkRefs = reactive([])
  // console.log(111, draggables, watermarkRefs)
  // 方法二
  draggables.length = 0
  watermarkRefs.length = 0
  // console.log(222, draggables, watermarkRefs)
  // 方法三
  // draggables.splice(0, draggables.length)
  // watermarkRefs.splice(0, watermarkRefs.length)
  // console.log(333, draggables, watermarkRefs)

  // 2、更新重置后的dom信息
  await nextTick()

  // 3、生成 draggables、watermarkRefs
  list.value.forEach((item: any, index: number) => {
    const ref = watermarkRefs[index]

    draggables[index] = useDraggable(ref, {
      onEnd() {
        // currentIndex.value 更新有延迟，用setTimeout异步处理
        setTimeout(() => updateCurrentPosition(), 0)
      },
      initialValue: {
        x: canvasRect.value.left + item.style.left,
        y: canvasRect.value.top + item.style.top,
      },
    })
  })
  // console.log(
  //   '444 list watermarkRefs draggables',
  //   list.value,
  //   watermarkRefs,
  //   draggables
  // )
}
const updateImageSize = () => {
  // 获取img元素
  const imgs: any = document.querySelectorAll('.canvas .img')

  Array.from(imgs).forEach((img: any) => {
    // console.log(img)
    const index = img.getAttribute('index')

    // 等待图片加载完成
    img.onload = function () {
      // 访问图片的原始宽高
      const originalWidth = img.naturalWidth
      const originalHeight = img.naturalHeight

      list.value[index].style.width = originalWidth
      list.value[index].style.height = originalHeight

      // console.log('onload 原始宽度:', originalWidth)
      // console.log('onload 原始高度:', originalHeight)
      // console.log('onload', list.value[index].style)
    }

    // 如果图片已经缓存，onload可能不会被触发，此时可以直接获取
    if (img.complete) {
      const originalWidth = img.naturalWidth
      const originalHeight = img.naturalHeight

      list.value[index].style.width = originalWidth
      list.value[index].style.height = originalHeight

      // console.log('complete 原始宽度:', originalWidth)
      // console.log('complete 原始高度:', originalHeight)
      // console.log('complete', list.value[index].style)
    }
  })
}
const updateCurrentChoose = async (index: number, type: string) => {
  if (index === currentIndex.value) return

  currentType.value = ''
  await nextTick()
  currentIndex.value = index
  currentType.value = type
}
const updateCanvasRect = () => {
  canvasDom = canvasDom || document.querySelector('.edit .canvas')
  canvasRect.value = canvasDom?.getBoundingClientRect()
}
const updateCurrentPosition = () => {
  if (currentIndex.value < 0) return

  const index = currentIndex.value
  const draggable: any = draggables[index]
  const actualLeft: any = draggable.x - canvasRect.value.left
  const actualTop: any = draggable.y - canvasRect.value.top
  list.value[index].style.left = parseInt(actualLeft)
  list.value[index].style.top = parseInt(actualTop)
  // console.log('updateCurrentPosition', actualLeft, actualTop)
}
const updateCenter = (direction: 'vertical' | 'horizontal') => {
  if (currentIndex.value < 0) return

  const index = currentIndex.value
  const draggable: any = draggables[index]

  // 获取目标元素的宽高，left、top
  const { width, height } = watermarkRefs[index].getBoundingClientRect()
  const left = (canvasRect.value.width - parseInt(width)) / 2
  const top = (canvasRect.value.height - parseInt(height)) / 2

  if (direction === 'vertical') {
    // 居中，更新数据中的位置
    list.value[index].style.top = top
    // 居中，更新页面中的位置
    draggable.y = canvasRect.value.top + top
  }
  if (direction === 'horizontal') {
    // 居中，更新数据中的位置
    list.value[index].style.left = left
    // 居中，更新页面中的位置
    draggable.x = canvasRect.value.left + left
  }

  // console.log(
  //   'updateCenter',
  //   direction,
  //   parseInt(width),
  //   parseInt(height),
  //   left,
  //   top,
  //   canvasRect.value
  // )
}
const keyupHandler = (e: KeyboardEvent) => {
  e.stopPropagation()

  if (currentIndex.value < 0) return
  if (['ArrowLeft', 'ArrowUp', 'ArrowRight', 'ArrowDown'].includes(e.code)) {
    const draggable = draggables[currentIndex.value]

    switch (e.code) {
      case 'ArrowLeft':
        draggable.x--
        updateCurrentPosition()
        break
      case 'ArrowUp':
        draggable.y--
        updateCurrentPosition()
        break
      case 'ArrowRight':
        draggable.x++
        updateCurrentPosition()
        break
      case 'ArrowDown':
        draggable.y++
        updateCurrentPosition()
        break
      default:
        break
    }
    // console.log('keyupHandler draggable', currentIndex.value, JSON.stringify(draggable))
  }
}
const resizeHandler = (e: UIEvent) => {
  updateView()
}

// const currentZoom = ref(1)
// function onWheel(e: WheelEvent) {
//   e.preventDefault()
//   if (e.deltaY < 0) {
//     currentZoom.value *= 1.1
//   } else {
//     currentZoom.value /= 1.1
//   }
//   currentZoom.value = Math.max(1, currentZoom.value)
// }

onMounted(() => {
  updateView()
  updateImageSize()
  window.addEventListener('keyup', keyupHandler)
  window.addEventListener('resize', resizeHandler)
})
onUnmounted(() => {
  window.removeEventListener('keyup', keyupHandler)
  window.removeEventListener('resize', resizeHandler)
})

defineExpose({
  updateView,
  updateCanvasRect,
  updateImageSize,
  updateCenter,
})
</script>

<style lang="scss">
// @import "@/styles/index.scss";
// .active {
//   @extend.common-dialog;
//   @include common-shadow;
// }

@import '../index.scss';
.edit {
  height: 100%;
  padding: 20px 10px;
  text-align: center;
  background-color: #999;
  overflow: hidden;

  .canvas {
    // box-shadow: 0 0 0 1px rgba(0,0,0,.1); // gray
    box-shadow: 0 0 0 1px red;

    .text,
    .img {
      position: absolute;
      user-select: none;
      cursor: move;
      -webkit-user-drag: none;
    }

    .no-result {
      min-height: 20px;
      min-width: 20px;
      border: 1px dotted #cccc;
      box-sizing: border-box;
    }

    .selected {
      box-shadow: 0 0 0 1px yellow;
    }
  }
}
</style>
