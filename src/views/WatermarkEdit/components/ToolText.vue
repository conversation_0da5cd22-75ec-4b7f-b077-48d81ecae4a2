<template>
  <div class="text-tool">
    <div class="flex">
      <div class="title flex1">文本</div>
      <el-button type="danger" text @click="deleteItem">删除</el-button>
    </div>
    <div class="line"></div>
    <!-- <textarea
      ref="textarea"
      v-model="input"
      class="resize-none mt30"
      :class="{
        disabled: !currentItem.edit,
      }"
      style="min-height: 30px; resize: none"
      placeholder="请输入文字"
      :disabled="!currentItem.edit"
      @keyup="currentItem.text = input"
    /> -->
    <el-input
      v-model="input"
      placeholder="请输入文字"
      class="resize-none mt30"
      :class="{
        disabled: !currentItem.edit,
      }"
      :disabled="!currentItem.edit"
      :maxlength="20"
      @keyup="currentItem.text = input"
    >
    </el-input>

    <div class="title mt30">样式</div>
    <div class="mt10 flex rowItem">
      <el-select
        size="mini"
        style="width: 138px; margin-right: 8px"
        v-model="currentItem.style.fontFamily"
        placeholder=""
      >
        <el-option
          v-for="item in fontFamilyList"
          :key="item.value"
          :label="item.name"
          :value="item.value"
          :style="{ fontFamily: item.value }"
        >
        </el-option>
      </el-select>
      <el-input-number
        class="number"
        style="width: 60px"
        v-model="currentItem.style.fontSize"
        :min="10"
        :max="500"
        :controls="false"
      ></el-input-number>
    </div>
    <div class="btnGroup flex mt10">
      <div
        class="styleBtn"
        :class="{ actived: currentItem.style.fontWeight === 'bold' }"
        @click="
          currentItem.style.fontWeight = currentItem.style.fontWeight
            ? ''
            : 'bold'
        "
      >
        <img src=" /img/mind_map/icon/<EMAIL>" alt="" />
      </div>
      <div
        class="styleBtn i"
        :class="{ actived: currentItem.style.fontStyle === 'italic' }"
        @click="
          currentItem.style.fontStyle = currentItem.style.fontStyle
            ? ''
            : 'italic'
        "
      >
        <img src=" /img/mind_map/icon/<EMAIL>" alt="" />
      </div>
      <div
        class="styleBtn u"
        :class="{ actived: currentItem.style.textDecoration === 'underline' }"
        :style="{
          textDecoration: 'underline',
        }"
        @click="
          currentItem.style.textDecoration =
            currentItem.style.textDecoration === 'underline' ? '' : 'underline'
        "
      >
        <img src=" /img/mind_map/icon/<EMAIL>" alt="" />
      </div>
      <div
        class="styleBtn s"
        :class="{
          actived: currentItem.style.textDecoration === 'line-through',
        }"
        :style="{
          textDecoration: 'line-through',
          marginRight: '8px',
        }"
        @click="
          currentItem.style.textDecoration =
            currentItem.style.textDecoration === 'line-through'
              ? ''
              : 'line-through'
        "
      >
        <img src=" /img/mind_map/icon/<EMAIL>" alt="" />
      </div>
      <el-color-picker v-model="color" @change="changeColor" />
    </div>
    <div class="title mt30">旋转</div>
    <div class="slider-action rotate flex">
      <el-slider
        v-model="currentItem.style.rotate"
        :show-tooltip="false"
        :max="360"
      />
      <div class="size">{{ currentItem.style.rotate?.toFixed(0) || 0 }}°</div>
    </div>
    <div class="title mt30">图层顺序</div>
    <div class="mt10">
      <el-button @click="setTop">置顶</el-button>
      <el-button @click="setBottom">置底</el-button>
    </div>

    <div class="title mt30">位置操作</div>
    <div class="mt10">
      <el-button @click="canvasRef.updateCenter('horizontal')"
        >水平居中</el-button
      >
      <el-button @click="canvasRef.updateCenter('vertical')"
        >垂直居中</el-button
      >
    </div>
    <div style="margin-top: 10px; user-select: none">
      <p>left：{{ currentItem.style.left }}px</p>
      <p>top：{{ currentItem.style.top }}px</p>
    </div>

    <el-checkbox class="can-replace mt30" v-model="currentItem.edit">
      <p>可替换</p>
    </el-checkbox>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
// import { useTextareaAutosize } from '@vueuse/core'
import { storeToRefs } from 'pinia'
import { useWatermarkEditStore } from '@/pinia/watermarkEdit'
import { fontFamilyList } from '@/utils/enum'
const { proxy } = getCurrentInstance()
const watermarkEditStore = useWatermarkEditStore()
let { list, canvasRef, currentIndex, currentType } =
  storeToRefs(watermarkEditStore)
let currentItem = list.value[currentIndex.value]
let color = ref(currentItem.style.color)

const deleteItem = async () => {
  proxy?.$messageBox
    .confirm(' ', '确认删除该文本？', {
      distinguishCancelAndClose: true,
      confirmButtonText: '确定', // 保留
      cancelButtonText: '取消', // 不保留
      customClass: 'customStyleByMessageBox',
      type: 'warning',
      // center: true
    })
    .then((e: any) => {
      if (e === 'confirm') {
        // 删除相关节点
        list.value.splice(currentIndex.value, 1)
        // 指向canvas画布
        currentIndex.value = -1
        currentType.value = 'canvas'
        // 更新画布
        canvasRef.value?.updateView()
      }
    })
    .catch((e: any) => {})
}
const setTop = async () => {
  // 置顶目标节点
  const target = list.value.splice(currentIndex.value, 1)[0]
  list.value.push(target) // 添加到最后一个
  // 选中顶层元素
  const last = list.value.length - 1
  currentIndex.value = last
  currentType.value = list.value[last].type
  // 更新画布
  canvasRef.value?.updateView()
}
const setBottom = async () => {
  // 置底目标节点
  const target = list.value.splice(currentIndex.value, 1)[0]
  list.value.unshift(target) // 添加到第一个
  // 选中底层元素
  const first = 0
  currentIndex.value = first
  currentType.value = list.value[first].type
  // 更新画布
  canvasRef.value?.updateView()
}
const changeColor = (color: string) => {
  if (color) {
    currentItem.style.color = color
  } else {
    currentItem.style.color = 'transparent'
  }
}
// let { textarea, input } = useTextareaAutosize()
// input.value = currentItem.text
let input = ref('')
input.value = currentItem.text
</script>

<style lang="scss">
@import '../index.scss';
.text-tool {
  width: 200px;
  height: 100%;
  padding: 5px;
  overflow-y: auto;

  // textarea {
  //   display: block;
  //   min-width: 100px;
  //   max-width: 190px;
  //   font-size: 14px;
  //   padding: 5px;
  //   border-radius: 5px;
  //   outline: none;
  //   border: 1px solid #ccc;
  //   background: #fff;
  //   color: #333;
  //   transition: background-color 0.5s;
  // }

  .btnGroup {
    display: flex;
    width: 100%;

    .styleBtn {
      position: relative;
      width: 27px;
      height: 27px;
      margin-right: 10px;
      border-radius: 4px;
      background-color: #fff;
      border: 1px solid #dce2ed;
      display: flex;
      justify-content: center;
      align-items: center;
      font-weight: bold;
      flex-shrink: 0;
      cursor: pointer;

      img {
        width: 12px;
        height: 14px;
      }

      &.actived {
        background: rgba(220, 226, 237, 0.5);
      }

      &.i {
        font-style: italic;
      }
    }

    .el-color-picker {
      .el-color-picker__trigger {
        width: 27px !important;
        height: 27px !important;
      }
    }
  }

  .slider-action {
    margin-left: 10px;
    height: 20px;
    margin-bottom: 16px;
    user-select: none;

    .size {
      width: 33px;
      margin-left: 12px;
      font-weight: 400;
      font-size: 12px;
      color: #333333;
      height: 32px;
      line-height: 32px;
      text-align: left;
      flex-shrink: 0;
      overflow: hidden;
    }
  }
}
</style>
