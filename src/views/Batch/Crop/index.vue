<template>
  <div
    v-loading="loading"
    :element-loading-text="''"
    class="Crop"
    :style="{
      backgroundColor: imgsMap.size ? '#f6f6f6' : '#fff',
    }"
  >
    <!-- 图片列表无内容 -->
    <NoPicture
      :imgExts="imgWatermarkExts"
      :imgsMap="imgsMap"
      :callback="batchStore.imageInfoHandler"
    />
    <section v-if="imgsMap.size" class="detail">
      <BatchHeader :currentStore="batchStore" />
      <!-- <div
        class="cropper-box chessboard-bg"
        :style="{
          position: 'absolute',
          zIndex: loading ? 100 : -1,
        }"
      ></div> -->
      <div class="cropper-box chessboard-bg center">
        <img
          ref="cropperRef"
          :src="imgUrl"
          @load="initCropper"
          @cropend="batchStore.updateCropBoxData"
        />
      </div>
      <BatchSwiper :imgsMap="imgsMap" :currentStore="batchStore" />
      <CropDrawer />
    </section>
    <!-- web才使用 -->
    <InputHidden
      :accept="getWebExtsByName('imgWatermarkExts')"
      :chooseWebImages="batchStore.chooseWebImages"
    />
    <ExportDirectoryDialog />
    <ConvertSuccessDialog v-if="showConvertSuccessDialog" :showOpen="false" />
  </div>
</template>

<script lang="ts" setup>
import { onMounted, onUnmounted, ref, watch } from 'vue'
import { useRoute } from 'vue-router'
import { storeToRefs } from 'pinia'
import { useCommonStore } from '@/pinia/common'
import { useBatchCropStore } from '@/pinia/batchCrop'
import { imgWatermarkExts, getWebExtsByName } from '@/utils/enum/configData'
// import { messageSimple } from '@/utils/message'

// 1.0: https://github.com/fengyuanchen/cropperjs/blob/v1/README.md
// 1.0: demo: https://fengyuanchen.github.io/cropperjs/v1/
// 2.0: https://fengyuanchen.github.io/cropperjs/playground.html
import Cropper from 'cropperjs'
import 'cropperjs/dist/cropper.css'

const route = useRoute()
const commonStore = useCommonStore()
const batchStore = useBatchCropStore()

// 解构出需要的状态
let {
  imgsMap,
  currentIndex,
  currentImg,
  loading,
  option,
  cropper,
  cropperRef,
} = storeToRefs(batchStore)
let { showConvertSuccessDialog } = storeToRefs(commonStore)
let imgUrl = ref('')

// 初始化
batchStore.init(route.name)
batchStore.batchSaveCallback()

// 监听 currentIndex 变化
watch(
  () => [currentIndex.value],
  async () => {
    // console.log("currentIndexChange:", currentIndex.value);
    batchStore.getCurrent()
  }
)

// 监听 currentImg 变化
watch(
  () => [currentImg.value],
  async () => {
    // 销毁旧的裁剪器实例
    if (cropper.value) {
      cropper.value.destroy()
      cropper.value = null
    }
    if (currentImg.value) {
      // 释放之前的 URL
      URL.revokeObjectURL(imgUrl.value)
      imgUrl.value = URL.createObjectURL(currentImg.value.file)
    }
  }
)

// 初始化裁剪器
const initCropper = () => {
  // console.log('initCropper');
  if (cropperRef.value) {
    try {
      cropper.value = new Cropper(cropperRef.value, {
        dragMode: option.value.dragMode as any,
        viewMode: option.value.viewMode as any,
        guides: option.value.guides, // 是否显示网格线
        movable: option.value.movable, // 图片是否可移动
        zoomable: option.value.zoomable, // 是否可以缩放图片（以图片左上角为原点进行缩放）
        autoCrop: option.value.autoCrop, // 是否自动裁剪
        cropBoxMovable: option.value.cropBoxMovable, // 裁剪框是否可移动
        cropBoxResizable: !option.value.cropBoxResizable, // 裁剪框是否可调整大小
        minCropBoxWidth: option.value.minCropBoxWidth, // 裁剪框最小宽度
        minCropBoxHeight: option.value.minCropBoxHeight, // 裁剪框最小高度

        ready() {
          if (!cropper.value) return
          // console.log(111, currentImg.value?.record)
          if (currentImg.value?.record?.aspectRatio) {
            // console.log(222)
            cropper.value.setAspectRatio(currentImg.value?.record?.aspectRatio)
          }
          if (currentImg.value?.record?.left) {
            // console.log(333)
            cropper.value.setCropBoxData({
              left: currentImg.value?.record?.left || 0,
              top: currentImg.value?.record?.top || 0,
              width: currentImg.value?.record?.autoCropWidth || undefined,
              height: currentImg.value?.record?.autoCropHeight || undefined,
            })
          }
          cropper.value.crop() // 手动触发裁剪框显示
          // console.log(`Cropper 初始化完成`, cropper.value);
          batchStore.updateCropBoxData()
        },
        cropend() {
          console.log('cropend')
          if (!currentImg.value.record.aspectRatio && currentImg.value.record.name !== '自由') {
            currentImg.value.record.name = '自由'
          }
        },
      })
    } catch (error) {
      console.error('初始化裁剪器失败:', error)
    }
  }
}

// // 处理窗口大小变化
// const handleResize = () => {
//   // console.log("handleResize");
//   try {
//     cropper.value?.reset(); // 重新加载裁剪框
//   } catch (error) {
//     console.error("重新加载裁剪框失败:", error);
//   }
// };

onMounted(() => {
  // window.addEventListener("resize", handleResize);
  if (!imgUrl.value && currentImg.value?.file) {
    imgUrl.value = URL.createObjectURL(currentImg.value.file)
  }
})

onUnmounted(() => {
  batchStore.unBindEvent()
  // window.removeEventListener("resize", handleResize);

  if (imgUrl.value) {
    URL.revokeObjectURL(imgUrl.value)
  }
})
</script>

<style lang="scss" scoped>
@import './index.scss';
</style>
