<template>
  <div
    v-loading="loading"
    :element-loading-text="' '"
    class="Rotate"
    :style="{
      backgroundColor: imgsMap.size ? '#f6f6f6' : '#fff',
    }"
  >
    <!-- 图片列表无内容 -->
    <NoPicture
      :imgExts="imgWatermarkExts"
      :imgsMap="imgsMap"
      :callback="batchStore.imageInfoHandler"
    ></NoPicture>
    <section v-if="imgsMap.size" class="detail">
      <BatchHeader :currentStore="batchStore"></BatchHeader>
      <div class="img-box center chessboard-bg">
        <img
          :style="{
            transform: `rotate(${currentImg.record.rotate}deg) scaleX(${currentImg.record.scaleX}) scaleY(${currentImg.record.scaleY})`,
            // transition: 'transform 0.3s ease'
          }"
          :src="imgUrl"
        />
      </div>
      <BatchSwiper :imgsMap="imgsMap" :currentStore="batchStore"></BatchSwiper>
      <el-drawer
        v-model="drawer"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        :with-header="false"
      >
        <div class="title flex">
          <div class="left flex1">批量旋转</div>
          <div class="right reset btn flex" @click="reset">
            <img src="/img/pic/second2/<EMAIL>" alt="" class="icon" />
            <span>重置</span>
          </div>
        </div>

        <div class="action btns flex mt16 mb16">
          <div class="item btn flex" @click="updateRotate(-90)">
            <img src="/img/pic/second2/<EMAIL>" alt="" />
            <div class="tip">逆时针90°</div>
          </div>
          <div class="item btn flex" @click="updateRotate(90)">
            <img src="/img/pic/second2/<EMAIL>" alt="" />
            <div class="tip">顺时针90°</div>
          </div>
        </div>

        <div class="rotate-slider flex">
          <div class="tip">旋转</div>
          <el-slider
            v-model="currentImg.record.rotate"
            :show-tooltip="false"
            :min="0"
            :max="360"
          />
          <div class="size">
            {{ Number(currentImg.record.rotate).toFixed(0) }}°
          </div>
        </div>

        <div class="action btns flex mt24 mb40">
          <div
            class="item btn flex"
            @click="currentImg.record.scaleX = -currentImg.record.scaleX"
          >
            <img src="/img/pic/second2/<EMAIL>" alt="" />
            <div class="tip">上下翻转</div>
          </div>
          <div
            class="item btn flex"
            @click="currentImg.record.scaleY = -currentImg.record.scaleY"
          >
            <img src="/img/pic/second2/<EMAIL>" alt="" />
            <div class="tip">左右翻转</div>
          </div>
        </div>

        <el-button type="primary" class="btn mt28 flex" @click="handler">
          <img src="/img/pic/second2/<EMAIL>" class="icon" />
          <div>应用到全部</div>
        </el-button>
      </el-drawer>
    </section>
    <!-- web才使用 -->
    <InputHidden
      :accept="getWebExtsByName('imgWatermarkExts')"
      :chooseWebImages="batchStore.chooseWebImages"
    ></InputHidden>
    <ExportDirectoryDialog></ExportDirectoryDialog>
    <ConvertSuccessDialog
      v-if="showConvertSuccessDialog"
      :showOpen="false"
    ></ConvertSuccessDialog>
  </div>
</template>

<script lang="ts" setup>
import { onMounted, onUnmounted, ref, watch } from 'vue'
import { useRoute } from 'vue-router'
import { storeToRefs } from 'pinia'
import { useCommonStore } from '@/pinia/common'
import { useBatchRotateStore } from '@/pinia/batchRotate'
import { imgWatermarkExts, getWebExtsByName } from '@/utils/enum/configData'
import BatchSwiper from '@/components/Batch/BatchSwiper.vue'
import { messageSimple } from '@/utils/message'

const route = useRoute()
const commonStore = useCommonStore()
const batchStore = useBatchRotateStore()
const drawer = ref(true)
let { imgsMap, currentIndex, currentImg, applyToAll, loading } = storeToRefs(batchStore)
let { showConvertSuccessDialog } = storeToRefs(commonStore)
let imgUrl = ref('')

// 初始化
batchStore.init(route.name)
batchStore.batchSaveCallback()

onMounted(() => {
  if (!imgUrl.value && currentImg.value?.file) {
    imgUrl.value = URL.createObjectURL(currentImg.value.file)
  }
})
onUnmounted(() => {
  batchStore.unBindEvent()

  if (imgUrl.value) {
    URL.revokeObjectURL(imgUrl.value)
  }
})

watch(
  () => [currentIndex.value],
  () => {
    // newValue, oldValue
    // console.log(newValue, oldValue)
    batchStore.getCurrent()
  }
)
watch(
  () => [currentImg.value],
  () => {
    URL.revokeObjectURL(imgUrl.value)
    if (currentImg.value) {
      URL.revokeObjectURL(imgUrl.value)
      imgUrl.value = URL.createObjectURL(currentImg.value.file)
    }
  }
)

const updateRotate = (size: any) => {
  currentImg.value.record.rotate += size
  if (currentImg.value.record.rotate > 360) {
    currentImg.value.record.rotate -= 360
  }
  if (currentImg.value.record.rotate < 0) {
    currentImg.value.record.rotate += 360
  }
}

const reset = () => {
  currentImg.value.record.scaleX = 1
  currentImg.value.record.scaleY = 1
  currentImg.value.record.rotate = 0
}

const handler = () => {
  applyToAll.value = true
  imgsMap.value.forEach((img: any) => {
    img.record.scaleX = currentImg.value.record.scaleX
    img.record.scaleY = currentImg.value.record.scaleY
    img.record.rotate = currentImg.value.record.rotate
  })
  messageSimple.success('应用成功')
}
</script>

<style lang="scss" scoped>
@import './index.scss';
</style>
