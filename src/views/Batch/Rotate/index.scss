// @import '@/styles/btn.scss';
.Rotate {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  height: 100%;
  background-color: #f6f6f6;

  @keyframes spin {
    0% {
      transform: rotate(0deg);
    } /* 动画开始时，图片旋转0度 */
    100% {
      transform: rotate(360deg);
    } /* 动画结束时，图片旋转360度 */
  }
  :deep(.el-overlay) {
    left: auto;
    top: 48px;
    width: 289px;
    background: transparent !important;
    .el-drawer {
      width: 289px !important;
      height: calc(100vh - 48px);
      box-shadow: none;
    }
  }

  section {
    -webkit-box-flex: 1;
    -ms-flex: 1;
    flex: 1;
    height: 100%;
  }

  .detail {
    .img-box {
      margin: 0 47px;
      // width: 560px;
      // height: 470px;
      width: calc(100% - 289px - 94px);
      height: calc(100% - 202px);
      background-color: transparent;
      box-shadow: 0px 0px 20px 0px rgba(0, 0, 0, 0.17);
      overflow: hidden;

      img {
        max-width: 100%;
        max-height: 100%;
        -webkit-app-region: no-drag;
      }
    }
    :deep(.el-overlay) {
      z-index: 1 !important;
      .el-drawer {
        .el-drawer__body {
          padding: 19px 20px;
          .title {
            font-weight: 600;
            font-size: 14px;
            color: #000000;
            line-height: 20px;

            .right {
              font-weight: 400;
              font-size: 14px;
              color: #999999;
              line-height: 20px;

              .width,
              .height {
                width: 60px;
              }
              .height {
                margin-left: 10px;
              }
            }
            .reset {
              font-weight: 400;
              font-size: 12px;
              color: #1890ff;
              line-height: 20px;
              cursor: pointer;

              .icon {
                width: 20px;
                height: 20px;
              }

              span {
                line-height: 20px;
              }
            }
          }

          .action {
            .item {
              margin-right: 40px;
              cursor: pointer;
              &:last-child {
                margin-right: 0;
              }

              img {
                width: 18px;
                height: 18px;
              }

              .tip {
                margin-left: 4px;
                font-weight: 400;
                font-size: 14px;
                color: #666666;
                line-height: 20px;
              }
            }
          }

          .rotate-slider {
            min-width: 173px;
            height: 20px;
            margin-top: calc(16px - 7px);
            user-select: none;

            .tip {
              width: 40px;
              height: 20px;
              flex-shrink: 0;

              font-weight: 400;
              font-size: 14px;
              color: #999999;
              line-height: 20px;
            }

            .size {
              width: 32px;
              font-weight: 400;
              font-size: 12px;
              color: #333333;
              line-height: 20px;
              padding-left: 12px;
              // text-align: right;
              flex-shrink: 0;
            }

            .el-slider {
              height: 20px;
              width: 173px;

              .el-slider__runway {
                height: 3px;
                border-radius: 3px;
                background-color: #e4e4e6;

                .el-slider__bar {
                  height: 3px !important;
                  border-radius: 3px;
                }

                .el-slider__button {
                  position: relative;
                  width: 9px;
                  height: 9px;
                  top: -1.5px;
                  border: #1890ff;
                  background: #1890ff;
                }
              }
              .is-disabled {
                .el-slider__button {
                  background: #e4e4e6;
                }
              }
            }
          }

          .el-button {
            width: 249px;
            height: 36px;
            background: #389bfd;
            border: 0;
            border-radius: 6px;

            font-weight: 400;
            font-size: 14px;
            color: #ffffff;
            line-height: 20px;
            text-align: center;
            font-style: normal;

            img {
              width: 16px;
              height: 16px;
              margin-right: 6px;
            }
          }
        }
      }
    }
  }
}
