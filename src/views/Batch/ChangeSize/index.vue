<template>
  <div
    v-loading="loading"
    :element-loading-text="' '"
    class="ChangeSize"
    :style="{
      backgroundColor: imgsMap.size ? '#f6f6f6' : '#fff',
    }"
  >
    <!-- 图片列表无内容 -->
    <NoPicture
      :imgExts="imgWatermarkExts"
      :imgsMap="imgsMap"
      :callback="batchStore.imageInfoHandler"
    ></NoPicture>
    <section v-if="imgsMap.size" class="detail">
      <BatchHeader :currentStore="batchStore"></BatchHeader>
      <div class="img-box center chessboard-bg">
        <img :src="imgUrl" alt="" />
      </div>
      <BatchSwiper :imgsMap="imgsMap" :currentStore="batchStore"></BatchSwiper>
      <el-drawer
        v-model="drawer"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        :with-header="false"
      >
        <div class="title flex">
          <div class="left flex1">原图尺寸</div>
          <div class="right flex">
            <div class="width">宽：{{ currentImg?.record?.width }}</div>
            <div class="height">高：{{ currentImg?.record?.height }}</div>
          </div>
        </div>
        <div class="title flex mt24">
          <div class="left flex1">导出尺寸</div>
          <div class="right reset btn flex" @click="reset">
            <img src="/img/pic/second2/<EMAIL>" alt="" class="icon" />
            <span>重置</span>
          </div>
        </div>

        <el-checkbox
          class="custom-size flex mt13"
          v-model="isCustomSize"
          @change="sizeTypeChange('customSize')"
        >
          <span>指定尺寸</span>
        </el-checkbox>
        <div
          class="custom-size-input-box flex"
          :style="{
            opacity: isCustomSize ? 1 : 0.5,
          }"
        >
          <el-input
            type="text"
            :min="1"
            :max="99999"
            :minlength="1"
            :maxlength="5"
            :disabled="currentImg?.record?.sizeType === 'scaleSize'"
            oninput="value=value.replace(/[^0-9]/g,'')"
            v-model="currentImg.record.customWidth"
            placeholder="请输入宽"
          >
            <template #prefix> <div class="tip">宽</div> </template>
          </el-input>
          <img src="/img/pic/second2/<EMAIL>" alt="" class="link" />
          <el-input
            type="text"
            :min="1"
            :max="99999"
            :minlength="1"
            :maxlength="5"
            :disabled="currentImg?.record?.sizeType === 'scaleSize'"
            oninput="value=value.replace(/[^0-9]/g,'')"
            v-model="currentImg.record.customHeight"
            placeholder="请输入高"
          >
            <template #prefix> <div class="tip">高</div> </template>
          </el-input>
        </div>

        <el-checkbox
          class="scale-size flex mt17"
          v-model="isScaleSize"
          @change="sizeTypeChange('scaleSize')"
        >
          <span>等比缩放尺寸</span>
        </el-checkbox>
        <div
          class="scale-size-box"
          :style="{
            opacity: isScaleSize ? 1 : 0.5,
          }"
        >
          <div class="action scale flex">
            <div class="tip">缩放</div>
            <el-slider
              v-model="currentImg.record.scale"
              :show-tooltip="false"
              :min="10"
              :max="400"
              :disabled="currentImg?.record?.sizeType === 'customSize'"
            />
            <div class="size">{{ currentImg?.record?.scale }}%</div>
          </div>
          <div class="action scale-info flex mt12">
            <div class="tip">尺寸</div>
            <div class="size">
              {{
                (
                  (currentImg?.record?.width * currentImg?.record?.scale) /
                  100
                ).toFixed(0)
              }}*{{
                (
                  (currentImg?.record?.height * currentImg?.record?.scale) /
                  100
                ).toFixed(0)
              }}
            </div>
          </div>
        </div>

        <el-button type="primary" class="btn mt28 flex" @click="handler">
          <img src="/img/pic/second2/<EMAIL>" class="icon" />
          <div>应用到全部</div>
        </el-button>
      </el-drawer>
    </section>
    <!-- web才使用 -->
    <InputHidden
      :accept="getWebExtsByName('imgWatermarkExts')"
      :chooseWebImages="batchStore.chooseWebImages"
    ></InputHidden>
    <ExportDirectoryDialog></ExportDirectoryDialog>
    <ConvertSuccessDialog
      v-if="showConvertSuccessDialog"
      :showOpen="false"
    ></ConvertSuccessDialog>
  </div>
</template>

<script lang="ts" setup>
import { onMounted, onUnmounted, ref, watch } from 'vue'
import { useRoute } from 'vue-router'
import { storeToRefs } from 'pinia'
import { useCommonStore } from '@/pinia/common'
import { useBatchChangeSizeStore } from '@/pinia/batchChangeSize'
import { imgWatermarkExts, getWebExtsByName } from '@/utils/enum/configData'
import BatchSwiper from '@/components/Batch/BatchSwiper.vue'
import { messageSimple } from '@/utils/message'

const route = useRoute()
const commonStore = useCommonStore()
const batchStore = useBatchChangeSizeStore()
let { imgsMap, currentIndex, currentImg, applyToAll, loading } = storeToRefs(batchStore)
let { showConvertSuccessDialog } = storeToRefs(commonStore)
const drawer = ref(true)
const isCustomSize = ref(true)
const isScaleSize = ref(false)
let imgUrl = ref('')

// 初始化
batchStore.init(route.name)
batchStore.batchSaveCallback()
onMounted(() => {
  if (!imgUrl.value && currentImg.value?.file) {
    imgUrl.value = URL.createObjectURL(currentImg.value.file)
  }
})
onUnmounted(() => {
  batchStore.unBindEvent()

  if (imgUrl.value) {
    URL.revokeObjectURL(imgUrl.value)
  }
})

watch(
  () => [currentIndex.value],
  () => {
    // newValue, oldValue
    // console.log(newValue, oldValue)
    batchStore.getCurrent()
  }
)
watch(
  () => [currentImg.value],
  () => {
    sizeTypeChange(currentImg.value.record.sizeType)
    if (currentImg.value) {
      currentImg.value.record.customWidth = currentImg.value.record.customWidth || currentImg.value.record.width
      currentImg.value.record.customHeight = currentImg.value.record.customHeight || currentImg.value.record.height

      URL.revokeObjectURL(imgUrl.value)
      imgUrl.value = URL.createObjectURL(currentImg.value.file)
    }
  }
)

const sizeTypeChange = (sizeType: any) => {
  currentImg.value.record.sizeType = sizeType
  isCustomSize.value = sizeType === 'customSize'
  isScaleSize.value = sizeType === 'scaleSize'
}

const reset = () => {
  currentImg.value.record.sizeType = 'customSize'
  currentImg.value.record.customWidth = currentImg.value.record.width
  currentImg.value.record.customHeight = currentImg.value.record.height
  currentImg.value.record.scale = 100
  sizeTypeChange(currentImg.value.record.sizeType)
}

const handler = () => {
  applyToAll.value = true
  imgsMap.value.forEach((img: any) => {
    img.record.sizeType = currentImg.value.record.sizeType
    img.record.customWidth = currentImg.value.record.customWidth
    img.record.customHeight = currentImg.value.record.customHeight
    img.record.scale = currentImg.value.record.scale
  })
  messageSimple.success('应用成功')
}
</script>

<style lang="scss" scoped>
@import './index.scss';
</style>
