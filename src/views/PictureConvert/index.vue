<template>
  <div v-loading="loading" class="pictureConvert">
    <!-- 图片列表无内容 -->
    <PictureNoPicture></PictureNoPicture>
    <section v-if="imgsMap.size" class="list">
      <!-- 顶部操作栏 -->
      <PictureHeader></PictureHeader>
      <!-- 图片列表 -->
      <div
        class="item-wrap-web auto-scrollbar" v-scroll-effect
        :class="{
          'iten-wrap-electron': isElectron,
        }"
      >
        <div class="item" v-for="(key, index) in imgsMap" :key="index">
          <PictureContent :index="index" :imgMap="key"></PictureContent>
        </div>
      </div>
    </section>
    <!-- 底部操作栏 -->
    <PictureFooter></PictureFooter>
    <!-- web才使用 -->
    <InputHidden></InputHidden>
  </div>
</template>

<script lang="ts" setup>
import { onUnmounted } from 'vue'
import { useRoute } from 'vue-router'
import { storeToRefs } from 'pinia'
import { usePictureStore } from '@/pinia/picture'
import PictureContent from '@/components/Picture/PictureContent.vue'
import PictureFooter from '@/components/Picture/PictureFooter.vue'
import PictureHeader from '@/components/Picture/PictureHeader.vue'
import InputHidden from '@/components/Common/InputHidden.vue'
import PictureNoPicture from '@/components/Picture/PictureNoPicture.vue'

const route = useRoute()
const pictureStore = usePictureStore()
const isElectron = window.isElectron
let { imgsMap, loading } = storeToRefs(pictureStore)

// 初始化
pictureStore.init(route.name)

// 图片转换结果返回
pictureStore.progressCallback('magick-execute-progress')

onUnmounted(() => pictureStore.unBindEvent())
</script>

<style lang="scss">
.pictureConvert {
  min-height: 80vh;
}
</style>
<style lang="scss" scoped>
@import './index.scss';
</style>
