<script setup lang="ts">
import { ref } from 'vue'
import Header from '@/components/Home/Header.vue'
import Footer from '@/components/Home/Footer.vue'
const showFooter = ref(false)
setTimeout(() => {
  showFooter.value = true
}, 100)
</script>

<template>
  <div class="home-wrap-scroll">
    <main class="home-wrap">
      <Header type="home" />
      <div class="main flex">
        <div class="content">
          <iframe
            src="/picture/convert"
            style="width: 100%; height: 100%; border: 0"
          ></iframe>
        </div>
      </div>
      <Footer v-if="showFooter" />
    </main>
  </div>
</template>

<style lang="scss" scoped>
.home-wrap-scroll {
  .home-wrap {
    height: 500px;
    background: url('/img/pic/web/table_web_bg.png');
    .main {
      height: calc(100vh - 112px);
      align-items: center;
      justify-content: center;

      .content {
        position: relative;
        width: 1100px !important;
        height: 672px !important;
        @include common-dialog;
      }
    }

    .vipPopover {
      margin-left: -85px !important;
    }
  }
}
</style>
