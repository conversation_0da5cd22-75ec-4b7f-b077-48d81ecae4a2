<template>
  <div class="index">
    <section class="header flex">
      <div class="left flex">
        <div class="module1 flex" @click="goNext('convert')">
          <div class="left">
            <p class="title">通用格式转换</p>
            <p class="info">一键批量操作，支持几十种格式相互转换</p>
            <p class="btn convert">
              <img src="/img/pic/second2/<EMAIL>" alt="" />
            </p>
          </div>
          <div class="right">
            <img src="/img/pic/second2/<EMAIL>" alt="" />
          </div>
        </div>
        <div class="module1 flex" @click="goNext('compress')">
          <div class="left">
            <p class="title">图片压缩</p>
            <p class="info">极限压缩，原画品质。速度快，效果好</p>
            <p class="btn convert">
              <img src="/img/pic/second2/<EMAIL>" alt="" />
            </p>
          </div>
          <div class="right">
            <img src="/img/pic/second2/<EMAIL>" alt="" />
          </div>
        </div>
      </div>
      <div class="right">
        <div class="module2 flex" @click="goNext('watermark')">
          <img src="/img/pic/second2/<EMAIL>" alt="" />
          <div class="flex1">
            <p class="title">图片加水印</p>
            <p class="info">操作简单，模版丰富</p>
          </div>
        </div>
        <div class="module2 flex btn" @click="goNext('pdf')">
          <img src="/img/pic/second2/<EMAIL>" alt="" />
          <div class="flex1">
            <p class="title">图片转PDF</p>
            <p class="info">一键处理，简单易用</p>
          </div>
        </div>
        <!-- <div class="module2 flex" @click="goNext('puzzle')">
          <img src="/img/pic/second2/<EMAIL>" alt="" />
          <div class="flex1">
            <p class="title">拼图</p>
            <p class="info">操作简单，模版丰富</p>
          </div>
        </div>
        <div class="module2 flex btn" @click="goNext('passport')">
          <img src="/img/pic/second2/<EMAIL>" alt="" />
          <div class="flex1">
            <p class="title">证件照</p>
            <p class="info">一键处理，简单易用</p>
          </div>
        </div> -->
      </div>
    </section>
    <section class="body flex">
      <div
        class="item"
        v-for="(item, index) in list"
        :key="index"
        @click="goNext(item.name)"
      >
        <img :src="item?.icon" alt="" />
        <p>{{ item?.title }}</p>
      </div>
    </section>
  </div>
</template>

<script lang="ts" setup>
import { VERSION } from '@/utils/configData/config'
import bridge from '@/utils/bridge'
import { useRouter } from 'vue-router'
const router = useRouter()
const goNext = (name: string) => {
  router.push({ name: name })
}

let list = [
  {
    icon: '/img/pic/second2/<EMAIL>',
    name: 'raw',
    title: 'raw转换',
  },
  {
    icon: '/img/pic/second2/<EMAIL>',
    name: 'heic',
    title: 'heic转换',
  },
  {
    icon: '/img/pic/second2/<EMAIL>',
    name: 'livp',
    title: 'livp转换',
  },
  {
    icon: '/img/pic/second2/<EMAIL>',
    name: 'matting',
    title: '智能抠图',
  },
  // {
  //   icon: '/img/pic/second2/<EMAIL>',
  //   name: 'photoTemplate',
  //   title: '写真模版',
  // },
  // {
  //   icon: '/img/pic/second2/<EMAIL>',
  //   name: 'puzzle',
  //   title: '拼图',
  // },
  {
    icon: '/img/pic/second2/<EMAIL>',
    name: 'passport',
    title: '证件照',
  },
  {
    icon: '/img/pic/second2/<EMAIL>',
    name: 'watermark',
    title: '图片加水印',
  },
  {
    icon: '/img/pic/second2/<EMAIL>',
    name: 'enlarge',
    title: '图片无损放大',
  },
  {
    icon: '/img/pic/second2/<EMAIL>',
    name: 'oldPicture',
    title: '老照片上色',
  },
  {
    icon: '/img/pic/second2/<EMAIL>',
    name: 'repair',
    title: '模糊人脸修复',
  },
  {
    icon: '/img/pic/second2/<EMAIL>',
    name: 'pdf',
    title: '图片转PDF',
  },
  {
    icon: '/img/pic/second2/<EMAIL>',
    name: 'cartoon',
    title: '人脸动漫化',
  },
  {
    icon: '/img/pic/second2/<EMAIL>',
    name: 'sketch',
    title: '人像素描',
  },
  {
    icon: '/img/pic/second2/<EMAIL>',
    name: 'changeSize',
    title: '批量改尺寸',
  },
  {
    icon: '/img/pic/second2/<EMAIL>',
    name: 'crop',
    title: '批量裁剪',
  },
  {
    icon: '/img/pic/second2/<EMAIL>',
    name: 'rename',
    title: '批量重命名',
  },
  {
    icon: '/img/pic/second2/<EMAIL>',
    name: 'rotate',
    title: '批量旋转',
  },
  {
    icon: '/img/pic/second2/<EMAIL>',
    name: 'classify',
    title: '批量分类',
  },
]
if (window.isElectron && bridge.compareVersions(VERSION, '*******') <= 0) {
  list = list.filter((item) => {
    return !item.title.includes('批量')
  })
}
</script>

<style lang="scss" scoped>
@import './index.scss';
.index {
  margin: calc(48px + 47px) auto 0;
  width: 1040px;

  .header {
    height: 219px;
    .left {
      .module1 {
        position: relative;
        margin-right: 25px;
        width: 370px;
        height: 220px;
        background: #ffffff;
        box-shadow: 0px 0px 8px 0px rgba(178, 178, 178, 0.3);
        border-radius: 6px;
        user-select: none;
        cursor: pointer;

        &:hover {
          opacity: 0.7;
        }

        &::before {
          content: '';
          position: absolute;
          top: -7px;
          left: 0;
          width: 84px;
          height: 30px;
          background: url('/img/pic/second2/<EMAIL>');
          background-size: 100% 100%;
        }

        .left {
          margin-left: 32px;
          .title {
            margin-top: 45px;

            font-weight: 600;
            font-size: 24px;
            color: #000000;
            line-height: 33px;
            text-shadow: 0px 0px 8px rgba(178, 178, 178, 0.3);
          }
          .info {
            margin-top: 5px;

            width: 160px;
            font-size: 14px;
            color: #666666;
            line-height: 20px;
            text-shadow: 0px 0px 8px rgba(178, 178, 178, 0.3);
          }
          .convert {
            margin-top: 25px;
            text-align: left;
            img {
              width: 120px;
              height: 40px;
              object-fit: contain;
            }
          }
        }

        .right {
          margin: 48px 0 0 40px;

          img {
            width: 98px;
            height: 98px;
            object-fit: contain;
          }
        }
      }
    }

    .right {
      .module2 {
        margin-bottom: 24px;
        width: 250px;
        height: 98px;
        background: #ffffff;
        box-shadow: 0px 0px 8px 0px rgba(178, 178, 178, 0.3);
        border-radius: 6px;
        border: 1px solid #ffffff;
        user-select: none;
        cursor: pointer;

        &:hover {
          opacity: 0.7;
        }

        img {
          margin: 20px 14px 20px 20px;
          width: 58px;
          height: 58px;
        }

        .title {
          margin-top: 26px;
          font-weight: 500;
          font-size: 18px;
          color: #000000;
          line-height: 25px;
          text-shadow: 0px 0px 8px rgba(178, 178, 178, 0.3);
          text-align: left;
        }
        .info {
          margin-top: 2px;
          font-size: 14px;
          color: #666666;
          line-height: 20px;
          text-shadow: 0px 0px 8px rgba(178, 178, 178, 0.3);
          text-align: left;
        }
      }
    }
  }

  .body {
    flex-wrap: wrap;
    margin: 25px 0;
    width: 1040px;
    padding: 19px 32px;
    background: #ffffff;
    box-shadow: 0px 0px 8px 0px rgba(178, 178, 178, 0.3);
    border-radius: 12px;

    .item {
      padding: 19px 0 30px;

      // width: 122px;
      // height: 100px;
      width: 118px;
      height: 96px;
      margin: 2px;

      background: #ffffff;
      border-radius: 6px;
      text-align: center;
      user-select: none;
      text-align: center;
      cursor: pointer;

      &:hover {
        box-shadow: 0px 0px 8px 0px rgba(178, 178, 178, 0.3);
      }

      img {
        width: 36px;
        height: 36px;
      }

      p {
        margin-top: 6px;
        font-weight: 500;
        font-size: 14px;
        color: #333333;
        line-height: 20px;
        text-shadow: 0px 0px 8px rgba(178, 178, 178, 0.3);
      }
    }
  }
}
</style>
