<template>
  <div v-loading="loading" class="picture-watermark">
    <!-- 图片列表无内容 -->
    <PictureNoPicture :imgExts="imgWatermarkExts"></PictureNoPicture>
    <section v-if="imgsMap.size" class="list">
      <!-- 顶部操作栏 -->
      <PictureHeader></PictureHeader>
      <!-- 图片列表 -->
      <div
        class="item-wrap-web auto-scrollbar" v-scroll-effect
        :class="{
          'iten-wrap-electron': isElectron,
        }"
      >
        <div class="item" v-for="(key, index) in imgsMap" :key="index">
          <PictureContent :index="index" :imgMap="key"></PictureContent>
        </div>
      </div>
    </section>
    <!-- 底部操作栏 -->
    <PictureFooter></PictureFooter>
    <!-- web才使用 -->
    <InputHidden accept="image/*"></InputHidden>
    <WatermarkPictureEditDialog
      v-if="showWatermarkPictureEditDialog"
    ></WatermarkPictureEditDialog>
  </div>
</template>

<script lang="ts" setup>
import { onUnmounted } from 'vue'
import { useRoute } from 'vue-router'
import { storeToRefs } from 'pinia'
import { usePictureStore } from '@/pinia/picture'
import { useWatermarkStore } from '@/pinia/watermark'
import PictureContent from '@/components/Picture/PictureContent.vue'
import PictureFooter from '@/components/Picture/PictureFooter.vue'
import PictureHeader from '@/components/Picture/PictureHeader.vue'
import InputHidden from '@/components/Common/InputHidden.vue'
import PictureNoPicture from '@/components/Picture/PictureNoPicture.vue'
import WatermarkPictureEditDialog from '@/components/Picture/Watermark/WatermarkPictureEditDialog.vue'
import { imgWatermarkExts } from '@/utils/enum/configData'

const route = useRoute()
const pictureStore = usePictureStore()
const watermarkStore = useWatermarkStore()
const isElectron = window.isElectron
let { imgsMap, loading } = storeToRefs(pictureStore)
let { showWatermarkPictureEditDialog } = storeToRefs(watermarkStore)

// 初始化
pictureStore.init(route.name)
watermarkStore.getListAll()

// 水印转换结果返回
pictureStore.waterProgressCallback()

pictureStore.bindEvent()
onUnmounted(() => pictureStore.unBindEvent())
</script>

<style lang="scss" scoped>
@import './index.scss';
</style>
