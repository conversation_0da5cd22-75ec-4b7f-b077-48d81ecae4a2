// @import '@/styles/btn.scss';
.Matting {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  height: 100%;
  background-color: #f6f6f6;

  @keyframes spin {
    0% {
      transform: rotate(0deg);
    } /* 动画开始时，图片旋转0度 */
    100% {
      transform: rotate(360deg);
    } /* 动画结束时，图片旋转360度 */
  }

  .box {
    position: absolute;
    margin: 0 47px;
    width: calc(100% - 289px - 94px);
    height: calc(100% - 202px);
    box-shadow: 0px 0px 20px 0px rgba(0, 0, 0, 0.07);
  }

  .detail {
    position: relative;
    -webkit-box-flex: 1;
    -ms-flex: 1;
    flex: 1;
    height: 100%;

    .watermark-box {
      overflow: hidden;
      pointer-events: none;
      z-index: 3;
    }

    .segment-drawer-wrapper {
      position: relative;

      &.disabled {
        pointer-events: none; /* Prevents all mouse interactions */
        opacity: 0.6; /* Visual indication that it's disabled */

        &::after {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          z-index: 10;
          /* This transparent overlay prevents any clicks from reaching elements underneath */
          background-color: rgba(0, 0, 0, 0.01);
          cursor: not-allowed;
        }
      }
    }
  }
}