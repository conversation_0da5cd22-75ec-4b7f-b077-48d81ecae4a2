<template>
  <div
    class="Matting"
    :style="{
      backgroundColor: imgsMap.size ? '#f6f6f6' : '#fff',
    }"
  >
    <!-- 抠图首页 -->
    <EntranceHome
      :imgExts="imgWatermarkExts"
      :imgsMap="imgsMap"
      :callback="batchStore.imageInfoHandler"
    />
    <div
      v-if="imgsMap.size"
      v-loading="loading"
      :element-loading-text="''"
      class="detail"
    >
      <MattingHeader :currentStore="batchStore" :imgsMap="batchStore.imgsMap" />
      <!-- 1、手动精修 -->
      <ManualRefineBox
        ref="manualRefineBoxRef"
        :style="{
          zIndex:
            batchStore.currentSegment === batchStore.segmentsType.custom
              ? 2
              : 0,
        }"
        :currentStore="batchStore" />
      <!-- 2、换背景 -->
      <BackgroundBox
        ref="backgroundBoxRef"
        :currentStore="batchStore"
        :imgsMap="imgsMap"
        :showProgress="showProgress"
        :progressValue="progressValue" />
      <!-- 3、改尺寸 -->
      <ResizeBox
        ref="resizeBoxRef"
        :style="{
          zIndex:
            batchStore.currentSegment === batchStore.segmentsType.changeSize
              ? 2
              : 0,
        }"
        :currentStore="batchStore" />
      <!-- 水印 -->
      <div v-if="!showProgress && batchStore.currentImg.mattingImage && commonStore.watermark()" class="watermark-box box">
        <el-watermark
          content="VIP可无水印保存"
          rotate="-50"
          :gap="[50, 50]"
          :font="{
            fontSize: 14,
            fontWeight: 500,
            color: 'rgb(187, 187, 187)',
          }"
          style="color: #fff"
        >
          <div style="height: 4000px" />
        </el-watermark>
      </div>
      <!-- SegmentDrawer -->
      <div class="segment-drawer-wrapper" :class="{ disabled: showProgress }">
        <SegmentDrawer :currentStore="batchStore" />
      </div>
    </div>
    <!-- web才使用 -->
    <InputHidden
      :keepShow="true"
      :multiple="false"
      :accept="getWebExtsByName('imgWatermarkExts')"
      :chooseWebImages="batchStore.chooseWebImages"
    />
    <!-- web才使用 -->
    <InputHidden
      id="customMattingInput"
      :keepShow="true"
      :multiple="true"
      :accept="getWebExtsByName('imgWatermarkExts')"
      :chooseWebImages="batchStore.customChooseWebImages"
    />
    <ExportDirectoryDialog />
    <PassportEditDialog v-if="currentImg.mattingImage && batchStore.showEditDialog" />
    <ConvertSuccessDialog v-if="showConvertSuccessDialog" :showOpen="false" />
  </div>
</template>

<script lang="ts" setup>
import { onMounted, onUnmounted, watch, nextTick } from 'vue'
import { useRoute } from 'vue-router'
import { storeToRefs } from 'pinia'
import { useCommonStore } from '@/pinia/common'
import { usePassportStore } from '@/pinia/passport'
import { imgWatermarkExts, getWebExtsByName } from '@/utils/enum/configData'
import { ElMessage } from 'element-plus'
import { MattingHeader, BackgroundBox, ManualRefineBox, ResizeBox, PassportEditDialog } from '@/components/Matting'
import EntranceHome from '@/views/Passport/components/EntranceHome.vue'
import SegmentDrawer from '@/components/Batch/SegmentDrawer.vue'
import InputHidden from '@/components/Common/InputHidden.vue'
import ExportDirectoryDialog from '@/components/Common/ExportDirectoryDialog.vue'
import ConvertSuccessDialog from '@/components/Common/ConvertSuccessDialog.vue'
// 导入抠图处理器
import {
  MattingProcessor,
  initMattingProcessor,
  processMattingImage,
  resetMattingState,
  mattingState
} from '@/utils/mattingProcessor'

const route = useRoute()
const commonStore = useCommonStore()
const batchStore = usePassportStore()

// 解构出需要的状态
let {
  imgsMap,
  currentIndex,
  currentImg,
  manualRefineBoxRef,
  backgroundBoxRef,
  resizeBoxRef,
  loading,
} = storeToRefs(batchStore)
let { showConvertSuccessDialog } = storeToRefs(commonStore)

// 初始化
batchStore.init(route.name)
batchStore.batchSaveCallback()

// 监听 currentIndex 变化
watch(
  () => [currentIndex.value],
  async () => {
    // console.log("currentIndexChange:", currentIndex.value);
    batchStore.getCurrent()
  }
)

// 监听 currentImg 变化
watch(
  () => [currentImg.value],
  async () => {
    try {
      resizeBoxRef.value?.clearCropper() // 调用 ResizeBox 组件的 clearCropper 方法

      // 重置处理器状态和释放资源
      resetMattingState()

      // 等待更长时间确保资源已释放，应该不需要先注释掉
      // await new Promise((resolve) => setTimeout(resolve, 300))

      // 重新初始化处理器
      await initProcessor()

      // 开始抠图
      handleMatting()

      // 当 currentImg 变化时，无论是否有 mattingImage，都需要更新尺寸
      // 这确保了首次加载时也会计算尺寸
      nextTick(() => batchStore.getBackgroundBoxSize())
      // 注意，需要清空手动精修历史记录
      batchStore.maskHistory = []
      batchStore.maskHistoryIndex = -1

      // 证件照常用分辨率为 300dpi（高清印刷标准）
      // 像素 = 毫米 ÷ 25.4 × 分辨率（dpi）
      // 毫米 = 像素 ÷ 分辨率（dpi）× 25.4

      // 证件照，初始化背景色
      batchStore.currentImg.backgroundColor = '#3e90e5' // #3e90e5  #ed0e1a
    } catch (error) {
      console.error('Error processing new image:', error)
      handleError(
        'Error processing new image: ' +
          (error instanceof Error ? error.message : String(error))
      )
    }
  }
)

// 处理抠图
const handleMatting = () => {
  try {
    if (!currentImg.value || !currentImg.value.file) {
      ElMessage.warning('请先选择图片')
      return
    }

    // 检查文件大小和尺寸，避免处理过大的图片
    const fileSizeMB = currentImg.value.file.size / (1024 * 1024)
    if (fileSizeMB > 30) {
      ElMessage.warning('图片过大，可能导致内存不足。请选择小于30MB的图片。')
      return
    }

    // 创建一个图像对象来检查尺寸
    const img = new Image()
    img.onload = () => {
      const pixelCount = img.width * img.height
      if (pixelCount > 10000 * 10000) {
        // 如果像素数量超过1亿
        ElMessage.warning(
          '图片尺寸过大，可能导致内存不足。请选择尺寸小一点的图片。'
        )
        return
      }

      // 开始处理图片
      startProcessing()
    }
    img.onerror = () => {
      console.error('Failed to load image for size check')
      // 如果加载失败，仍然尝试处理
      startProcessing()
    }
    img.src = URL.createObjectURL(currentImg.value.file)

    function startProcessing() {
      // 开始抠图处理
      const reader = new FileReader()
      reader.onload = (e: ProgressEvent<FileReader>) => {
        if (e.target && e.target.result) {
          const result = e.target.result as string
          srcImageUrl.value = result
          processImage(result)
        }
      }
      reader.onerror = (error) => {
        console.error('Failed to read file:', error)
        handleError(
          'Failed to read file: ' +
            (error instanceof Error ? error.message : 'Unknown error')
        )
      }
      reader.readAsDataURL(currentImg.value.file)
    }
  } catch (error) {
    console.error('Error in handleMatting:', error)
    handleError(
      'Error processing image: ' +
        (error instanceof Error ? error.message : String(error))
    )
  }
}

// ================ matting ================
// 使用抠图状态
const {
  imageState,
  srcImageUrl,
  progressValue,
  showProgress
} = mattingState

// 错误处理
const handleError = (error: unknown) => {
  console.error('Error:', error)
  imageState.value = 'NoImage'

  ElMessage({
    type: 'error',
    message:
      error instanceof Error
        ? error.message
        : String(error) || 'An error occurred',
    duration: 5000,
  })
}

// 处理图像抠图
const processImage = (imageUrl: string) => {
  try {
    processMattingImage(imageUrl, currentImg.value, batchStore.createMattingCropImage)
  } catch (error) {
    console.error('Error in processImage:', error)
    handleError(error)
  }
}

// 初始化处理器
const initProcessor = () => {
  return initMattingProcessor(currentImg.value, batchStore.createMattingCropImage)
    .catch((error: unknown) => {
      handleError(error)
      throw error
    })
}
// ================ matting ================

// 处理窗口大小变化
const handleResize = () => {
  // 获取背景盒子的尺寸
  batchStore.getBackgroundBoxSize()
}

onMounted(() => {
  initProcessor()

  // 添加窗口大小变化监听
  window.addEventListener('resize', handleResize)

  // 初始获取背景盒子尺寸，切换页面再回来也需要
  nextTick(() => batchStore.getBackgroundBoxSize())
})

onUnmounted(() => {
  batchStore.unBindEvent()

  // 移除窗口大小变化监听
  window.removeEventListener('resize', handleResize)

  if (MattingProcessor.worker) MattingProcessor.stop()
  resetMattingState()
})
</script>

<style lang="scss" scoped>
@import './index.scss';
</style>
