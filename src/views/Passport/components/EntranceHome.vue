<template>
  <div v-show="!props.imgsMap.size" class="entrance-home">
    <div
      class="picture-btns center"
      @click="(e: any) => commonStore.webUpload(e)"
    >
      <div class="add-img btn flex">
        <img src="/img/pic/second2/<EMAIL>" />
        <p class="btn-text">添加图片</p>
      </div>
    </div>

    <div class="demo-list">
      <div class="demo-title">示例证件照</div>
      <div class="demo-item flex">
        <img
          v-for="index in 7"
          :key="index"
          :src="`/img/pic/second2/picture_example_${index}@2x.png`"
        />
      </div>
    </div>

    <div class="demo-list">
      <div class="demo-title">拍照注意</div>
      <div class="demo-item attention flex">
        <img
          v-for="index in 7"
          :key="index"
          :src="`/img/pic/second2/picture_warn_${index}@2x.png`"
        />
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { useCommonStore } from '@/pinia/common'
const commonStore = useCommonStore()

const props = defineProps({
  imgExts: {
    type: Array,
    default: [],
  },
  imgsMap: {
    type: Object,
    default: {},
  },
  callback: {
    // 添加图片回调，将选择后的图片传给回调
    type: Function,
    default: () => {},
  },
})
</script>

<style lang="scss" scoped>
.entrance-home {
  height: 100vh;
  padding: 45px 54px;
  background: #f5f6f7;

  .picture-btns {
    height: 127px;
    width: 100%;
    margin-bottom: 17px;
    background: #e4f0fe;
    border-radius: 12px;
    cursor: pointer;

    .add-img {
      width: 112px;
      height: 28px;

      img {
        width: 28px;
        height: 28px;
      }

      .btn-text {
        font-size: 18px;
        color: #333333;
        line-height: 28px;
        margin-left: 12px;
      }
    }
  }

  .demo-list {
    margin-top: 28px;

    .demo-title {
      font-weight: 500;
      font-size: 16px;
      color: #333333;
      line-height: 22px;
      margin-bottom: 12px;
    }

    .demo-item {
      justify-content: space-between;
      img {
        width: 102px;
        height: 143px;
        -webkit-user-drag: none;
      }
    }
    .attention {
      img {
        height: 135px;
      }
    }
  }
}
</style>
