<template>
  <div v-loading="pageLoading" class="picture-ali">
    <section
      v-if="pageInfo[pageType].currentImg?.origin"
      class="detail-container"
    >
      <Header2></Header2>
      <Detail></Detail>
    </section>
    <section v-else class="demo-container">
      <Header1></Header1>
      <DemoDetail></DemoDetail>
      <Swiper></Swiper>
    </section>
    <InputHidden
      accept="image/*"
      :multiple="false"
      :chooseWebImages="pictureAliStore.chooseWebImages"
    ></InputHidden>
    <ExportDirectoryDialog></ExportDirectoryDialog>
    <ConvertSuccessDialog
      v-if="showConvertSuccessDialog"
      :url="exportUrl"
    ></ConvertSuccessDialog>
  </div>
</template>

<script lang="ts" setup>
import { useRoute } from 'vue-router'
import { storeToRefs } from 'pinia'
import { usePictureAliStore } from '@/pinia/pictureAli'
import { useCommonStore } from '@/pinia/common'
// import Header1 from '@/components/PictureAli/Header1.vue'
// import Header2 from '@/components/PictureAli/Header2.vue'
// import DemoDetail from '@/components/PictureAli/DemoDetail.vue'
// import Detail from '@/components/PictureAli/Detail.vue'
// import Swiper from '@/components/PictureAli/Swiper.vue'
// import Menus from '@/components/PictureAli/Menus.vue'
// import InputHidden from '@/components/Common/InputHidden.vue'

const pictureAliStore = usePictureAliStore()
const commonStore = useCommonStore()
const { pageInfo, exportUrl, pageType, pageLoading } = storeToRefs(pictureAliStore)
let { showConvertSuccessDialog } = storeToRefs(commonStore)

// setTimeout(() => {
//   pageInfo.value[pageType.value].currentImg.select = 'origin'
//   pageInfo.value[pageType.value].currentImg.new =
//     'https://static.miaoji66.com/image_convert/demo-compress/oldPicture/result1.jpg'
//   pageInfo.value[pageType.value].currentImg.origin =
//     'https://static.miaoji66.com/image_convert/demo-compress/oldPicture/origin1.jpg'
// }, 50)

const route = useRoute()
pageType.value = route.name as string
pictureAliStore.processCallback()
</script>

<style lang="scss" scoped>
@import './index.scss';
</style>
