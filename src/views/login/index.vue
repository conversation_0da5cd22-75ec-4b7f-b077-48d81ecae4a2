<template>
  <div class="login-wrap">
    <div class="login-content">
      <div class="login-content-c">
        <div class="login-icon">
          <img src="/img/login/moto-show-icon.png" />
          <div class="login-icon-text">
            <p><span>摩托范</span></p>
          </div>
        </div>
        <div class="login-title">内容审核管理系统</div>
        <div class="login-text">您可以直接输入您的管理账号和密码登录</div>
        <el-form ref="loginFormRef" class="login-form" :model="param" :rules="rules">
          <el-form-item prop="username">
            <div class="content-input">
              <img src="/img/login/phone-icon.png" />
              <el-input v-model="param.username" type="text" placeholder="账号" auto-complete="on"></el-input>
            </div>
          </el-form-item>
          <el-form-item prop="password">
            <div class="content-input">
              <img src="/img/login/password-icon.png" />
              <el-input v-model="param.password" type="password" placeholder="密码" auto-complete="on"
                @keyup.enter="submitForm">
              </el-input>
            </div>
          </el-form-item>
          <div class="login-btn">
            <el-button type="primary" :loading="btnLoading" round @click="submitForm">登录</el-button>
          </div>
        </el-form>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive } from "vue";
import { useRoute, useRouter } from "vue-router";
import { ElMessage } from "element-plus";
import { validate } from "@/utils/formExtend";
import util from "@haluo/util"; // cros cookie

const route = useRoute();
const router = useRouter();
const btnLoading = ref(false);
const loginFormRef = ref(null);
const param = reactive({
  username: "",
  password: "",
});
const rules = reactive({
  username: [{ required: true, message: "请输入账号", trigger: "blur" }],
  password: [{ required: true, message: "请输入密码", trigger: "blur" }],
});

// 登录
const submitForm = async () => {
  // cros cookie
  const userInfo = util.cookie.getCookie("userInfo");
  if (userInfo) {
    localStorage["userInfo"] = userInfo;
    router.push({ path: (route.query.redirect as string) || "/" });
    return;
  }

  const valid = await validate(loginFormRef);
  if (valid) {
    btnLoading.value = true;
  } else {
    if (!param.username.trim() && !param.password.trim()) {
      ElMessage.info("请输入账号和密码");
      return;
    }
    if (!param.username.trim()) {
      ElMessage.info("请输入账号");
      return;
    }
    if (!param.password.trim()) {
      ElMessage.info("请输入密码");
      return;
    }
  }
};
</script>
<style lang="scss">
.login-form .el-form-item .el-form-item__content .content-input .el-input {
  width: 190px;
}

.login-form .el-form-item .el-form-item__content .content-input .el-input .el-input__inner {
  height: 38px;
  padding: 0;
  background-color: #fff4e3;
  border: none;
  box-shadow: none;
}
</style>
<style scoped lang="scss">
@import "@/styles/common.scss";

.dioag {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 100;
  width: 100%;
  height: 100%;
  background-color: rgba(204, 200, 196, 0.5);

  .dioag-content {
    position: relative;
    top: 50%;
    left: 50%;
    width: 500px;
    max-height: 700px;
    padding: 30px 40px;
    overflow: hidden;
    overflow-y: auto;
    background-color: #fff;
    border-radius: 5px;
    transform: translate(-50%, -50%);

    .dioag-close {
      position: absolute;
      top: 20px;
      right: 24px;
    }

    .dioag-content-tip {
      margin-bottom: 30px;
      text-align: center;

      p {
        margin-top: 5px;
        font-size: 14px;
        font-weight: bold;
        color: #333;
      }
    }

    .shopList-item {
      display: flex;
      height: 80px;
      margin-bottom: 20px;
      border: 1px solid #eee;
      border-radius: 5px;
      align-items: center;

      img {
        width: 40px;
        height: 40px;
        margin: 0 10px 0 20px;
        border-radius: 50%;
      }
    }
  }
}

.login-wrap {
  position: relative;
  width: 100%;
  height: 100%;
  background-color: #235bae;
  background-image: url("/img/login/login-bg.png");
  background-size: cover;
}

.login-content {
  width: 100%;
  height: 100%;
  padding-left: 300px;
  overflow: hidden;
}

.login-content-c {
  width: 400px;
  margin-top: 150px;
  margin-left: 130px;

  .login-icon {
    display: flex;
    align-items: center;

    img {
      width: 40px;
      height: 40px;
    }

    .login-icon-text p:nth-child(1) {
      font-size: 16px;
      font-weight: bold;

      span {
        margin-right: 10px;
        margin-left: 4px;
      }
    }

    .login-icon-text p:nth-child(2) {
      font-size: 12px;
      color: #d0d0d0;
    }
  }

  .login-title {
    margin-top: 60px;
    font-size: 34px;
  }

  .login-text {
    margin-top: 5px;
    margin-bottom: 140px;
    font-size: 14px;
    color: #979ca9;
  }
}

.login-form {
  .content-input {
    display: flex;
    width: 340px;
    background-color: #fff4e3;
    border-radius: 20px;
    align-items: center;

    img {
      width: 20px;
      height: 20px;
      margin-right: 10px;
      margin-left: 20px;
    }

    .codetext {
      color: #979ca9;
    }

    .activeCodetext {
      color: #ff3c08;
    }
  }

  .el-input {
    width: 200px;
  }
}

.login-btn {
  width: 340px;
  text-align: center;
}

.login-btn button {
  width: 100%;
  height: 36px;
  margin-top: 20px;
  margin-bottom: 10px;
  background-color: #feaf38;
  border: none;
}

:deep(.el-input__wrapper) {
  background-color: transparent !important;
  box-shadow: none !important;
}
</style>
