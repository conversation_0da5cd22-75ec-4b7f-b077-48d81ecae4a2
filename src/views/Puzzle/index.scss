// @import '@/styles/btn.scss';
.Crop {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  height: 100%;
  background-color: #f6f6f6;

  @keyframes spin {
    0% {
      transform: rotate(0deg);
    } /* 动画开始时，图片旋转0度 */
    100% {
      transform: rotate(360deg);
    } /* 动画结束时，图片旋转360度 */
  }

  section {
    -webkit-box-flex: 1;
    -ms-flex: 1;
    flex: 1;
    height: 100%;
  }

  .detail {
    :deep(.cropper-box) {
      margin: 0 47px;
      // width: 560px;
      // height: 470px;
      width: calc(100% - 289px - 94px);
      height: calc(100% - 202px);
      background-color: transparent;
      box-shadow: 0px 0px 20px 0px rgba(0, 0, 0, 0.17);
      overflow: hidden;

      img {
        // max-width: 100%;
        // max-height: 100%;
        -webkit-app-region: no-drag;
      }

      .cropper-crop-box {
        position: relative;
        box-shadow: 0px 2px 9px 0px rgba(0, 0, 0, 0.31);
        .cropper-view-box {
          outline: 1px solid #ffffff;
        }
        /* 为裁剪框添加网格线 */
        &::before,
        &::after {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          pointer-events: none;
          z-index: 1;
        }
        /* 垂直网格线 */
        &::before {
          background-image: linear-gradient(
            to right,
            #fff 1px,
            transparent 1px
          );
          background-size: 33.33% 100%;
        }
        /* 水平网格线 */
        &::after {
          background-image: linear-gradient(
            to bottom,
            #fff 1px,
            transparent 1px
          );
          background-size: 100% 33.33%;
        }
      }

      .cropper-line {
        background-color: white;
      }

      .cropper-dashed {
        border: 0 solid #fff;
        border-left-width: 1px;
        border-right-width: 1px;
        border-bottom-width: 1px;
        border-top-width: 1px;
      }

      .cropper-point {
        opacity: 1;
        background-color: white;
      }

      .point-n,
      .point-s {
        display: none;
      }

      .point-nw {
        // 左上
        // width: 6px;
        // height: 26px;
        // background: #ffffff;

        width: 26px;
        height: 26px;
        border: 6px solid #ffffff;
        border-radius: 0;
        border-right-width: 0;
        border-bottom-width: 0;
        background-color: transparent;
      }
      .point-ne {
        // 右上
        width: 26px;
        height: 26px;
        border: 6px solid #ffffff;
        border-radius: 0;
        border-left-width: 0;
        border-bottom-width: 0;
        background-color: transparent;
      }
      .point-w {
        // 左中
        width: 6px;
        height: 26px;
        background: #ffffff;
        border-radius: 0;
        margin-top: -12px;
      }
      .point-e {
        // 右中
        width: 6px;
        height: 26px;
        background: #ffffff;
        border-radius: 0;
        margin-top: -12px;
      }
      .point-sw {
        // 左下
        width: 26px;
        height: 26px;
        border: 6px solid #ffffff;
        border-radius: 0;
        border-top-width: 0;
        border-right-width: 0;
        background-color: transparent;
      }
      .point-se {
        // 右下
        width: 26px;
        height: 26px;
        border: 6px solid #ffffff;
        border-radius: 0;
        border-top-width: 0;
        border-left-width: 0;
        background-color: transparent;
      }
    }
  }
}
