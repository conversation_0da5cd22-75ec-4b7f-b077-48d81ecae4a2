// @import '@/styles/btn.scss';
.pictureCompress {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  height: 100%;
  background: #fff;

  @keyframes spin {
    0% {
      transform: rotate(0deg);
    } /* 动画开始时，图片旋转0度 */
    100% {
      transform: rotate(360deg);
    } /* 动画结束时，图片旋转360度 */
  }

  section {
    -webkit-box-flex: 1;
    -ms-flex: 1;
    flex: 1;
    height: 100%;
  }

  .list {
    padding: 0 20px;
    min-width: 943px;

    .item-wrap-web {
      // 计算
      height: calc(100% - 70px - 59px);
      overflow-y: auto;

      .item {
        position: relative;
      }
    }

    .iten-wrap-electron {
      // 计算
      height: calc(100% - 70px - 97px);

      .item {
        .item-content {
          .img-wrap {
            &::before,
            &::after {
              position: absolute;
              content: '';
              top: 0;
              left: 0;
              width: 80px;
              height: 80px;
              background: #000000;
              border-radius: 4px;
              opacity: 0.1;
              z-index: -1;
            }
          }
        }
      }
    }
    .iten-wrap-web-custom {
      height: calc(100% - 70px - 97px - 44px);
    }
    .iten-wrap-electron-custom {
      height: calc(100% - 70px - 97px - 80px);
    }
  }
}
