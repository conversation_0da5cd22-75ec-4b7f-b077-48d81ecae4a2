<template>
  <div v-loading="loading" class="pictureCompress">
    <!-- 图片列表无内容 -->
    <PictureNoPicture :imgExts="imgCompressExts"></PictureNoPicture>
    <section v-if="imgsMap.size" class="list">
      <!-- 顶部操作栏 -->
      <PictureHeader></PictureHeader>
      <!-- 图片列表 -->
      <div
        class="item-wrap-web auto-scrollbar" v-scroll-effect
        :class="{
          'iten-wrap-electron': isElectron,
          'iten-wrap-web-custom': !isElectron && compressType === '自定义',
          'iten-wrap-electron-custom': isElectron && compressType === '自定义',
        }"
      >
        <div class="item" v-for="(key, index) in imgsMap" :key="index">
          <PictureContent :index="index" :imgMap="key"></PictureContent>
        </div>
      </div>
    </section>
    <!-- 底部操作栏 -->
    <CompressFooter></CompressFooter>
    <!-- web才使用 -->
    <InputHidden></InputHidden>
    <CompressDialog v-if="showCompressDialog"></CompressDialog>
  </div>
</template>

<script lang="ts" setup>
import { onUnmounted } from 'vue'
import { useRoute } from 'vue-router'
import { storeToRefs } from 'pinia'
import { usePictureStore } from '@/pinia/picture'
import { useCompressStore } from '@/pinia/compress'
import PictureContent from '@/components/Picture/PictureContent.vue'
import CompressFooter from '@/components/Picture/CompressFooter.vue'
import PictureHeader from '@/components/Picture/PictureHeader.vue'
import InputHidden from '@/components/Common/InputHidden.vue'
import PictureNoPicture from '@/components/Picture/PictureNoPicture.vue'
import CompressDialog from '@/components/Picture/CompressDialog.vue'
import { imgCompressExts } from '@/utils/enum/configData'

const route = useRoute()
const pictureStore = usePictureStore()
const compressStore = useCompressStore()
const isElectron = window.isElectron
let { imgsMap, loading } = storeToRefs(pictureStore)
let { showCompressDialog, compressType } = storeToRefs(compressStore)

// 初始化
pictureStore.init(route.name)

// 图片压缩结果返回
pictureStore.progressCallback('magick-compress-progress')

onUnmounted(() => pictureStore.unBindEvent())
</script>

<style lang="scss" scoped>
@import './index.scss';
</style>
