// Created by wa<PERSON><PERSON> on 2017-04-21.
//========================= 方法 =========================

@mixin background($url) {
  background: url($url) no-repeat center;
  background-image: url($url);
  background-repeat: no-repeat;
  background-size: 100% 100%;
}

@mixin background2($url, $position: center, $size: cover) {
  background: url($url);
  background-position-x: $position;
  background-position-y: $position;
  background-size: $size;
}

//宽高
@mixin wh($width: auto, $height: auto) {
  width: $width;
  height: $height;
}

//字体大小，颜色
@mixin sc($size: 14px, $color: #333) {
  font-size: $size;
  color: $color;
}

// 圆角
@mixin border-radius($radius: 5px) {
  border-radius: $radius;
}

// 阴影
@mixin box-shadow($x: 0, $y: 5px, $blur: 5px, $extend: 5px, $color: gray) {
  box-shadow: $x $y $blur $extend $color;
}

// 公共 box-shadow
@mixin common-shadow {
  box-shadow: 0px 9px 28px 8px rgba(0, 0, 0, 0.05),
    0px 9px 28px 8px rgba(0, 0, 0, 0.05), 0px 9px 28px 8px rgba(0, 0, 0, 0.05),
    0px 6px 16px 0px rgba(0, 0, 0, 0.08), 0px 3px 6px -4px rgba(0, 0, 0, 0.12);
}

// 公共弹框样式
@mixin common-dialog {
  @include common-shadow;
  border: 1px solid #d9d9d9;
}

// 旋转
@mixin rotate($deg: 45deg) {
  transform: rotate($deg);
}

// 动画
@mixin transition($attribute: all, $type: ease-in, $time: 200ms) {
  transition: $attribute $type $time;
}

// flex布局
@mixin flex() {
  display: -webkit-box;
  display: -webkit-flex;
  display: -moz-box;
  display: -moz-flex;
  display: -ms-flexbox;
  display: flex;
}

// flex num
@mixin flex-num($num: 1) {
  flex: $num;
}

// flex-center
@mixin flex-center($num: 1, $justify: center, $align: center) {
  @include flex();

  justify-content: center;
  align-items: center;
}

// flex-center-direction  row column
@mixin flex-center-direction($direction: row) {
  @include flex();

  justify-content: center;
  align-items: center;
  flex-direction: $direction;
}

// children flex & center
@mixin flex-num-center($num: 1, $justify: center, $align: center) {
  flex: $num;
  justify-content: $justify;
  align-items: $align;
}

// center
@mixin center($pack: center, $align: center) {
  display: -webkit-box;
  -webkit-box-align: $align;

  /* autoprefixer: ignore next */
  -webkit-box-pack: $pack;
}

@mixin center2() {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

@mixin center3() {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  margin: auto;
}

@mixin flex-center($pack: center, $align: center) {
  display: flex;
  justify-content: $pack;
  align-items: $align;
}

// img src 图片居中剪切
@mixin img--cover($value: cover) {
  object-fit: $value;
}

// 背景图片居中剪切
@mixin background--cover($value: cover) {
  background-size: $value;
}

// 文字两边对齐
@mixin text-align--justify($value: justify) {
  text-align: $value;
}

// 换行
@mixin word-break($para: break-all) {
  word-break: $para;
}

// 英文字符换行
@mixin english-word-break($para: break-all) {
  @include word-break();

  word-wrap: break-word;
}

// 保持内容不换行，并左右滑动
@mixin nowrap-scroll($width: 375px) {
  display: block;
  width: $width;
  overflow: auto;
  white-space: nowrap;
}

// white-space
@mixin white-space($type: pre) {
  white-space: $type;
}

// 单行...
@mixin dotdotdot1($text-overflow: ellipsis, $space: nowrap, $overflow: hidden) {
  overflow: $overflow;
  text-overflow: $text-overflow;
  white-space: $space;
}
// 单行限宽...
@mixin dotdotdot1-width($width: 100%) {
  width: $width;
  @include dotdotdot1();
}
// 多行...
@mixin dotdotdot2($num: 2) {
  display: -webkit-box;

  /* autoprefixer: ignore next */
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: $num;
}


@for $i from 0 to 100 {
  .mb#{$i} {
    margin-bottom: $i + px;
  }
  .mt#{$i} {
    margin-top: $i + px;
  }
  .mr#{$i} {
    margin-right: $i + px;
  }
  .ml#{$i} {
    margin-left: $i + px;
  }
  .mv#{$i} {
    margin-top: $i + px;
    margin-bottom: $i + px;
  }
  .mh#{$i} {
    margin-right: $i + px;
    margin-left: $i + px;
  }
  .pb#{$i} {
    padding-bottom: $i + px;
  }
  .pt#{$i} {
    padding-top: $i + px;
  }
  .pr#{$i} {
    padding-right: $i + px;
  }
  .pl#{$i} {
    padding-left: $i + px;
  }
  .pv#{$i} {
    padding-top: $i + px;
    padding-bottom: $i + px;
  }
  .ph#{$i} {
    padding-right: $i + px;
    padding-left: $i + px;
  }
}
