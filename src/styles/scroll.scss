// ::-webkit-scrollbar {
//   width: 5px;
//   height: 5px;
// }

// ::-webkit-scrollbar-thumb:vertical {
//   height: 5px;
// }

// ::-webkit-scrollbar-thumb:horizontal {
//   width: 5px;
// }

// ::-webkit-scrollbar-thumb {
//   border-radius: 5px;
//   background: #e5e7ed;
// }

// scrollbar-thumb 定义滚动条，scrollbar-track-piece 定义滚动条轨道
// ::-webkit-scrollbar-track-piece {
//   background-color: #fff;
//   border-radius: 5px;
// }
// ::-webkit-scrollbar-track {
//   border-radius: 5px;
//   background-color: #e5e7ed;
// }

// // ===========================================================================

::-webkit-scrollbar {
  width: 5px;
  height: 5px;
  background: transparent;
}

::-webkit-scrollbar-thumb {
  border-radius: 5px;
}

// 自定义样式
// :hover::-webkit-scrollbar-thumb {
//   background: #e5e7ed;
// }
.auto-scrollbar::-webkit-scrollbar-thumb {
  background: #e5e7ed;
}
