@import './variables.scss';
@import './mixin.scss';
@import './fonts.scss';
@import './reset.scss';
@import './scroll.scss';
@import './transition.scss';
@import './element-ui.scss';
@import './sidebar.scss';
@import './btn.scss';
@import './atomic.scss';
@import './common.scss';

*,
*::before,
*::after {
  padding: 0;
  margin: 0;
  box-sizing: inherit;
}

html {
  height: 100%;
  box-sizing: border-box;
}

body {
  height: 100%;
  font-family: Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB,
    Microsoft YaHei, Arial, sans-serif;
  // font-family: AliInclusiveBody;
  // font-family: AliSquareBody;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  text-rendering: optimizeLegibility;
}

#app {
  height: 100%;
}

label {
  font-weight: 700;
}

a:focus,
a:active {
  outline: none;
}

a,
a:focus,
a:hover {
  color: inherit;
  text-decoration: none;
  cursor: pointer;
}

div:focus {
  outline: none;
}

ul,
li {
  list-style: none;
}

.pointer {
  cursor: pointer;
}

.clearfix {
  zoom: 1;
}

.clearfix:after {
  display: block;
  clear: both;
  content: '\20';
}

img {
  border: none;
  vertical-align: bottom;
}

p,
ul,
ol,
li,
form {
  margin: 0;
  padding: 0;
  list-style: none;
}

a {
  text-decoration: none;
  color: #666;
  border: none;
}

a:hover {
  text-decoration: none;
}

.clear {
  clear: both;
}

button {
  border: none;
}

// 手势按下，文字样式不修改
.noselect {
  -webkit-touch-callout: none;
  user-select: none;
}

// // 定义滚动条高宽及背景 高宽分别对应横竖滚动条的尺寸
// ::-webkit-scrollbar {
//   width: 5px;
//   height: 5px;
// }

// // 定义滚动条轨道 内阴影+圆角
// ::-webkit-scrollbar-track {
//   box-shadow: inset 0 0 1px rgba(0, 0, 0, 0);
//   border-radius: 10px;
//   background-color: #c5c9d8;
// }

// // 定义滑块 内阴影+圆角
// ::-webkit-scrollbar-thumb {
//   border-radius: 10px;
//   box-shadow: inset 0 0 6px #c5c9d8;
//   background-color: #c5c9d8;
// }

// 解决谷歌浏览器中的input背景色默认是黄色
// input:-webkit-autofill {
//   box-shadow: 0 0 0px 1000px @base-bg-color1 inset !important;
// }

input,
textarea {
  -webkit-appearance: none;
  -moz-appearance: none;
  outline: none;
  // font-family: "PingFang SC", "PingHei", "Helvetica Neue", "Helvetica", "STHeitiSC-Light", "Arial", sans-serif;
}

// pre {
//   font-family: "PingFang SC", "PingHei", "Helvetica Neue", "Helvetica", "STHeitiSC-Light", "Arial", sans-serif;
// }

::-webkit-input-placeholder {
  font-size: 14px;
  color: #999;
}

:-moz-placeholder {
  font-size: 14px;
  color: #999;
}

::-moz-placeholder {
  font-size: 14px;
  color: #999;
}

:-ms-input-placeholder {
  font-size: 14px;
  color: #999;
}

input:-moz-placeholder {
  font-size: 14px;
  color: #999;
}
// reset end

// animate start

// .fade-enter-active,
.fade-leave-active {
  transition: opacity 0.25s ease-in;
}

.fade-enter,
.fade-leave-to {
  opacity: 0.1;
}

// color start
// .base-bg-color {
//   background-color: @base-bg-color!important;
// }

.bg-color-white {
  background-color: #fff;
}

.red {
  color: red;
}
// color end

.hide {
  display: none !important;
}

.show {
  display: block !important;
}

.disabled {
  color: #ccc !important;
}

// =========== 弹框公共样式 ==============
.common-dialog {
  @include common-dialog;
}
.mask {
  position: fixed;
  z-index: 2000;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  background: rgba(255, 255, 255, 0.6);
}
// =====================================

.line {
  height: 1px;
  width: 100%;
  background-color: #e0e0e0;
}

.img-bg-mask {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  border-radius: 5px;
  z-index: 50;
  background: rgba(0, 0, 0, 0.2);
}

.btn {
  -webkit-appearance: none;
  border: 0;
  box-sizing: border-box;
  color: inherit;
  display: block;
  outline: 0;
  user-select: none;
  overflow: hidden;
  position: relative;
  text-align: center;
}

.btn::after {
  background-color: #fff;
  content: ' ';
  opacity: 0;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  position: absolute;
}

.btn:active::after {
  opacity: 0.2;
}

.chessboard-bg {
  background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAAXNSR0IArs4c6QAAADBJREFUOE9jfPXq1X8GPEBUVBSfNAPjqAHDIgz+//+PNx28fv0afzoYNYCBceiHAQAmfVXZBZk9KQAAAABJRU5ErkJggg==);
  // background: url(/img/pic/second/<EMAIL>);
  // background-size: cover;
}

// 粘性定位，暂时不要使用 by wanghui
// .sticky {
//     position: -webkit-sticky;
//     position: -moz-sticky;
//     position: -ms-sticky;
//     position: sticky;
// }
.fixed {
  position: fixed !important;
}

.center {
  // display: -webkit-box;
  // -webkit-box-align: center;
  // -webkit-box-pack: center;
  @include center;
}

.center2 {
  @include center2;
}

.center3 {
  @include center3;
}

.flex-center {
  @include flex-center;
}

.allcover {
  position: absolute;
  top: 0;
  right: 0;
}

.ct {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
}

.cl {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
}

.line-th {
  text-decoration: line-through;
}

.flex {
  @include flex();
}

.flex1 {
  @include flex-num(1);
}

.flex3 {
  @include flex-num(3);
}

.flex-center {
  @include flex-center;
}

.fl-left {
  float: left;
}

.fl-right {
  float: right;
}

.font-0 {
  font-size: 0;
}

.dotdotdot1 {
  @include dotdotdot1;
}

.dotdotdot2 {
  @include dotdotdot2;
}

.dotdotdot3 {
  @include dotdotdot2(3);
}

.dotdotdot4 {
  @include dotdotdot2(4);
}

.dotdotdot6 {
  @include dotdotdot2(6);
}

.dotdotdot8 {
  @include dotdotdot2(8);
}

.english-word-break {
  // 英文字符换行
  @include english-word-break;
}

.text-left {
  text-align: left;
}

.text-center {
  text-align: center;
}

.text-right {
  text-align: right;
}

.margin-top0 {
  margin-top: 0;
}

.margin-top10 {
  margin-top: 10px;
}

.margin-bt0 {
  margin-bottom: 0;
}

.padding-top10 {
  padding-top: 10px;
}

.margin-bt0 {
  margin-bottom: 0;
}

.border-bt0 {
  border-bottom: 0;
}

.border-tp0 {
  border-top: 0;
}

// my reset
.el-message {
  z-index: 3001 !important;
}
.el-overlay {
  background: rgba(255, 255, 255, 0.6) !important;
}
.el-dialog,
.el-message-box,
.el-container,
.vipPopover,
.customerPopover {
  border: 1px solid #d9d9d9 !important;
}
.el-container {
  border-radius: 8px;
}
.customStyleByMessageBox {
  position: relative;
  width: 424px !important;
  padding: 10px 5px 20px !important;
  .el-message-box__header {
    padding: 15px 15px 2px;
    .el-message-box__title {
      padding-left: 36px;
      line-height: 24px;
      font-size: 16px;
      font-weight: 600; // electron
      color: rgba(0, 0, 0, 0.85);
    }
    .el-message-box__close {
      position: absolute;
      top: 10px;
      right: 10px;
    }
  }
  .el-message-box__content {
    padding: 10px 15px;
    .el-message-box__container {
      font-size: 14px;
      font-weight: 400;
      color: rgba(0, 0, 0, 0.65);
      line-height: 22px;

      .el-message-box-icon--warning {
        // 换了名字
        position: absolute;
        top: 25px;

        color: transparent;
        background-image: url(/img/mind_map/icon/<EMAIL>);
        width: 24px;
        height: 24px;
        background-size: contain;
      }

      .el-message-box__message {
        padding-left: 36px;
        padding-right: 12px;
        word-break: break-word;
      }
    }
  }
  .el-message-box__btns {
    padding: 5px 15px 0;
    .el-button {
      height: 32px !important;
      padding: 0 15px !important;
      background: #ffffff !important;
      border-radius: 2px !important;
      border: 1px solid rgba(0, 0, 0, 0.15) !important;

      font-size: 14px !important;
      font-weight: 400 !important;
      color: rgba(0, 0, 0, 0.65) !important;
      line-height: 22px !important;

      &:hover {
        color: #409eff !important;
        border-color: #c6e2ff !important;
        background-color: #eff5ff !important;
        outline: 0 !important;
      }
    }
    .el-button--primary {
      height: 32px !important;
      background: #1890ff !important;
      border-radius: 2px !important;

      font-size: 14px !important;
      font-weight: 400 !important;
      color: #ffffff !important;
      line-height: 22px !important;

      &:hover {
        color: #fff !important;
        border-color: #79bbff !important;
        background-color: #79bbff !important;
        outline: 0 !important;
      }
    }
  }
}
.el-radio-group {
  flex-wrap: nowrap;
  height: 22px;
  line-height: 22px;

  .el-radio {
    height: 22px;
    line-height: 22px;
    margin-right: 36px;
    color: rgba(0, 0, 0, 0.65) !important;

    .el-radio__input {
      top: 0.5px;
    }

    .el-radio__input.is-checked + .el-radio__label {
      color: #333 !important;
    }

    .el-radio__label {
      padding-left: 8px;
    }

    .el-radio__inner {
      border-color: #d4d4d4;
    }

    .el-radio__input.is-checked .el-radio__inner {
      border-color: #0089fa;
      background: #fff;
    }

    .el-radio__inner::after {
      width: 8px;
      height: 8px;
      background-color: #0089fa;
    }
  }
  .el-radio.el-radio--large .el-radio__inner {
    width: 16px;
    height: 16px;
  }
}
