// format('woff2') format('woff2')
@font-face {
  font-family: '仓耳与墨';
  src: url('/fonts/仓耳与墨W02.ttf') format('truetype');
}
@font-face {
  font-family: '思源宋体';
  src: url('/fonts/思源宋体SC-Bold.woff2') format('opentype');
}
// @font-face {
//   font-family: '方正黑体';
//   src: url('/fonts/fz/方正黑体.ttf') format('truetype'),
// }
// @font-face {
//   font-family: '方正楷体';
//   src: url('/fonts/fz/方正楷体.ttf') format('truetype'),
// }
// @font-face {
//   font-family: '方正书宋简体';
//   src: url('/fonts/fz/方正书宋简体.ttf') format('truetype'),
// }
// @font-face {
//   font-family: '阿里妈妈刀隶体';
//   src: url('/fonts/ali/阿里妈妈刀隶体.otf') format('opentype');
// }
// @font-face {
//   font-family: '阿里妈妈东方大楷';
//   src: url('/fonts/ali/阿里妈妈东方大楷.otf') format('opentype');
// }
// @font-face {
//   font-family: '阿里妈妈方圆体';
//   src: url('/fonts/ali/阿里妈妈方圆体.ttf') format('truetype'),
// }
// @font-face {
//   font-family: '阿里妈妈灵动体';
//   src: url('/fonts/ali/阿里妈妈灵动体.ttf') format('truetype'),
// }
@font-face {
  font-family: '阿里巴巴普惠体';
  src: url('/fonts/ali/阿里巴巴普惠体.woff2') format('opentype');
}
@font-face {
  font-family: '阿里妈妈数黑体';
  src: url('/fonts/ali/阿里妈妈数黑体.otf') format('opentype');
}
@font-face {
  font-family: 'Remem';
  src: url('/fonts/Remem-Regular.ttf') format('truetype');
}
@font-face {
  font-family: 'Roboto';
  src: url('/fonts/Roboto-Regular.ttf') format('truetype');
}

// 202410
@font-face {
  font-family: '包图小白体';
  src: url('/fonts/202410/包图小白体.ttf') format('truetype');
}
@font-face {
  font-family: '钉钉进步体';
  src: url('/fonts/202410/钉钉进步体-Regular.ttf') format('truetype');
}
@font-face {
  font-family: '抖音美好体';
  src: url('/fonts/202410/抖音美好体.ttf') format('truetype');
}
@font-face {
  font-family: '荆南麦圆体';
  src: url('/fonts/202410/荆南麦圆体.ttf') format('truetype');
}
@font-face {
  font-family: '优设标题黑';
  src: url('/fonts/202410/优设标题黑.ttf') format('truetype');
}
