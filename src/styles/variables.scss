// base color
$blue: #324157;
$light-blue: #3a71a8;
$red: #c03639;
$pink: #e65d6e;
$green: #30b08f;
$tiffany: #4ab7bd;
$yellow: #fec171;
$panGreen: #30b08f;

// sidebar
$menuText: #000;
$menuActiveText: #FF5924;
$subMenuActiveText: #FF5924; // https://github.com/ElemeFE/element/issues/12951

$menuBg: #fff;
$menuHover: #fef7eb;

$subMenuBg: #fff;
$subMenuHover: #fef7eb;

$sideBarWidth: 210px;

// z-index
$zElement1: 1; // 页面元素：1-99
$zElement2: 2; // 页面元素：1-99
$zFixed1: 100; // fixed：100-199
$zPopup1: 200; // 弹框：200-299
$zToast1: 300; // Toast：300-399
$zCover: 1000; // 覆盖库层级的最高



:export {
  $my-menuText: $menuText;
  $my-menuActiveText: $menuActiveText;
  $my-subMenuActiveText: $subMenuActiveText;
  $my-menuBg: $menuBg;
  $my-menuHover: $menuHover;
  $my-subMenuBg: $subMenuBg;
  $my-subMenuHover: $subMenuHover;
  $my-sideBarWidth: $sideBarWidth;
  $z-element-1: $zElement1;
  $z-element-2: $zElement2;
  $z-fixed-1: $zFixed1;
  $z-popup-1: $zPopup1;
  $z-toast-1: $zToast1;
  $z-cover: $zCover;
}
