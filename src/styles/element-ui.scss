// cover some element-ui styles

.el-breadcrumb__inner,
.el-breadcrumb__inner a {
  font-weight: 400 !important;
}

.el-upload {
  input[type="file"] {
    display: none !important;
  }
}

.el-upload__input {
  display: none;
}

.cell {
  .el-tag {
    margin-right: 0;
  }
}

.small-padding {
  .cell {
    padding-right: 5px;
    padding-left: 5px;
  }
}

.fixed-width {
  .el-button--mini {
    min-width: 60px;
    padding: 7px 10px;
  }
}

.status-col {
  .cell {
    padding: 0 10px;
    text-align: center;

    .el-tag {
      margin-right: 0;
    }
  }
}

// page组件样式调整-version:2.1.9
.el-pagination.is-background .el-pager li:not(.is-disabled).is-active{
  color: #FEAF38 !important;
  background-color: #fff !important;
  border: 1px solid #FEAF38;
}

.el-pagination.is-background .el-pager li:not(.is-disabled):hover{
  color: #FEAF38 !important;
}

.el-pagination__editor.el-input .el-input__inner{
  padding: 0 !important;
}

// refine element ui upload
// .upload-container {
//   .el-upload {
//     width: 100%;

//     .el-upload-dragger {
//       width: 100%;
//       height: 200px;
//     }
//   }
// }

// dropdown
.el-dropdown-menu {
  a {
    display: block;
  }
}

// fix date-picker ui bug in filter-item
.el-range-editor.el-input__inner {
  display: inline-flex !important;
}

// to fix el-date-picker css style
.el-range-separator {
  box-sizing: content-box;
}

.el-popover.el-popper{
  width: auto;
  min-width: auto;
}

// el-date-picker type="datetime bug 内部选择框高度很小 version:2.1.9
.el-date-picker__editor-wrap .el-input .el-input__inner{
  height: 30px;
}


