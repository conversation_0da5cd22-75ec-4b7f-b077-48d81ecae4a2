@import './variables.scss';

@mixin colorBtn($color) {
  background: $color;

  &:hover {
    color: $color;

    &::before,
    &::after {
      background: $color;
    }
  }
}

.blue-btn {
  @include colorBtn($blue);
}

.light-blue-btn {
  @include colorBtn($light-blue);
}

.red-btn {
  @include colorBtn($red);
}

.pink-btn {
  @include colorBtn($pink);
}

.green-btn {
  @include colorBtn($green);
}

.tiffany-btn {
  @include colorBtn($tiffany);
}

.yellow-btn {
  @include colorBtn($yellow);
}

.pan-btn {
  position: relative;
  display: inline-block;
  padding: 14px 36px;
  font-size: 14px;
  color: #fff;
  border: none;
  border-radius: 8px;
  outline: none;
  transition: 600ms ease all;

  &:hover {
    background: #fff;

    &::before,
    &::after {
      width: 100%;
      transition: 600ms ease all;
    }
  }

  &::before,
  &::after {
    position: absolute;
    top: 0;
    right: 0;
    width: 0;
    height: 2px;
    content: '';
    transition: 400ms ease all;
  }

  &::after {
    top: inherit;
    right: inherit;
    bottom: 0;
    left: 0;
  }
}

.custom-button {
  display: inline-block;
  padding: 10px 15px;
  margin: 0;
  font-size: 14px;
  line-height: 1;
  color: #fff;
  text-align: center;
  white-space: nowrap;
  cursor: pointer;
  background: #fff;
  border-radius: 4px;
  outline: 0;
  box-sizing: border-box;
  -webkit-appearance: none;
}
