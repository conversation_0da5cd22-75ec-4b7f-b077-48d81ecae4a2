a, abbr, acronym, address, applet, article, aside, audio, b, big, blockquote, body, canvas, caption, center, cite, code, dd, del, details, dfn, div, dl, dt, em, embed, fieldset, figcaption, figure, footer, form, h1, h2, h3, h4, h5, h6, header, html, i, iframe, img, input, ins, kbd, label, legend, li, mark, menu, nav, object, ol, output, p, pre, q, ruby, s, samp, section, small, span, strike, strong, sub, summary, sup, table, tbody, td, tfoot, th, thead, time, tr, tt, u, ul, var, video {
  margin: 0;
  padding: 0;
  border: 0;
}

article, aside, details, figcaption, figure, footer, header, menu, nav, section {
  display: block
}

// body {
//   line-height: 1
// }

blockquote, q {
  quotes: none
}

blockquote:after, blockquote:before, q:after, q:before {
  content: none
}

table {
  border-collapse: collapse;
  border-spacing: 0
}

a {
  color: #7e8c8d;
  text-decoration: none;
  backface-visibility: hidden;
}

li {
  list-style: none
}

// ::-webkit-scrollbar {
//   width: 5px;
//   height: 5px;
// }

// ::-webkit-scrollbar-track-piece {
//   background-color: #fff;
//   border-radius: 6px;
// }

// ::-webkit-scrollbar-thumb:vertical {
//   height: 5px;
//   background-color: #fff;
//   border-radius: 6px;
// }

// ::-webkit-scrollbar-thumb:horizontal {
//   width: 5px;
//   background-color: #fff;
//   border-radius: 6px;
// }

body, html {
  width: 100%
}

body {
  -webkit-text-size-adjust: none;
  -webkit-tap-highlight-color: transparent;
  background-color: #fff
}