<template>
  <el-config-provider>
    <router-view />
  </el-config-provider>
  <VipDialog></VipDialog>
  <PayDialog></PayDialog>
</template>
<script lang="ts" setup>
import { useCommonStore } from '@/pinia/common'
// import VipDialog from '@/components/Pay/VipDialog.vue'
import VipDialog from '@/components/Pay/VipDialogSimple.vue'
import PayDialog from '@/components/Pay/PayDialog.vue'
// import { KeyBoard } from '@/utils/board'

// 初始化输出文件路径
const commonStore = useCommonStore()
commonStore.getPath()

// onMounted(() => {
//   new KeyBoard()
// })

// <SwipeItem :swipe="swipe"></SwipeItem>
// const store = useStore()
// const swipe = computed(() => {
//   // console.log('app：', JSON.stringify(store.state.swipe))
//   return store.state.swipe
// })
</script>
