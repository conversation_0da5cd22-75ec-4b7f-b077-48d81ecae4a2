import { defineStore } from 'pinia'
import { messageSimple } from '@/utils/message'
// import { ElMessage, ElMessageBox } from 'element-plus'
import { useCommonStore } from '@/pinia/common'
import { getImageInfo, webUpload, fileToBase64ByReader } from '@/utils'
import { mapToArray } from '@/utils/util'
import { pageType } from '@/utils/enum'
import { chooseWebImages, dragHandler, onDragChange } from '@/utils/drag'
import { imgWatermarkExts } from '@/utils/enum/configData'
import { goLogin, goPay } from '@/utils/user'
import { getListAll, getListAllByBackground } from '@/api/batch'
import type Cropper from 'cropperjs'

let currentImgSize = 0
export const usePassportStore = defineStore({
  id: 'passport',
  state: () => ({
    imgsMap: new Map(),
    currentIndex: 0,
    categoryList: [] as any, // 尺寸列表
    categoryBackgroundList: [] as any, // 背景列表
    currentSegment: '换背景', // 当前segment：'手动精修', '换背景', '改尺寸'
    // changeSegmenting: false, // 是否正在切换 segment
    segments: [
      '手动精修',
      '换背景',
      '改尺寸'
    ],
    segmentsType: { custom: '手动精修', changeBackground: '换背景', changeSize: '改尺寸' },
    scaleFactor: 2, // 按scaleFactor进行放大（待定）
    currentImg: {
      // 第一层 为水印，不需要变量承接
      mattingImage: '', // 第二层 抠图后的图片
      // 第三层  #3e90e5  #ed0e1a
      backgroundColor: '', // 抠图后添加的背景色 backgroundColor backgroundImage 互斥
      backgroundImage: '', // 抠图后添加的背景图片 backgroundColor backgroundImage 互斥
      mattingCropImage: '', // 待裁剪图片：一二三层拼合后的图片，手动进行裁剪
      mattingCropedImage: '', // 最终裁剪后的图片

      record: {
        // transform: `rotate(${currentImg.record.rotate}deg) scaleX(${currentImg.record.scaleX}) scaleY(${currentImg.record.scaleY})`,
        opacity: 100, // 透明度，0-100
        rotate: 0, // 旋转，0-360
        zoom: 1, // 缩放（兼容旧代码）
        scaleX: 1, // X轴缩放
        scaleY: 1, // Y轴缩放
        transformX: 1, // 左右翻转
        transformY: 1, // 上下翻转
        translate: { x: 0, y: 0 }, // 位移
        // sizeType: 'customSize',
        // customWidth: '',
        // customHeight: '',
        width: 0,
        height: 0,
      },
    } as any,
    // 手动精修相关状态
    brushType: 'keep', // 画笔类型：保留(keep)或擦除(erase)
    brushSize: 20, // 画笔大小
    edgeSoftness: 0, // 边缘柔和度
    maskHistory: [] as string[], // 蒙版历史记录
    maskHistoryIndex: -1, // 当前历史记录索引
    maskZoom: 1, // 蒙版缩放比例
    maskDragMode: false, // 是否处于拖动模式
    manualRefineCanvas: null as HTMLCanvasElement | null, // 显示画布 - 显示原图
    redMaskCanvas: null as HTMLCanvasElement | null, // 操作画布 - 用于红色蒙层操作
    redMaskCanvasContext: null as CanvasRenderingContext2D | null, // 缓存操作画布的上下文
    manualRefineBoxRef: null as any, // 手动精修组件引用
    backgroundBoxRef: null as any, // 换背景组件引用
    resizeBoxRef: null as any, // 改尺寸组件引用
    cropper: null as Cropper | null,
    cropperRef: null as any,
    option: {
      guides: false, // 是否显示网格线
      movable: false, // 是否可以移动图片
      zoomable: false, // 是否可以缩放图片（以图片左上角为原点进行缩放）
      // 当 autoCrop属性 被设置为 true时, crop 事件将在 ready 事件之前被触发.
      // 当设置了 data 属性时, 另一个 crop 事件将在 ready 事件之前被触发.
      autoCrop: true, // 是否自动裁剪
      autoCropArea: 1, // 默认裁剪区域大小，值为0-1，表示裁剪框占图片的比例，1表示裁剪框与图片一样大
      aspectRatio: 89 / 127, // 初始化比例
      cropBoxMovable: true, // 是否允许通过拖动来移动裁剪框
      cropBoxResizable: false, // 是否允许通过拖动来调整裁剪框的大小
      // 'crop': 创建一个新的裁剪框
      // 'move': 图片容器可移动
      // 'none': 什么也不做
      dragMode: 'none',
      // 0: 没有限制
      // 1: 限制裁剪框不超过图片容器的范围。
      // 2: 限制最片容器尺寸以在裁剪容器中展示。 如果图片容器和裁剪容器的比例不同，则图片容器以cover模式填充（图片容器保持原有比例，最长边和裁剪容器大小一致，短边等比缩放，可能会有部分区域不可见)。
      // 3: 限制图片容器尺寸以在裁剪器中展示。 如果图片容器和裁剪容器的比例不同，则图片容器以contain模式填充（图片容器保持原有比例，最短边和裁剪容器大小一直，长边等比缩放，可能会有留白）。
      viewMode: 2,
      minCropBoxWidth: 25,
      minCropBoxHeight: 25,
    },
    pageType: '',
    applyToAll: false,
    loading: false,
    showSearchResult: true,
    isOrigin: false, // 是否是原图
    showEditDialog: false,
    // 排版照相关状态
    selectedPaperSize: '5寸', // 选中的相纸尺寸
    selectedOtherSize: '', // 选中的其他尺寸
    layoutRows: 2, // 排版行数
    layoutCols: 5, // 排版列数
  }),
  getters: {},
  actions: {
    // 手动精修相关方法
    updateBrushType(type: string) {
      console.log('[MattingStore] 更新画笔类型:', type)
      this.brushType = type
      console.log('[MattingStore] 画笔类型已更新为:', this.brushType)
    },

    updateBrushSize(size: number) {
      console.log('[MattingStore] 更新画笔大小:', size)
      this.brushSize = size
      console.log('[MattingStore] 画笔大小已更新为:', this.brushSize)
    },

    updateEdgeSoftness(softness: number) {
      console.log('[MattingStore] 更新边缘柔和度:', softness)
      this.edgeSoftness = softness
      console.log('[MattingStore] 边缘柔和度已更新为:', this.edgeSoftness)
    },

    // 添加蒙版历史记录
    addMaskHistory(maskData?: string) {
      if (!this.redMaskCanvas) return
      console.log('[MattingStore] 添加蒙版历史记录, 当前索引:', this.maskHistoryIndex, '历史长度:', this.maskHistory.length)

      // 将当前操作画布状态转换为数据URL
      maskData = maskData || this.redMaskCanvas.toDataURL('image/png')

      // 如果当前不是最新状态，则删除当前位置之后的所有历史
      if (this.maskHistoryIndex < this.maskHistory.length - 1) {
        const removedCount = this.maskHistory.length - 1 - this.maskHistoryIndex
        this.maskHistory = this.maskHistory.slice(0, this.maskHistoryIndex + 1)
        console.log('[MattingStore] 删除了', removedCount, '条后续历史记录')
      }

      // 添加新的历史记录
      this.maskHistory.push(maskData)
      this.maskHistoryIndex = this.maskHistory.length - 1
      console.log('[MattingStore] 蒙版历史记录已添加, 新索引:', this.maskHistoryIndex, '新长度:', this.maskHistory.length)
    },

    // 应用历史记录中的蒙版（由 Matting/index.vue 处理）
    applyMaskHistory(index: number) {
      if (!this.redMaskCanvas || !this.redMaskCanvasContext) return
      console.log('[Matting/index] 应用历史记录中的蒙版, 索引:', index, '历史长度:', this.maskHistory.length)

      // 确保索引有效
      if (index < 0 || index >= this.maskHistory.length) {
        console.warn('[Matting/index] 无效的历史记录索引:', index)
        return
      }

      // 从store获取历史记录
      const maskData = this.maskHistory[index]
      if (!maskData) {
        console.warn('[Matting/index] 历史记录数据为空, 索引:', index)
        return
      }

      // 创建图像对象加载历史记录
      const img = new Image()
      img.onload = () => {
        // 清空当前操作画布
        this.redMaskCanvasContext!.clearRect(0, 0, this.redMaskCanvas!.width, this.redMaskCanvas!.height)

        // 绘制历史记录到操作画布
        this.redMaskCanvasContext!.drawImage(img, 0, 0)

        // 更新store中的历史索引
        this.maskHistoryIndex = index

        console.log('[Matting/index] 历史记录已应用, 当前索引:', index)

        // 调用 处理蒙版图片抠图更新 mattingImage、mattingCropImage 事件的防抖函数
        const event = new CustomEvent('mattingImageUpdated')
        window.dispatchEvent(event)
        this.createMattingCropImage()
      }
      img.onerror = () => {
        console.error('[Matting/index] 历史记录图像加载失败, 索引:', index)
      }
      img.src = maskData
    },

    // 重置蒙版（清空历史记录）
    resetMask() {
      if (!this.redMaskCanvas || !this.redMaskCanvasContext) return
      console.log('[MattingStore] 重置蒙版')

      // 清空历史记录
      this.maskHistory = [this.maskHistory[0]]
      this.maskHistoryIndex = 0
      this.applyMaskHistory(0) // 应用第一个历史记录
      console.log('[MattingStore] 蒙版已重置, 历史记录已清空')
    },

    // 缩放蒙版
    zoomMask(scale: number) {
      const newZoom = Math.max(0.4, Math.min(5, this.maskZoom + scale)) // 限制缩放范围在 0.4 到 5 之间
      this.maskZoom = newZoom
      console.log('[MattingStore] 蒙版缩放已更新为:', this.maskZoom)

      // 触发画布重绘 - 通过更新相关的 DOM 样式
      this.updateCanvasZoom()
    },

    // 重置缩放
    resetZoomMask() {
      console.log('[MattingStore] 重置蒙版缩放, 当前缩放:', this.maskZoom)
      this.maskZoom = 1
      console.log('[MattingStore] 蒙版缩放已重置为:', this.maskZoom)

      // 触发画布重绘
      this.updateCanvasZoom()
    },

    // 更新画布缩放（现在由 Moveable 控制，这里只更新样式类）
    updateCanvasZoom() {
      console.log('[MattingStore] 更新画布缩放, 当前缩放:', this.maskZoom)

      // 查找手动精修画布元素，只更新样式类，不操作 transform
      const canvas = document.querySelector('.manual-refine-canvas') as HTMLCanvasElement
      if (canvas) {
        // 根据拖动模式更新样式类
        if (this.maskDragMode) {
          canvas.classList.add('drag-mode')
        } else {
          canvas.classList.remove('drag-mode')
        }

        console.log('[MattingStore] 画布样式类已更新, 拖动模式:', this.maskDragMode)
      } else {
        console.warn('[MattingStore] 未找到手动精修画布元素')
      }

      // 同时更新 origin-box 的样式类
      const originBox = document.querySelector('.origin-box') as HTMLElement
      if (originBox) {
        if (this.maskDragMode) {
          originBox.style.cursor = 'grab'
        } else {
          originBox.style.cursor = 'default'
        }
        console.log('[MattingStore] origin-box 样式已更新')
      }
    },

    // ================ 共享的尺寸和变换方法 ================

    // 获取背景盒子的尺寸
    getBackgroundBoxSize() {
      console.log('[MattingStore] getBackgroundBoxSize')
      const backgroundBox = document.querySelector('.background-box')
      if (backgroundBox) {
        const backgroundBoxSize = {
          width: Math.round(backgroundBox.clientWidth),
          height: Math.round(backgroundBox.clientHeight),
        }
        // console.log('[MattingStore] Background box size:', backgroundBoxSize)

        // 更新抠图图片尺寸
        this.updateMattingImageSize(backgroundBoxSize)
        return backgroundBoxSize
      }
      return null
    },

    // 更新抠图、背景图的尺寸
    updateMattingImageSize(backgroundBoxSize: { width: number; height: number }): Promise<void> {
      if (!backgroundBoxSize.width || !backgroundBoxSize.height) {
        return Promise.resolve() // 如果没有有效的背景盒子尺寸，立即resolve
      }

      return new Promise<void>((resolve) => {
        // 如果存在背景图片，先计算背景图片的尺寸
        if (this.currentImg?.backgroundImage) {
          // 先计算背景图片的尺寸
          const bgImg = new Image()
          bgImg.onload = () => {
            const bgWidth = bgImg.width
            const bgHeight = bgImg.height
            const bgAspectRatio = bgWidth / bgHeight

            // 计算背景图片的尺寸
            let backgroundImageWidth, backgroundImageHeight

            // 确保背景图片可以撑满背景盒子
            if (bgWidth >= bgHeight) {
              // 当背景图片宽大于等于高时，宽度等于背景盒子宽度，高度按比例计算
              backgroundImageWidth = Math.round(backgroundBoxSize.width)
              backgroundImageHeight = Math.round(backgroundImageWidth / bgAspectRatio)
            } else {
              // 当背景图片宽小于高时，高度等于背景盒子高度，宽度按比例计算
              backgroundImageHeight = Math.round(backgroundBoxSize.height)
              backgroundImageWidth = Math.round(backgroundImageHeight * bgAspectRatio)
            }

            // 修正
            // 确保 背景图片尺寸 不超过 背景盒子 尺寸
            if (backgroundImageHeight > backgroundBoxSize.height) {
              backgroundImageHeight = Math.round(backgroundBoxSize.height)
              backgroundImageWidth = Math.round(backgroundImageHeight * bgAspectRatio)
            }
            if (backgroundImageWidth > backgroundBoxSize.width) {
              backgroundImageWidth = Math.round(backgroundBoxSize.width)
              backgroundImageHeight = Math.round(backgroundImageWidth / bgAspectRatio)
            }

            console.log('[MattingStore] Background image size:', {
              width: backgroundImageWidth,
              height: backgroundImageHeight,
            })

            // 设置背景图片的尺寸
            const backgroundImage = document.querySelector('.background-image')
            if (backgroundImage) {
              backgroundImage.setAttribute(
                'style',
                `width: ${backgroundImageWidth}px; height: ${backgroundImageHeight}px;`
              )
            }

            // 如果有 mattingImage，计算它的尺寸
            if (this.currentImg?.mattingImage) {
              const mattingImg = new Image()
              mattingImg.onload = () => {
                const mattingWidth = mattingImg.width
                const mattingHeight = mattingImg.height
                const mattingAspectRatio = mattingWidth / mattingHeight

                // 计算 matting-image 的尺寸
                let newWidth, newHeight

                // 当 background-image 的宽小于 matting-image 宽时
                if (backgroundImageWidth < mattingWidth) {
                  newWidth = backgroundImageWidth
                  newHeight = Math.round(newWidth / mattingAspectRatio)
                  console.log('[MattingStore] background-image width < matting-image width, adjusting matting-image width')
                }
                // 当 background-image 的高小于 matting-image 高时
                else if (backgroundImageHeight < mattingHeight) {
                  newHeight = backgroundImageHeight
                  newWidth = Math.round(newHeight * mattingAspectRatio)
                  console.log('[MattingStore] background-image height < matting-image height, adjusting matting-image height')
                }
                // 如果背景图片尺寸都大于 matting-image，保持 matting-image 原始尺寸
                else {
                  newWidth = mattingWidth
                  newHeight = mattingHeight
                  console.log('[MattingStore] background-image size >= matting-image size, keeping original matting-image size')
                }

                // 修正
                // 确保 抠图尺寸 不超过 背景图片 尺寸
                if (newWidth > backgroundImageWidth) {
                  newWidth = backgroundImageWidth
                  newHeight = Math.round(newWidth / mattingAspectRatio)
                }
                if (newHeight > backgroundImageHeight) {
                  newHeight = backgroundImageHeight
                  newWidth = Math.round(newHeight * mattingAspectRatio)
                }

                const mattingImageSize = {
                  width: newWidth,
                  height: newHeight,
                }

                // console.log('[MattingStore] Matting image size:', {
                //   width: Math.round(mattingImageSize.width),
                //   height: Math.round(mattingImageSize.height),
                // })

                // 应用新尺寸到 background-container 元素
                this.applyMattingImageSize(mattingImageSize)

                // 设置 matting-image 的具体像素值和居中样式
                const mattingImage = document.querySelector('.matting-image')
                if (mattingImage) {
                  const left = Math.round((backgroundImageWidth - newWidth) / 2)
                  const top = Math.round((backgroundImageHeight - newHeight) / 2)

                  const currentStyle = mattingImage.getAttribute('style') || ''
                  const mattingStyleWithoutDimensions = currentStyle.replace(
                    /left:\s*[^;]+;?|top:\s*[^;]+;?|width:\s*[^;]+;?|height:\s*[^;]+;?/g,
                    ''
                  )
                  mattingImage.setAttribute(
                    'style',
                    `left: ${left}px; top: ${top}px; width: ${Math.round(
                      newWidth
                    )}px; height: ${Math.round(
                      newHeight
                    )}px; ${mattingStyleWithoutDimensions}`
                  )
                }

                // 所有处理完成，resolve Promise
                resolve()
              }
              mattingImg.onerror = () => {
                console.error('[MattingStore] Failed to load matting image')
                resolve() // 即使加载失败也resolve，以避免Promise挂起
              }
              mattingImg.src = this.currentImg.mattingImage
            } else {
              resolve() // 如果没有mattingImage，直接resolve
            }
          }
          bgImg.onerror = () => {
            console.error('[MattingStore] Failed to load background image')
            resolve() // 即使加载失败也resolve，以避免Promise挂起
          }
          bgImg.src = this.currentImg.backgroundImage
        }
        // 如果没有背景图片，使用原来的逻辑
        else if (this.currentImg?.mattingImage) {
          // 创建一个图像对象来获取原始尺寸
          const img = new Image()
          img.onload = () => {
            const imgWidth = img.width
            const imgHeight = img.height
            const aspectRatio = imgWidth / imgHeight

            // 计算新的尺寸，精确到整数位
            let newWidth, newHeight

            if (imgWidth >= imgHeight) {
              // 当图片宽大于等于高时，宽度等于背景盒子宽度，高度按比例计算
              newWidth = Math.round(backgroundBoxSize.width)
              newHeight = Math.round(newWidth / aspectRatio)

              // 确保高度不超过背景盒子高度
              if (newHeight > backgroundBoxSize.height) {
                console.log('[MattingStore] Adjusting height to fit within background-box')
                newHeight = Math.round(backgroundBoxSize.height)
                newWidth = Math.round(newHeight * aspectRatio)
              }
            } else {
              // 当图片宽小于高时，高度等于背景盒子高度，宽度按比例计算
              newHeight = Math.round(backgroundBoxSize.height)
              newWidth = Math.round(newHeight * aspectRatio)

              // 确保宽度不超过背景盒子宽度
              if (newWidth > backgroundBoxSize.width) {
                console.log('[MattingStore] Adjusting width to fit within background-box')
                newWidth = Math.round(backgroundBoxSize.width)
                newHeight = Math.round(newWidth / aspectRatio)
              }
            }

            // 更新尺寸
            const mattingImageSize = {
              width: newWidth,
              height: newHeight,
            }
            // console.log('[MattingStore] Matting image size:', {
            //   width: Math.round(mattingImageSize.width),
            //   height: Math.round(mattingImageSize.height),
            // })

            // 应用新尺寸到 background-container 元素
            this.applyMattingImageSize(mattingImageSize)

            // 所有处理完成，resolve Promise
            resolve()
          }
          img.onerror = () => {
            console.error('[MattingStore] Failed to load matting image')
            resolve() // 即使加载失败也resolve，以避免Promise挂起
          }
          img.src = this.currentImg.mattingImage
        } else {
          resolve() // 如果既没有背景图片也没有mattingImage，直接resolve
        }
      })
    },

    // 应用抠图图片的尺寸到 DOM
    applyMattingImageSize(mattingImageSize: { width: number; height: number }) {
      const backgroundContainer = document.querySelector('.background-container')
      if (backgroundContainer && this.currentImg?.backgroundImage) {
        return backgroundContainer.setAttribute('style', '') // 清除之前的样式
      }

      // 如果没有背景图片，保持现有逻辑不变
      if (
        backgroundContainer &&
        mattingImageSize.width &&
        mattingImageSize.height
      ) {
        // 设置 background-container 的尺寸，确保是整数
        const existingStyle = backgroundContainer.getAttribute('style') || ''
        const styleWithoutDimensions = existingStyle.replace(
          /width:\s*[^;]+;?|height:\s*[^;]+;?/g,
          ''
        )
        const roundedWidth = Math.round(mattingImageSize.width)
        const roundedHeight = Math.round(mattingImageSize.height)
        backgroundContainer.setAttribute(
          'style',
          `width: ${roundedWidth}px; height: ${roundedHeight}px; ${styleWithoutDimensions}`
        )

        // 如果有 matting-image 元素，但没有背景图片，使用百分比尺寸
        const mattingImage = document.querySelector('.matting-image')
        if (mattingImage) {
          const currentStyle = mattingImage.getAttribute('style') || ''
          const mattingStyleWithoutDimensions = currentStyle.replace(
            /left:\s*[^;]+;?|top:\s*[^;]+;?|width:\s*[^;]+;?|height:\s*[^;]+;?/g,
            ''
          )
          mattingImage.setAttribute(
            'style',
            `left: auto; top: auto; width: 100%; height: 100%; ${mattingStyleWithoutDimensions}`
          )
        }
      }
    },

    // 计算画布尺寸，使其与 matting-image 保持一致
    calculateCanvasSize() {
      console.log('[MattingStore] 开始计算画布尺寸')

      if (!this.currentImg?.mattingImage) {
        console.warn('[MattingStore] 无法计算画布尺寸，缺少抠图图像')
        return null
      }

      // 获取容器尺寸
      const container = document.querySelector('.manual-refine-box') as HTMLElement
      if (!container) {
        console.warn('[MattingStore] 无法找到容器元素')
        return null
      }

      const containerRect = container.getBoundingClientRect()
      console.log('[MattingStore] 容器尺寸:', containerRect.width, 'x', containerRect.height)

      return new Promise<{ width: number; height: number }>((resolve, reject) => {
        // 加载图像获取原始尺寸
        const img = new Image()
        img.crossOrigin = 'anonymous'
        img.onload = () => {
          console.log('[MattingStore] 图像原始尺寸:', img.width, 'x', img.height)

          // == 根据图片宽高缩放到容器尺寸 ==
          // 计算缩放比例，保持宽高比
          const scaleX = containerRect.width / img.width
          const scaleY = containerRect.height / img.height
          const scale = Math.min(scaleX, scaleY)
          // 计算显示尺寸
          const displayWidth = Math.round(img.width * scale)
          const displayHeight = Math.round(img.height * scale)
          console.log('[MattingStore] 计算得到的显示尺寸:', displayWidth, 'x', displayHeight)

          const canvasSize = {
            width: displayWidth,
            height: displayHeight
          }

          console.log('[MattingStore] 画布尺寸计算完成')
          resolve(canvasSize)
        }
        img.onerror = () => {
          console.error('[MattingStore] 图像加载失败，无法计算尺寸')
          reject(new Error('图像加载失败'))
        }
        img.src = this.currentImg.mattingImage
      })
    },

    // 创建抠图后的图片与背景的组合图
    createMattingCropImage() {
      if (!this.currentImg || !this.currentImg.mattingImage) {
        console.error('[MattingStore] No matting image available')
        return
      }

      console.log('[MattingStore] createMattingCropImage')
      // 创建一个新的 Image 对象来加载抠图图片
      const mattingImg = new Image()
      mattingImg.crossOrigin = 'anonymous'
      mattingImg.onload = () => {
        if (this.currentImg.backgroundImage) {
          // 如果有背景图片，使用DOM元素的尺寸和位置
          // 获取背景图片和抠图图片的DOM元素
          const backgroundImageEl = document.querySelector('.background-image')
          const mattingImageEl = document.querySelector('.matting-image')

          if (backgroundImageEl && mattingImageEl) {
            // 使用 getComputedStyle 获取实际应用的样式值
            const bgComputedStyle = window.getComputedStyle(backgroundImageEl)
            const mattingComputedStyle = window.getComputedStyle(mattingImageEl)

            // 获取背景图片的尺寸
            const backgroundImageWidth =
              parseInt(bgComputedStyle.width) || backgroundImageEl.clientWidth || 0
            const backgroundImageHeight =
              parseInt(bgComputedStyle.height) ||
              backgroundImageEl.clientHeight ||
              0

            // 获取抠图图片的尺寸和位置
            const mattingWidth =
              parseInt(mattingComputedStyle.width) ||
              mattingImageEl.clientWidth ||
              0
            const mattingHeight =
              parseInt(mattingComputedStyle.height) ||
              mattingImageEl.clientHeight ||
              0
            const left = parseInt(mattingComputedStyle.left) || 0
            const top = parseInt(mattingComputedStyle.top) || 0

            console.log('[MattingStore] DOM elements dimensions:', {
              backgroundImageWidth,
              backgroundImageHeight,
              mattingWidth,
              mattingHeight,
              left,
              top,
            })

            // 检查尺寸是否有效
            if (
              backgroundImageWidth <= 0 ||
              backgroundImageHeight <= 0 ||
              mattingWidth <= 0 ||
              mattingHeight <= 0
            ) {
              console.error(
                '[MattingStore] Invalid dimensions detected, falling back to original logic'
              )
              return
            }

            // 创建画布，使用背景图片的尺寸，但分辨率提高一倍以提升图片质量
            const canvas = document.createElement('canvas')
            // 将画布尺寸设置为背景图片尺寸的scaleFactor倍，以提高图片质量
            canvas.width = backgroundImageWidth * this.scaleFactor
            canvas.height = backgroundImageHeight * this.scaleFactor
            const ctx = canvas.getContext('2d')

            if (!ctx) {
              console.error('[MattingStore] Failed to get canvas context')
              return
            }

            // 设置画布的缩放比例
            ctx.scale(this.scaleFactor, this.scaleFactor)

            // 先绘制背景图片
            const bgImg = new Image()
            bgImg.crossOrigin = 'anonymous'
            bgImg.onload = () => {
              // 由于我们已经对整个画布进行了 scaleFactor 的缩放，这里的尺寸使用原始值
              ctx.drawImage(
                bgImg,
                0,
                0,
                backgroundImageWidth,
                backgroundImageHeight
              )

              // 保存当前状态
              ctx.save()

              // 提取变换值
              let translateX = 0, translateY = 0, rotate = 0, scaleX = 1, scaleY = 1, opacity = 1, transformX = 1, transformY = 1

              // 从 record 中获取变换值
              if (this.currentImg.record) {
                translateX = this.currentImg.record.translate?.x || 0
                translateY = this.currentImg.record.translate?.y || 0
                rotate = this.currentImg.record.rotate || 0
                scaleX = this.currentImg.record.scaleX || 1
                scaleY = this.currentImg.record.scaleY || 1
                opacity = this.currentImg.record.opacity || 1
                transformX = this.currentImg.record.transformX || 1
                transformY = this.currentImg.record.transformY || 1
              }

              // 设置变换中心点为抠图图片的中心
              const centerX = left + mattingWidth / 2
              const centerY = top + mattingHeight / 2

              // 应用变换
              ctx.translate(centerX, centerY)
              ctx.rotate((rotate * Math.PI) / 180)
              ctx.scale(scaleX * transformX, scaleY * transformY) // 包含翻转
              ctx.translate(translateX, translateY)

              // 设置透明度
              ctx.globalAlpha = opacity

              // 绘制抠图图片，以中心点为基准
              ctx.drawImage(
                mattingImg,
                -mattingWidth / 2,
                -mattingHeight / 2,
                mattingWidth,
                mattingHeight
              )

              // 恢复之前保存的状态
              ctx.restore()

              const cropImage = canvas.toDataURL('image/png', 0.95)
              if (cropImage === this.currentImg.mattingCropImage) return
              this.resizeBoxRef?.clearCropper() // 调用 ResizeBox 组件的 clearCropper 方法
              this.currentImg.mattingCropImage = cropImage // 将组合后的图片保存到 store
            }
            bgImg.src = this.currentImg.backgroundImage
            return
          }
        } else {
          // 如果没有背景图片或者无法获取DOM元素
          // 获取 matting-image DOM 元素的实际显示尺寸和位置
          const mattingImageEl = document.querySelector('.matting-image')
          let canvasWidth = mattingImg.width
          let canvasHeight = mattingImg.height
          let mattingDisplayWidth = mattingImg.width
          let mattingDisplayHeight = mattingImg.height
          let mattingLeft = 0
          let mattingTop = 0

          if (mattingImageEl) {
            const mattingComputedStyle = window.getComputedStyle(mattingImageEl)
            mattingDisplayWidth = parseInt(mattingComputedStyle.width) || mattingImageEl.clientWidth || mattingImg.width
            mattingDisplayHeight = parseInt(mattingComputedStyle.height) || mattingImageEl.clientHeight || mattingImg.height
            mattingLeft = parseInt(mattingComputedStyle.left) || 0
            mattingTop = parseInt(mattingComputedStyle.top) || 0

            // 获取容器尺寸作为画布尺寸
            const backgroundContainer = document.querySelector('.background-container')
            if (backgroundContainer) {
              const containerStyle = window.getComputedStyle(backgroundContainer)
              canvasWidth = parseInt(containerStyle.width) || backgroundContainer.clientWidth || mattingImg.width
              canvasHeight = parseInt(containerStyle.height) || backgroundContainer.clientHeight || mattingImg.height
            }

            // console.log('[MattingStore] No background image - using DOM dimensions:', {
            //   canvasWidth,
            //   canvasHeight,
            //   mattingDisplayWidth,
            //   mattingDisplayHeight,
            //   mattingLeft,
            //   mattingTop,
            // })
          }

          // 创建画布
          const canvas = document.createElement('canvas')
          canvas.width = canvasWidth * this.scaleFactor
          canvas.height = canvasHeight * this.scaleFactor
          const ctx = canvas.getContext('2d')

          if (!ctx) {
            console.error('[MattingStore] Failed to get canvas context')
            return
          }

          // 设置画布的缩放比例
          ctx.scale(this.scaleFactor, this.scaleFactor)

          // 保存当前的变换状态
          ctx.save()

          // 如果有背景色，先填充背景色
          if (
            this.currentImg.backgroundColor &&
            this.currentImg.backgroundColor !== 'transparent'
          ) {
            ctx.fillStyle = this.currentImg.backgroundColor
            ctx.fillRect(0, 0, canvasWidth, canvasHeight)
          }

          // 设置抠图图片的位置和变换
          // 如果有translate值，则使用translate值
          let translateX = 0, translateY = 0, rotate = 0, scaleX = 1, scaleY = 1, opacity = 1, transformX = 1, transformY = 1

          if (this.currentImg.record) {
            translateX = this.currentImg.record.translate?.x || 0
            translateY = this.currentImg.record.translate?.y || 0
            rotate = this.currentImg.record.rotate || 0
            scaleX = this.currentImg.record.scaleX || 1
            scaleY = this.currentImg.record.scaleY || 1
            opacity = this.currentImg.record.opacity || 1 // 转换为0-1范围
            transformX = this.currentImg.record.transformX || 1
            transformY = this.currentImg.record.transformY || 1
          }

          // 设置变换中心点
          const centerX = mattingLeft + mattingDisplayWidth / 2
          const centerY = mattingTop + mattingDisplayHeight / 2

          // 应用变换
          ctx.translate(centerX, centerY)
          ctx.rotate((rotate * Math.PI) / 180)
          ctx.scale(scaleX * transformX, scaleY * transformY) // 包含翻转
          ctx.translate(translateX, translateY)

          // 设置透明度
          ctx.globalAlpha = opacity

          // 绘制抠图图片
          ctx.drawImage(
            mattingImg,
            -mattingDisplayWidth / 2,
            -mattingDisplayHeight / 2,
            mattingDisplayWidth,
            mattingDisplayHeight
          )

          // 恢复之前保存的状态
          ctx.restore()

          const cropImage = canvas.toDataURL('image/png', 0.95)
          if (cropImage === this.currentImg.mattingCropImage) return
          this.resizeBoxRef?.clearCropper() // 调用 ResizeBox 组件的 clearCropper 方法
          this.currentImg.mattingCropImage = cropImage // 将组合后的图片保存到 store
        }
      }
      mattingImg.onerror = (error) => {
        console.error('[MattingStore] Failed to load matting image:', error)
      }
      mattingImg.src = this.currentImg.mattingImage
    },

    // 切换拖动模式
    toggleDragMode(isDragging: boolean) {
      console.log('[MattingStore] 切换拖动模式:', isDragging)
      this.maskDragMode = isDragging
      console.log('[MattingStore] 拖动模式已更新为:', this.maskDragMode)

      // 更新画布样式以反映拖动模式的变化
      this.updateCanvasZoom()
    },

    init(name: any) {
      // this.loading = false
      this.pageType = pageType[name as keyof typeof pageType]
      this.bindEvent()
      setTimeout(this.dragHandler, 1000)
    },
    bindEvent() {
      // 选择文件或图片结果返回
      this.unBindEvent()
      window.ipcRendererApi?.on('select-callback', () => (this.loading = true))
    },
    unBindEvent() {
      window.ipcRendererApi?.removeAllListeners('select-callback')
    },
    clear() {
      this.imgsMap.clear()
    },
    /** 获取拖拽的文件 */
    dragHandler() {
      dragHandler(this.imageInfoHandler)
    },
    /** 获取拖拽的文件夹 */
    onDragChange(e: any) {
      onDragChange(e, this.imageInfoHandler)
    },
    customWebUpload(e: any) {
      this.loading = true
      webUpload(e, 'customMattingInput')
    },
    customChooseWebImages(res: any) {
      this.loading = false
      console.log(res.target.files)
      // TODO 上传图片到阿里云，并添加到 customArr 里面
      // 临时使用本地图片
      fileToBase64ByReader(res.target.files[0], (base64: any) => {
        this.currentImg.backgroundImage = base64
      })
    },
    /** web端选择图片 */
    chooseWebImages(choose: any = {}) {
      this.loading = true
      this.currentSegment = this.segmentsType.changeBackground
      // this.imgsMap.clear() // 清空所有图片，仅支持单张
      chooseWebImages(choose, this.imageInfoHandler)
    },
    getCurrent() {
      const mapArray = Array.from(this.imgsMap.entries())
      if (!mapArray.length) return

      const [, target] = mapArray[this.currentIndex]
      this.currentImg = target

      return target
    },
    /** 获取图片信息，返回缩略图、原始图宽高 */
    async getImageInfo(content: File | Blob) {
      if (!content) return Promise.resolve({})
      return getImageInfo(content)
    },
    /** 获取图片信息，并添加到 Map imgsMap 里面 */
    async imageInfoHandler(result: any) {
      if (!result) return
      this.loading = true

      const commonStore = useCommonStore()
      const filePath = commonStore.filePath
      const showWatermark = commonStore.watermark()

      // TODO can limit 张数
      // res.length = 30
      // messageSimple.warning('最多使用30张图片')

      // result.forEach(async (res: any) => {})
      for (const res of result) {
        const { key, fileBuffer } = res
        let { file } = res

        const filenameFull = window.isMac
          ? key.split('/').pop()
          : key.split('\\').pop()
        const originFormat = filenameFull.split('.').pop()

        if (imgWatermarkExts.includes(originFormat.toLowerCase())) {
          if (!this.imgsMap.has(key)) {
            const filename = filenameFull.replace(`.${originFormat}`, '')
            let blob: any = null

            if (fileBuffer) {
              // 仅electron使用
              blob = new Blob([fileBuffer], {
                type: `image/${originFormat}`,
              })
            }
            file = file || blob || ''
            // if (!file.size) return messageSimple.error('文件大小不能为0kb')
            const imgInfo: any =
              (file.size && (await this.getImageInfo(file))) || {}
            const newFormat = imgInfo.format || 'jpg'
            const imgUrl = imgInfo.thumbnail || '' // 缩略图
            const newFile = `${filePath}${filename}.${originFormat}`

            let pictureParams: any = {
              key, // 图片路径key，如：                            /Users/<USER>/Desktop/***/1、首页.jpg
              filename, // 文件名，不含后缀，如：                   1、首页
              newFileName: filename, // 新的文件名，支持修改，如：  1、首页custom
              newFile, // 新的图片路径，如：                       /Users/<USER>/Desktop/图片转换器/1、首页.jpg
              originFile: key, // 原始图片路径，如：               /Users/<USER>/Desktop/***/1、首页.jpg
              record: {
                originFormat, // 原始图片后缀，如：                jpg
                newFormat, // 新的图片后缀，如：                   jpg
                status: 'wait',
                loading: false,
                imgUrl, // 缩略图
                name: '89:127', // 初始化比例
                width: imgInfo.width || 0, // 原始图片宽度，如：   1920
                height: imgInfo.height || 0, // 原始图片高度，如：   1080

                // img.record.aspectRatio = currentImg.value.record.aspectRatio; // 记录比例
                // img.record.autoCropWidth = currentImg.value.record.autoCropWidth; // 自动裁剪宽度
                // img.record.autoCropHeight = currentImg.value.record.autoCropHeight; // 自动裁剪高度
                // img.record.pixel_with = currentImg.value.record.pixel_with; // 记录输出尺寸
                // img.record.pixel_height = currentImg.value.record.pixel_height; // 记录输出尺寸
                // img.record.scale = currentImg.value.record.scale; // 记录缩放
                // img.record.name = currentImg.value.name; // 记录尺寸选中名字

                opacity: 1, // 透明度，0-100
                rotate: 0, // 旋转，0-360
                zoom: 1, // 缩放
                transformX: 1, // 左右翻转
                transformY: 1, // 上下翻转
              },
              checked: true,
              showWatermark, // 是否打水印
              file,
            }
            // fileToCanvas(file, (result: any) => {
            //   pictureParams.image = result.image
            // })

            this.imgsMap.set(key, pictureParams)
          } else {
            messageSimple.warning('已过滤同名文件！')
          }
        } else {
          console.log('已过滤不支持的文件格式：' + originFormat)
          messageSimple.warning('已过滤不支持的文件格式：' + originFormat)
        }
      }
      // console.log('imgsMap', this.imgsMap)

      this.imgsMap.size > 1 && this.imgsMap.delete(this.currentImg.key) // 删除当前图片，仅支持单张
      this.getCurrent()
      this.loading = false
    },
    beforeConvert(type: string) {
      if (goLogin()) return

      const commonStore = useCommonStore()
      const showWatermark = commonStore.watermark()
      showWatermark ? goPay() : this.convert(type)
    },
    async convert(type: any) {
      // if (!window.isElectron) {
      //   return messageSimple.warning('需要下载app')
      // }

      this.loading = true

      // 如果是从证件照编辑对话框导出单张照片，直接使用mattingCropedImage
      if (type === 'current' && this.currentImg?.mattingCropedImage) {
        return this.exportSinglePassportImage()
      }

      // // 切换到改尺寸模块，确保裁剪器初始化
      // const currentSegment = this.currentSegment
      // this.currentSegment = this.segmentsType.changeSize // 改尺寸
      // this.changeSegmenting = true
      // // 等待裁剪器初始化和渲染完成
      // await new Promise((resolve) => {
      //   // 检查裁剪器是否已初始化
      //   const checkCropper = () => {
      //     if (this.cropper && !this.changeSegmenting) {
      //       // 裁剪器已初始化且渲染完成
      //       console.log('Cropper is ready for export')
      //       resolve(true)
      //     } else {
      //       // 继续等待
      //       console.log('Waiting for cropper to initialize...')
      //       setTimeout(checkCropper, 100)
      //     }
      //   }
      //   // 开始检查
      //   setTimeout(checkCropper, 300)
      // })
      // 额外等待一点时间确保渲染完全
      // await new Promise((resolve) => setTimeout(() => resolve(true), 200))

      let tempCurrentIndex = this.currentIndex
      let imgs: any = new Map()
      let index = 0
      const isSingleImage = type === 'current'

      // mark crop
      for (const img of this.imgsMap) {
        const [, key] = img // [key, proxy]

        // const options = key.record.pixel_with
        //   ? {
        //       width: key.record.pixel_with,
        //       height: key.record.pixel_height,
        //     }
        //   : {}
        // mark crop 未固定宽高，则增加等待输出图片的清晰度
        // {
        //   width: key.record.autoCropWidth * 2,
        //   height: key.record.autoCropHeight * 2,
        // }
        // console.log('cropper options', options)

        // options 导出图片的尺寸设置
        const options = {
          width: key.record.pixel_with || key.record.width,
          height: key.record.pixel_height || key.record.height,
        }
        // 含背景图时的宽高
        if (key.backgroundImage && key.record.name === '自由') {
          options.width = key.record.autoCropWidth
          options.height = key.record.autoCropHeight
        }
        // 设置比例时，按scaleFactor进行放大
        if (key.record.name === '自由' || key.record.name.includes(':')) {
          options.width = options.width * this.scaleFactor
          options.height = options.height * this.scaleFactor
        }
        // console.log('options', options);

        const sizeKB = key.file.size / 1024
        const formatType = key.backgroundColor && key.backgroundColor !== 'transparent' || key.backgroundImage ? 'image/jpeg' : 'image/png'
        const format = '.' + formatType.split('/')[1]
        if (isSingleImage) {
          if (index === this.currentIndex) {
            const base64 = this.cropper
              ?.getCroppedCanvas(options)
              .toDataURL(formatType, 0.9)

            key.base64 = base64
            imgs.set(key.key, {
              key: key.key,
              record: { ...key.record },
              base64: key.base64,
              originFile: key.originFile,
              // newFile: key.newFile,
              newFile: key.newFile.replace(/\.\w+$/, format),
              filename: key.filename,
              showWatermark: key.showWatermark,
            })
          }
        } else {
          this.currentIndex = index
          // mark crop 大图增加等待时间
          let timeout = 500
          if (sizeKB > 1024 * 1) timeout = sizeKB / 2
          if (sizeKB > 1024 * 5) timeout = sizeKB / 3
          if (sizeKB > 1024 * 10) timeout = sizeKB / 4
          if (sizeKB > 1024 * 20) timeout = sizeKB / 6
          if (sizeKB > 1024 * 40) timeout = sizeKB / 8
          await new Promise((resolve) =>
            setTimeout(() => resolve(true), timeout)
          )
          // console.log('timeout', timeout)
          // this.downSimple()

          // 如果自己画（废弃）：1、img拉伸 2、按比例裁剪时需计算裁剪宽高 3、canvas画
          // var img = new Image()
          // img.crossOrigin = 'Anonymous'
          // img.onload = function () {
          //   ctx.drawImage(
          //     img,
          //     item.style.left,
          //     item.style.top,
          //     item.style.width,
          //     item.style.height
          //   )
          // }
          // img.src = item.img

          const base64 = this.cropper
            ?.getCroppedCanvas(options)
            .toDataURL(formatType, 0.9)
          key.base64 = base64
          imgs.set(key.key, {
            key: key.key,
            record: { ...key.record },
            base64: key.base64,
            originFile: key.originFile,
            // newFile: key.newFile,
            newFile: key.newFile.replace(/\.\w+$/, format),
            filename: key.filename,
            showWatermark: key.showWatermark,
          })
        }
        index++
      }
      currentImgSize = isSingleImage ? 1 : imgs.size
      this.currentIndex = tempCurrentIndex // 切换回当前图片
      // this.currentSegment = currentSegment // 切换回当前模块

      if (window.isElectron) {
        this.batchSave(imgs)
      } else {
        imgs.forEach((img: any) => {
          // console.log(img.base64)
          var aLink: any = document.createElement('a')
          aLink.download = img.filename || 'demo'
          aLink.href = img.base64
          aLink.click()
        })
        this.loading = false
      }
    },
    getCroppedImage(type: any) {
      this.loading = true
      let base64: any = ''

      for (const img of this.imgsMap) {
        const [, key] = img // [key, proxy]

        // options 导出图片的尺寸设置
        const options = {
          width: key.record.pixel_with || key.record.width,
          height: key.record.pixel_height || key.record.height,
        }
        // 含背景图时的宽高
        if (key.backgroundImage && key.record.name === '自由') {
          options.width = key.record.autoCropWidth
          options.height = key.record.autoCropHeight
        }
        // 设置比例时，按scaleFactor进行放大
        if (key.record.name === '自由' || key.record.name.includes(':')) {
          options.width = options.width * this.scaleFactor
          options.height = options.height * this.scaleFactor
        }
        // console.log('options', options); // whtodo

        const formatType =
          (key.backgroundColor && key.backgroundColor !== 'transparent') ||
          key.backgroundImage
            ? 'image/jpeg'
            : 'image/png'
        base64 = this.cropper
          ?.getCroppedCanvas(options)
          .toDataURL(formatType, 0.9)
      }

      this.loading = false
      this.currentImg.mattingCropedImage = base64
      return base64
    },
    // 导出单张证件照
    exportSinglePassportImage() {
      if (!this.currentImg?.mattingCropedImage) {
        this.loading = false
        return
      }

      const formatType = this.currentImg.backgroundColor && this.currentImg.backgroundColor !== 'transparent' || this.currentImg.backgroundImage ? 'image/jpeg' : 'image/png'
      const format = '.' + formatType.split('/')[1]

      const exportData = {
        key: this.currentImg.key || 'passport_image',
        record: { ...this.currentImg.record },
        base64: this.currentImg.mattingCropedImage,
        originFile: this.currentImg.originFile || '',
        newFile: (this.currentImg.newFile || 'passport_image').replace(/\.\w+$/, format),
        filename: this.currentImg.filename || 'passport_image',
        showWatermark: this.currentImg.showWatermark || false,
      }

      if (window.isElectron) {
        // Electron环境 - 保持原数据格式
        this.batchSave(new Map([[exportData.key, exportData]]))
      } else {
        // Web环境 - 直接下载
        const aLink = document.createElement('a')
        aLink.download = exportData.filename + format
        aLink.href = exportData.base64
        aLink.click()
        this.loading = false
      }
    },
    batchSave(targetImgsMap: any) {
      window.ipcRendererApi?.send('batch-base64-img-to-file', {
        maps: [...mapToArray(targetImgsMap)],
        options: {},
      })
    },
    batchSaveCallback() {
      if (!window.isElectron) return
      const _this = this
      window.ipcRendererApi?.removeAllListeners(
        'batch-base64-img-to-file-progress'
      )
      window.ipcRendererApi?.on(
        'batch-base64-img-to-file-progress',
        (event: any, res: any) => {
          console.log(res)
          currentImgSize--
          if (currentImgSize === 0) {
            messageSimple.success('转换成功')
            _this.loading = false

            const commonStore = useCommonStore()
            commonStore.showConvertSuccessDialog = true
          }
        }
      )
    },
    downSimple(type?: string) {
      var aLink: any = document.createElement('a')
      aLink.download = this.currentImg.filename || 'demo'

      if (type === 'blob') {
        this.cropper?.getCroppedCanvas().toBlob((data: any) => {
          aLink.href = window.URL.createObjectURL(data)
          aLink.click()
        })
      } else {
        const base64 = this.cropper?.getCroppedCanvas().toDataURL()
        aLink.href = base64
        aLink.click()
      }
    },
    getListAll(params?: any) {
      const isSearch = !!params
      if (!params?.name && sessionStorage.categoryList) {
        this.categoryList = JSON.parse(sessionStorage.categoryList)
        this.showSearchResult = !!this.categoryList.length
        return
      }
      getListAll(params)
        .then((_: any) => {
          if (_.data.code === 0 && _.data.data.length > 0) {
            let categoryList: any = [
              {
                id: 0,
                name: '自定义',
                list: [],
              },
            ]

            _.data.data.forEach((item: any) => {
              categoryList.push({
                id: item.category.id,
                name: item.category.name,
                status: item.category.status,
                sticky: item.category.name.sticky,
                list: item.size,
              })
            })

            this.categoryList = categoryList
            !isSearch && this.updateCategorySession()
          } else {
            this.categoryList = []
            console.log(_)
            // messageSimple.warning('未搜索到结果')
          }
          if (isSearch) this.showSearchResult = !!this.categoryList.length
        })
        .catch((e) => {
          messageSimple.error('搜索异常：', e.message)
          console.log(e)
        })
    },
    getListAllByBackground(params?: any) {
      const isSearch = !!params
      if (!params?.name && sessionStorage.categoryBackgroundList) {
        this.categoryBackgroundList = JSON.parse(
          sessionStorage.categoryBackgroundList
        )
        return
      }
      getListAllByBackground(params)
        .then((_: any) => {
          if (_.data.code === 0 && _.data.data.length > 0) {
            let categoryBackgroundList: any = [
              {
                id: 0,
                name: '自定义',
                list: [],
              },
            ]

            _.data.data.forEach((item: any) => {
              categoryBackgroundList.push({
                id: item.category.id,
                name: item.category.name,
                status: item.category.status,
                list: item.background,
              })
            })

            this.categoryBackgroundList = categoryBackgroundList
            !isSearch && this.updateCategorySession()
          } else {
            this.categoryBackgroundList = []
            console.log(_)
            // messageSimple.warning('未搜索到结果')
          }
          if (isSearch)
            this.showSearchResult = !!this.categoryBackgroundList.length
        })
        .catch((e) => {
          messageSimple.error('搜索异常：', e.message)
          console.log(e)
        })
    },
    updateCategorySession() {
      this.categoryList.length &&
        (sessionStorage.categoryList = JSON.stringify(this.categoryList))

      this.categoryBackgroundList.length &&
        (sessionStorage.categoryBackgroundList = JSON.stringify(
          this.categoryBackgroundList
        ))
    },
    updateCropBoxData() {
      if (!this.cropper) return
      const cropBoxData = this.cropper.getCropBoxData()
      this.currentImg.record.left = cropBoxData.left
      this.currentImg.record.top = cropBoxData.top
      this.currentImg.record.autoCropWidth = cropBoxData.width
      this.currentImg.record.autoCropHeight = cropBoxData.height
      // console.log('updateCropBoxData', cropBoxData)
    },

    // 计算裁剪宽高
    calculateCropDimensions(
      imgWidth: number,
      imgHeight: number,
      cropWidth: number,
      cropHeight: number
    ) {
      // 当裁剪宽度大于图片原始宽度时进行缩放
      if (cropWidth > imgWidth) {
        const ratio = imgWidth / cropWidth
        cropWidth = imgWidth
        cropHeight = Math.round(cropHeight * ratio)
      }

      // 当裁剪高度大于图片原始高度时进行缩放
      if (cropHeight > imgHeight) {
        const ratio = imgHeight / cropHeight
        cropHeight = imgHeight
        cropWidth = Math.round(cropWidth * ratio)
      }

      return {
        cropWidth,
        cropHeight,
      }
    },

    // 选择尺寸
    chooseSize(custom: any) {
      if (!this.cropper) return
      // console.log(custom); // whtodo
      
      const canvasData: any = this.cropper.getCanvasData()
      const scale = canvasData.width / canvasData.naturalWidth
      
      if (
        custom.name.includes(':') &&
        ['1:1', '16:9', '9:16', '3:4', '4:3', '3:2', '2:3'].includes(custom.name)
      ) {
        // 清除自定义尺寸
        this.currentImg.record.autoCropWidth = null
        this.currentImg.record.autoCropHeight = null
        this.currentImg.record.pixel_with = null
        this.currentImg.record.pixel_height = null

        const [width, height] = custom.name.split(':').map(Number) // 截图框的宽高比例
        const ratio = width / height
        this.cropper.setAspectRatio(ratio)
        this.currentImg.record.aspectRatio = ratio
      } else if (custom.pixel_with && custom.pixel_height) {
        // 清除自定义比例
        this.currentImg.record.aspectRatio = null
        this.cropper.setAspectRatio(0)

        const customThanImgWidth = custom.pixel_with > canvasData.naturalWidth
        const customThanImgHeight = custom.pixel_height > canvasData.naturalHeight
        // 截取相对图片越界
        if (customThanImgWidth || customThanImgHeight) {
          const { cropWidth, cropHeight } = this.calculateCropDimensions(
            canvasData.naturalWidth * scale,
            canvasData.naturalHeight * scale,
            custom.pixel_with,
            custom.pixel_height
          )
          this.currentImg.record.autoCropWidth = cropWidth // 自动裁剪宽度
          this.currentImg.record.autoCropHeight = cropHeight // 自动裁剪高度
        } else {
          // 截取相对图片之内
          this.currentImg.record.autoCropWidth = custom.pixel_with * scale // 自动裁剪宽度
          this.currentImg.record.autoCropHeight = custom.pixel_height * scale // 自动裁剪高度
        }
        this.currentImg.record.pixel_with = custom.pixel_with // 记录输出尺寸
        this.currentImg.record.pixel_height = custom.pixel_height // 记录输出尺寸

        // 根据图片计算居中位置
        const left =
          (canvasData.width - this.currentImg.record.autoCropWidth) / 2 +
          canvasData.left
        const top =
          (canvasData.height - this.currentImg.record.autoCropHeight) / 2 +
          canvasData.top

        this.cropper.setCropBoxData({
          left,
          top,
          width: this.currentImg.record.autoCropWidth,
          height: this.currentImg.record.autoCropHeight,
        })
      } else {
        // 清除自定义比例
        this.currentImg.record.aspectRatio = null
        this.cropper.setAspectRatio(0)
      }

      this.currentImg.record.scale = scale // 记录缩放
      this.currentImg.record.name = custom.name // 记录尺寸选中名字

      this.updateCropBoxData()
    },
  },
})
