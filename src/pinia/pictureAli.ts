/**
 * 图片编辑相关
 */
import oss from '@/utils/aliOss'
import { defineStore } from 'pinia'
import { useUserStore } from '@/pinia/user'
import { useCommonStore } from '@/pinia/common'
import { messageSimple } from '@/utils/message'
import {
  webUpload,
  downloadImageByA,
  compressFile,
  // getImageSize,
  blobToFile,
} from '@/utils/image'

import {
  superResolution,
  colorize,
  face,
  humanAnime,
  humanSketch,
} from '@/api/pictureAli'

import { imgToPdfExts } from '@/utils/enum/configData'

// import { goLogin, goPay } from '@/utils/user'
// import qs from 'qs'
// import { messageSimple } from '@/utils/message'
// const query = qs.parse(location.search.replace('?', ''))

export const usePictureAliStore = defineStore({
  id: 'pictureAli',
  state: () => ({
    menus: [
      '港漫风',
      '国画工笔画',
      '漫画风格',
      '动画3D',
      '日漫风',
      '3D特效',
      '手绘风',
      '铅笔画',
      '艺术特效',
    ],
    menusType: {
      港漫风: 'hongkong',
      国画工笔画: 'claborate',
      漫画风格: 'comic',
      动画3D: 'animation3d',
      日漫风: 'anime',
      '3D特效': '3d',
      手绘风: 'handdrawn',
      铅笔画: 'sketch',
      艺术特效: 'artstyle',
    } as Record<string, string>,
    pageType: '', // 页面类型
    // 页面存储的信息
    pageInfo: {
      enlarge: {
        // scale
        // 当前页面选中的图片  select：origin、new 当前选中的是 原图 或 效果图
        currentImg: { origin: '', new: '', select: '', fileName: '' },
        currentDemoSwipeIndex: 0, // 当前选中的demo
        currentDemoImage: '', // 当前选中的demo图片
        currentDemoResultImage: '', // 当前选中的demo转换后的图片
        currentDemoResultTemplate:
          'https://static.miaoji66.com/image_convert/demo-compress/enlarge/result$.png', // demo result 图片模板
        currentDemoTemplate:
          'https://static.miaoji66.com/image_convert/demo-compress/enlarge/origin$.png', // demo图片模板
        currentDemoTip: '功能说明：将图片无损放大，提升细节纹理，降低噪点', // demo提示
        api: superResolution,
      },
      oldPicture: {
        currentImg: { origin: '', new: '', select: '', fileName: '' },
        currentDemoSwipeIndex: 0,
        currentDemoImage: '',
        currentDemoResultImage: '',
        currentDemoResultTemplate:
          'https://static.miaoji66.com/image_convert/demo-compress/oldPicture/result$.jpg',
        currentDemoTemplate:
          'https://static.miaoji66.com/image_convert/demo-compress/oldPicture/origin$.jpg',
        currentDemoTip: '功能说明：对黑白照片自动上色',
        api: colorize,
      },
      repair: {
        currentImg: { origin: '', new: '', select: '', fileName: '' },
        currentDemoSwipeIndex: 0,
        currentDemoImage: '',
        currentDemoResultImage: '',
        currentDemoResultTemplate:
          'https://static.miaoji66.com/image_convert/demo-compress/repair/result$.jpg',
        currentDemoTemplate:
          'https://static.miaoji66.com/image_convert/demo-compress/repair/origin$.png',
        currentDemoTip: '功能说明：对人脸进行细节增强，优化图像质量',
        api: face,
      },
      cartoon: {
        currentMenuIndex: 0, // 9种风格
        currentImg: { origin: '', new: '', select: '', fileName: '' },
        currentDemoSwipeIndex: 0,
        currentDemoImage: '',
        currentDemoResultImage: '',
        currentDemoResultTemplate:
          'https://static.miaoji66.com/image_convert/demo-compress/cartoon/result_cartoonMenu$.png', // menuIndex: cartoonMenu swipeIndex: $
        currentDemoTemplate:
          'https://static.miaoji66.com/image_convert/demo-compress/cartoon/origin$.png',
        currentDemoTip: '功能说明：将图片转成9种动漫效果',
        api: humanAnime,
      },
      sketch: {
        currentImg: { origin: '', new: '', select: '', fileName: '' },
        currentDemoSwipeIndex: 0,
        currentDemoImage: '',
        currentDemoResultImage: '',
        currentDemoResultTemplate:
          'https://static.miaoji66.com/image_convert/demo-compress/sketch/result$.png',
        currentDemoTemplate:
          'https://static.miaoji66.com/image_convert/demo-compress/sketch/origin$.png',
        currentDemoTip: '功能说明：将图片转成素描画风格（仅支持人像）',
        api: humanSketch,
      },
    } as any,
    exportUrl: '', // 导出图片的路径
    loading: false, // 阿里云转换图片请求loading
    pageLoading: false, // 页面loading
  }),
  getters: {},
  actions: {
    webUpload,
    electronUpload(e: any) {
      window.ipcRendererApi
        ?.invoke('select-pic', {
          singleSelections: true, // 只能单选
          exts: imgToPdfExts, // 限定图片类型
        })
        .then((res: any) => {
          res.forEach((res: any) => {
            const { key, fileBuffer } = res

            const options = {
              format: key.split('.').pop(),
              name: window.isMac ? key.split('/').pop() : key.split('\\').pop(),
            }
            // console.log(key, options)
            const file = blobToFile(fileBuffer, options)

            this.chooseWebImages({
              target: { files: [file] },
            })
          })
        })
    },
    chooseWebImages(e: any = {}) {
      const file = e.target.files[0]
      this.pageLoading = true

      const isEnlarge = this.pageType === 'enlarge'

      compressFile(
        {
          file,
          limitSize: 2000, // 限制最大像素
          getLimitSize: (image: any) => {
            // 高清放大限制：1920*1080
            if (isEnlarge) {
              return image.width > image.height ? 1920 : 1080
            }
            // 其他限制：2000*2000
            return 2000
          },
          format: file.type,
          returnFormat: 'file',
          quality: isEnlarge ? 1 : 0.9,
        },
        (res: any) => {
          // console.log('compressFile', res)

          // 文件超出大小
          if (res.file.size / 1024 > 3 * 1024) {
            return messageSimple.warning('图片大小不超过3M，请压缩后上传')
          }
          // 文件像素太小
          // if (res.width < 32 || res.height < 32) {
          //   return messageSimple.warning(
          //     '请使用分辨率在32x32以上的图像输入'
          //   )
          // }

          const userStore = useUserStore()
          oss.aliOss
            ?.ossUploadImage({
              file: res.file,
              fileId: Number(userStore.user.id),
            })
            .then((response: any) => {
              const url = response && response.url
              if (url) {
                this.pageInfo[this.pageType].currentImg.origin = url
                this.pageInfo[this.pageType].currentImg.new = ''
                this.pageInfo[this.pageType].currentImg.select = 'origin'
                this.pageInfo[this.pageType].currentImg.fileName = file.name
                // console.log(
                //   this.pageInfo[this.pageType].currentImg.origin,
                //   file.name
                // )
                this.getResultImage(url)
              } else {
                console.log('图片上传失败')
                messageSimple.error('图片上传失败')
              }
            })
            .catch((error: any) => {
              console.error('图片上传异常', error)
              messageSimple.error('图片上传异常', error.message)
            })
            .finally(() => (this.pageLoading = false))
        }
      )
    },
    getResultImage(url: string) {
      let currentPageInfo = this.pageInfo[this.pageType]

      this.loading = true
      let data: any = {
        // mjmind.oss-cn-shanghai.aliyuncs.com 才能调用阿里云 sdk
        url: url.replace(
          'static.mymind365.com',
          'mjmind.oss-cn-shanghai.aliyuncs.com'
        ),
      }
      if (this.pageType === 'cartoon') {
        data.algo_type =
          this.menusType[this.menus[currentPageInfo.currentMenuIndex]]
      }
      currentPageInfo
        .api(data)
        .then((res: any) => {
          if (res.data.code === 0) {
            currentPageInfo.currentImg.new = res.data.data
            currentPageInfo.currentImg.select = 'new'
          } else {
            let tip = ''
            try {
              const message = JSON.parse(
                res.data.message.split('Data: ')[1]
              ).Message
              const arr = message.split(' - ')
              tip = arr.slice(0, arr.length - 1).join(' - ')
            } catch (error) {
              console.error(error)
            }
            messageSimple.error('图片处理失败：' + (tip || res.data.message))

            this.pageInfo[this.pageType].currentImg.origin = '' // 转换失败则清除原图
          }
        })
        .catch((error: any) => {
          console.error('图片处理异常：', error)
          messageSimple.error('图片处理异常：', error.message)

          this.pageInfo[this.pageType].currentImg.origin = '' // 转换失败则清除原图
        })
        .finally(() => {
          this.loading = false
        })

      // this.loading = true
      // setTimeout(() => {
      //   currentPageInfo.currentImg.new = 'https://static.miaoji66.com/image_convert/demo-compress/oldPicture/result1.jpg'
      //   currentPageInfo.currentImg.select = 'new'
      //   this.loading = false
      // }, 1000)
    },
    // swipe、menu选择时使用
    getResultImageBySelect() {
      let currentPageInfo = this.pageInfo[this.pageType]

      let algoType = ''
      if (this.pageType === 'cartoon') {
        algoType = this.menusType[this.menus[currentPageInfo.currentMenuIndex]] // 从0开始
      }

      const swipeIndex = currentPageInfo.currentDemoSwipeIndex + 1 // 从1开始
      return currentPageInfo.currentDemoResultTemplate
        ?.replace('cartoonMenu', algoType)
        .replace('$', `${swipeIndex}`)
    },
    clearCurrentImg() {
      this.pageInfo[this.pageType].currentImg = {
        origin: '',
        new: '',
        select: '',
        fileName: '',
      }
    },
    exportImg({ url, filePath, fileName }: any) {
      fileName = fileName || this.pageInfo[this.pageType].currentImg.fileName

      if (window.isElectron) {
        window.ipcRendererApi?.send('save-img-by-url', {
          url,
          filePath,
          fileName,
        })
      } else {
        downloadImageByA(url, fileName)
        // setTimeout(() => messageSimple.success('导出成功'), 1000)
      }
    },
    processCallback() {
      if (!window.isElectron) return
      const _this = this

      window.ipcRendererApi?.removeAllListeners('save-img-by-url-callback')
      window.ipcRendererApi?.on(
        'save-img-by-url-callback',
        (event: any, res: any) => {
          console.log(res)

          if (res.status === 'success') {
            _this.exportUrl = res.exportUrl.replace('http://', 'https://')
            const commonStore = useCommonStore()
            commonStore.showConvertSuccessDialog = true
            // messageSimple.success('导出成功')
          } else if (res.status === 'error') {
            messageSimple.error(`导出失败：${res.message}`)
          }
        }
      )
    },
  },
})
