import { defineStore } from 'pinia'
import { messageSimple } from '@/utils/message'
// import { ElMessage, ElMessageBox } from 'element-plus'
import { useCommonStore } from '@/pinia/common'
import { getImageInfo, fileToCanvas } from '@/utils'
import { mapToArray } from '@/utils/util'
import { pageType } from '@/utils/enum'
import { chooseWebImages, dragHandler, onDragChange } from '@/utils/drag'
import { imgWatermarkExts } from '@/utils/enum/configData'
import { goLogin, goPay } from '@/utils/user'

let currentImgSize = 0
export const useBatchChangeSizeStore = defineStore({
  id: 'batchChangeSize',
  state: () => ({
    imgsMap: new Map(),
    currentIndex: 0,
    currentImg: {
      record: {
        sizeType: 'customSize',
        customWidth: '',
        customHeight: '',
        scale: 100,
      },
    } as any,
    pageType: '',
    applyToAll: false,
    loading: false,
  }),
  getters: {},
  actions: {
    init(name: any) {
      // this.loading = false
      this.pageType = pageType[name as keyof typeof pageType]
      this.bindEvent()
      setTimeout(this.dragHandler, 1000)
    },
    bindEvent() {
      // 选择文件或图片结果返回
      this.unBindEvent()
      window.ipcRendererApi?.on('select-callback', () => (this.loading = true))
    },
    unBindEvent() {
      window.ipcRendererApi?.removeAllListeners('select-callback')
    },
    clear() {
      this.imgsMap.clear()
    },
    /** 获取拖拽的文件 */
    dragHandler() {
      dragHandler(this.imageInfoHandler)
    },
    /** 获取拖拽的文件夹 */
    onDragChange(e: any) {
      onDragChange(e, this.imageInfoHandler)
    },
    /** web端选择图片 */
    chooseWebImages(choose: any = {}) {
      this.loading = true
      chooseWebImages(choose, this.imageInfoHandler)
    },
    getCurrent() {
      const mapArray = Array.from(this.imgsMap.entries())
      if (!mapArray.length) return

      const [, target] = mapArray[this.currentIndex]
      this.currentImg = target

      return target
    },
    /** 获取图片信息，返回缩略图、原始图宽高 */
    async getImageInfo(content: File | Blob) {
      if (!content) return Promise.resolve({})
      return getImageInfo(content)
    },
    /** 获取图片信息，并添加到 Map imgsMap 里面 */
    async imageInfoHandler(result: any) {
      if (!result) return
      this.loading = true

      const commonStore = useCommonStore()
      const filePath = commonStore.filePath
      const showWatermark = commonStore.watermark()

      // TODO can limit 张数
      // res.length = 30
      // messageSimple.warning('最多使用30张图片')

      // result.forEach(async (res: any) => {})
      for (const res of result) {
        const { key, fileBuffer } = res
        let { file } = res

        const filenameFull = window.isMac
          ? key.split('/').pop()
          : key.split('\\').pop()
        const originFormat = filenameFull.split('.').pop()

        if (imgWatermarkExts.includes(originFormat.toLowerCase())) {
          if (!this.imgsMap.has(key)) {
            const filename = filenameFull.replace(`.${originFormat}`, '')
            let blob: any = null

            if (fileBuffer) {
              // 仅electron使用
              blob = new Blob([fileBuffer], {
                type: `image/${originFormat}`,
              })
            }
            file = file || blob || ''
            // if (!file.size) return messageSimple.error('文件大小不能为0kb')
            const imgInfo: any =
              (file.size && (await this.getImageInfo(file))) || {}
            const newFormat = imgInfo.format || 'jpg'
            const imgUrl = imgInfo.thumbnail || '' // 缩略图
            const newFile = `${filePath}${filename}.${originFormat}`

            let pictureParams: any = {
              key, // 图片路径key，如：                            /Users/<USER>/Desktop/***/1、首页.jpg
              filename, // 文件名，不含后缀，如：                   1、首页
              newFileName: filename, // 新的文件名，支持修改，如：  1、首页custom
              newFile, // 新的图片路径，如：                       /Users/<USER>/Desktop/图片转换器/1、首页.jpg
              originFile: key, // 原始图片路径，如：               /Users/<USER>/Desktop/***/1、首页.jpg
              // icoSizeType,  // 'originImg', 'customImg' ico格式使用
              record: {
                originFormat, // 原始图片后缀，如：                jpg
                newFormat, // 新的图片后缀，如：                   jpg
                status: 'wait',
                loading: false,
                width: imgInfo.width || '--',
                height: imgInfo.height || '--',
                imgUrl, // 缩略图

                sizeType: 'customSize', // customSize、scaleSize
                customWidth: '',
                customHeight: '',
                scale: 100,
              },
              checked: true,
              showWatermark, // 是否打水印
              file,
            }
            // fileToCanvas(file, (resCanvas: any) => {
            //   pictureParams.canvas = resCanvas.canvas
            // })
            // 应用到全部
            if (this.applyToAll) {
              pictureParams.record.sizeType = this.currentImg.record.sizeType
              pictureParams.record.customWidth = this.currentImg.record.customWidth
              pictureParams.record.customHeight = this.currentImg.record.customHeight
              pictureParams.record.scale = this.currentImg.record.scale
            }

            this.imgsMap.set(key, pictureParams)
          } else {
            messageSimple.warning('已过滤同名文件！')
          }
        } else {
          console.log('已过滤不支持的文件格式：' + originFormat)
          messageSimple.warning('已过滤不支持的文件格式：' + originFormat)
        }
      }
      // console.log('imgsMap', this.imgsMap)

      this.getCurrent()
      this.loading = false
    },
    beforeConvert(type: string) {
      if (goLogin()) return

      const commonStore = useCommonStore()
      const showWatermark = commonStore.watermark()
      showWatermark ? goPay() : this.convert(type)
    },
    async convert(type: any) {
      if (!window.isElectron) {
        return messageSimple.warning('需要下载app')
      }

      this.loading = true
      let imgs: any = new Map()
      let index = 0
      const isSingleImage = type === 'current'
      for (const img of this.imgsMap) {
        const [, key] = img // [key, proxy]
        let limitWidth = key.record.width
        let limitHeight = key.record.height
        if (key.record.sizeType === 'customSize') {
          limitWidth = key.record.customWidth || key.record.width
          limitHeight = key.record.customHeight || key.record.height
        } else {
          limitWidth = ((key.record.width * key.record.scale) / 100).toFixed(0)
          limitHeight = ((key.record.height * key.record.scale) / 100).toFixed(
            0
          )
        }

        if (isSingleImage) {
          if (index === this.currentIndex) {
            await new Promise((resolve, reject) => {
              fileToCanvas(
                key.file,
                (res: any) => {
                  if (res.error) {
                    this.loading = false
                    messageSimple.error('图片处理失败：' + res.error)
                    reject('fail')
                  } else {
                    key.base64 = res.canvas?.toDataURL(
                      `image/${key.record.originFormat}`
                    )
                    imgs.set(key.key, key)
                    resolve('success')
                  }
                },
                {
                  limitWidth,
                  limitHeight,
                }
              )
            })
          }
        } else {
          await new Promise((resolve, reject) => {
            fileToCanvas(
              key.file,
              (res: any) => {
                if (res.error) {
                  this.loading = false
                  messageSimple.error('图片处理失败：' + res.error)
                  reject('fail')
                } else {
                  key.base64 = res.canvas?.toDataURL(
                    `image/${key.record.originFormat}`
                  )
                  imgs.set(key.key, key)
                  resolve('success')
                }
              },
              {
                limitWidth,
                limitHeight,
              }
            )
          })
        }
        index++
      }
      currentImgSize = isSingleImage ? 1 : imgs.size
      this.batchSave(imgs)
    },
    batchSave(targetImgsMap: any) {
      window.ipcRendererApi?.send('batch-base64-img-to-file', {
        maps: [...mapToArray(targetImgsMap)],
        options: {},
      })
    },
    batchSaveCallback() {
      if (!window.isElectron) return
      const _this = this
      window.ipcRendererApi?.removeAllListeners(
        'batch-base64-img-to-file-progress'
      )
      window.ipcRendererApi?.on(
        'batch-base64-img-to-file-progress',
        (event: any, res: any) => {
          console.log(res)
          currentImgSize--
          if (currentImgSize === 0) {
            messageSimple.success('转换成功')
            _this.loading = false

            const commonStore = useCommonStore()
            commonStore.showConvertSuccessDialog = true
          }
        }
      )
    },
  },
})
