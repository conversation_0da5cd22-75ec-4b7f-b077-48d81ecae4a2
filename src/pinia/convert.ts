/**
 * 图片压缩相关
 */
import { defineStore } from 'pinia'
import { usePictureStore } from './picture'
// import { messageSimple } from '@/utils/message'
// import { ElMessage, ElMessageBox } from 'element-plus'
let pictureStore: any = null

export const useConvertStore = defineStore({
  id: 'convert',
  state: () => ({}),
  getters: {},
  actions: {
    init() {
      pictureStore = pictureStore || usePictureStore()
    },
    /** 取消转换 */
    magickCancel(key: any) {
      window.ipcRendererApi?.send('magick-cancel', key.originFile)
      const [, value] = key
      value.record.loading = false
      value.record.status = 'cancel'
    },
  },
})
