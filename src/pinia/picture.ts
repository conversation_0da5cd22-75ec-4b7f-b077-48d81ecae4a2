/**
 * 图片转换操作相关
 */
// import heic2any from 'heic2any'
import { defineStore } from 'pinia'
import { useCommonStore } from '@/pinia/common'
import { useUserStore } from '@/pinia/user'
import { useCompressStore } from '@/pinia/compress'
import { useWatermarkStore } from '@/pinia/watermark'
import { usePictureAliStore } from '@/pinia/pictureAli'
import { convertImages } from '@/api/image'
import Semaphore from '@/utils/semaphore'
import oss from '@/utils/aliOss'
import { mapToArray, setIntervalHandler } from '@/utils/util'
import { goLogin, goPay } from '@/utils/user'
import { pageType } from '@/utils/enum'
import { chooseWebImages, dragHandler, onDragChange } from '@/utils/drag'
import { imgWatermarkExts, imgToPdfExts } from '@/utils/enum/configData'
import {
  webUpload,
  webUploadFolder,
  downloadImageByCanvas,
  downloadImageByXhr,
  getImageInfo,
  // fileToBase64ByReader,
  // fileToBase64,
  fileToCanvas,
} from '@/utils'
import { messageSimple } from '@/utils/message'
import { ElMessageBox } from 'element-plus'
// type InferStoreType<T> = T extends (...args: any[]) => infer U ? U : never
// type CompressStoreType = InferStoreType<typeof useCompressStore>
// type WatermarkStoreType = InferStoreType<typeof useWatermarkStore>

// Semaphore1
const maxConcurrent = 3
const sem = new Semaphore(maxConcurrent)

const icoSizeValue = '256x256'
// const icoSizeValue = '128x128'

export const usePictureStore = defineStore({
  id: 'picture',
  state: () => ({
    proxy: {} as any,
    imgsMap: new Map(), // 图片key集合（临时数据，各方共享），需要有序
    convertImgsMap: new Map(), // 图片格式转换集合，需要有序
    rawImgsMap: new Map(), // raw图片格式转换集合，需要有序
    heicImgsMap: new Map(), // heic图片格式转换集合，需要有序
    livpImgsMap: new Map(), // livp图片格式转换集合，需要有序
    compressImgsMap: new Map(), // 图片压缩集合，需要有序
    watermarkImgsMap: new Map(), // 图片水印集合，需要有序       纯H5
    pdfImgsMap: new Map(), // 图片转pdf集合，需要有序            列表在index.vue，UI跟其他不一样
    inputRefs: [] as Array<any>, // 用来存储输入框的引用
    filePath: '', // 文件输出路径，web相对路径，electron绝对路径
    tempPath: '',
    newFormatBySearch: '',
    newFormatAll: 'jpg',
    showWatermark: true, // 是否显示水印
    loading: false,
    pageType: pageType.convert, // 页面类型：convert、compress、watermark
  }),
  getters: {},
  actions: {
    init(name: any) {
      this.loading = false
      if (name) {
        this.imgsMap = this.getImgsMap(name)
        this.pageType = pageType[name as keyof typeof pageType]
      }
      this.getPath()
      this.watermark() // 加载图片时，就打上了是否需要水印
      this.bindEvent()
      setTimeout(this.dragHandler, 1000)
    },
    /** 获取当前页面的 imgsMap */
    getImgsMap(name: string) {
      const imgsMapKeyStore: any = {
        convert: this.convertImgsMap,
        raw: this.rawImgsMap,
        heic: this.heicImgsMap,
        livp: this.livpImgsMap,
        compress: this.compressImgsMap,
        watermark: this.watermarkImgsMap,
        pdf: this.pdfImgsMap,
      }
      return imgsMapKeyStore[name] || this.imgsMap
    },
    /** proxy传递 */
    // updateProxy(proxy) {
    //   this.proxy = proxy
    // },
    // 是否显示水印
    watermark() {
      const commonStore = useCommonStore()
      this.showWatermark = commonStore.watermark()
    },
    async semAcquire(key: any) {
      console.log('convert', `${this.pageType}_${key}`)
      return await sem.acquire() // 等待获取许可
    },
    semRelease(key: any) {
      sem?.release() // 释放信号量
      console.log('oncallback', `${this.pageType}_${key}`)
    },
    // 调用的是convert
    beforeConvertPdf(targetImgsMap?: any, options?: any) {
      if (goLogin()) return

      this.watermark()
      if (this.showWatermark) {
        const _this = this
        ElMessageBox.confirm(
          '非VIP带系统水印，VIP畅享软件所有功能',
          '未开通VIP，使用有限制',
          {
            distinguishCancelAndClose: true,
            confirmButtonText: '开通VIP', // 保留
            cancelButtonText: '试用', // 不保留
            showCancelButton: false, // 禁止试用
            customClass: 'customStyleByMessageBox',
            type: 'warning',
            // center: true
          }
        )
          .then((e: any) => {
            // console.log('then', e)
            // 开通VIP
            if (e === 'confirm') {
              _this.goPay()
            }
          })
          .catch((e: any) => {
            // console.log('catch', e)
            if (e === 'cancel') {
              _this.convert(targetImgsMap, options)
            }
          })
      } else {
        this.convert(targetImgsMap, options)
      }
    },
    // 调用的是convert
    beforeConvert(targetImgsMap?: any, isAll?: boolean) {
      if (goLogin()) return

      const isWatermark = this.pageType === pageType.watermark
      const isPdf = this.pageType === pageType.pdf

      // 上传状态校验
      if (isAll) {
        let isAllSuccess = true // 是否已全部上传成功
        let isAllLoading = true
        targetImgsMap.forEach((value: any) => {
          const { record } = value
          if (record.status !== 'success') {
            isAllSuccess = false

            if (window.isElectron) {
              if (!(record.status > 0)) {
                isAllLoading = false
              }
            } else {
              if (!record.loading) {
                isAllLoading = false
              }
            }
          }
        })
        if (isAllSuccess) {
          return messageSimple.success(
            isWatermark && window.isElectron
              ? '全部已处理'
              : '全部已处理，请点击保存按钮'
          )
        }
        if (isAllLoading) {
          return messageSimple.success('全部处理中，请稍等')
        }
      } else {
        let hasLoading = false // 是否存在上传中的图片
        targetImgsMap.forEach((value: any) => {
          const { record } = value
          if (record.status > 0 || record.loading) {
            hasLoading = true
          }
        })
        if (hasLoading) {
          return messageSimple.success('图片处理中，请稍等')
        }
      }

      this.watermark()
      if (this.showWatermark) {
        // 打水印、转pdf 页面，必须是会员，三方如此处理 (转pdf没用这个方法)
        if (isWatermark || isPdf) {
          return this.goPay()
        }

        const _this = this
        ElMessageBox.confirm(
          '非VIP带系统水印，VIP畅享软件所有功能',
          '未开通VIP，使用有限制',
          {
            distinguishCancelAndClose: true,
            confirmButtonText: '开通VIP', // 保留
            cancelButtonText: '试用', // 不保留
            customClass: 'customStyleByMessageBox',
            type: 'warning',
            // center: true
          }
        )
          .then((e: any) => {
            // console.log('then', e)
            // 开通VIP
            if (e === 'confirm') {
              _this.goPay()
            }
          })
          .catch((e: any) => {
            // console.log('catch', e)
            if (e === 'cancel') {
              _this.convert(targetImgsMap)
            }
          })
      } else {
        this.convert(targetImgsMap)
      }
    },
    // convert、watermarkConvert、pdfConvert、webConvert
    convert(targetImgsMap?: any, options?: any) {
      const commonStore = useCommonStore()
      commonStore.electronOpenFolder()

      const isWatermark = this.pageType === pageType.watermark
      const isPdf = this.pageType === pageType.pdf
      const isCompress = this.pageType === pageType.compress

      // 1、两端都支持
      // ① 图片打水印（支持web）
      if (isWatermark) {
        return this.watermarkConvert(targetImgsMap)
      }

      // 2、electron端
      if (window.isElectron) {
        // console.log(this.pageType, targetImgsMap)

        // ② 图片转PDF
        if (isPdf) {
          return this.pdfConvert(targetImgsMap, options)
        }

        // ③ 图片压缩、图片格式转换
        targetImgsMap.forEach(async (key: any) => {
          // 未转换过，才可以转换
          // !key.record.loading && key.record.status !== 'success'
          if (key.record.status !== 'success' && !(key.record.status > 0)) {
            // console.time(key.newFile)
            key.record.loading = true

            await this.semAcquire(key.key)
            this.updateFilePath(key) // 确保输出文件路径是最新的

            // 图片压缩
            // 图片格式转换：convert raw heic livp（支持web）
            const channel = isCompress ? 'magick-compress' : 'magick-execute'
            window.ipcRendererApi?.send(channel, JSON.parse(JSON.stringify(key)))
          }
        })
        return
      }

      // 3、web端
      // TODO 图片压缩补充纯H5
      // 图片压缩、图片转PDF（web暂不支持）
      if ([pageType.compress, pageType.pdf].includes(this.pageType)) {
        return messageSimple.warning('需要下载app')
      }

      // return messageSimple.warning('需要下载app')
      // 图片格式转换：convert raw heic livp
      this.webConvert(targetImgsMap)
    },
    webConvert(targetImgsMap: any) {
      const userStore = useUserStore()
      let store = JSON.parse(sessionStorage.store || '{}')
      let list: any = [] // 上传后端的图片转换集合
      let uploadPromises: any = [] // 上传阿里云的集合
      // 1、上传阿里云，并返回结果，用户可以点击按钮进行下载，会员才可以吧？
      targetImgsMap.forEach((key: any) => {
        // 未转换过，才可以转换
        if (!key.record.loading && key.record.status !== 'success') {
          key.record.loading = true
          key.record.status = 'wait'

          // 已上传的文件，不再次上传
          const storeOriginKey = `${key.webFileSize}_${key.originFile}`
          const storeNewKey = `${key.webFileSize}_${key.newFile}`
          const origin = store[storeOriginKey]
          if (origin) {
            key.record.aliOriginImgUrl = origin.aliOriginImgUrl || ''
            key.record.aliImgUrl = origin[storeNewKey] || ''
            if (key.record.aliImgUrl) {
              key.record.status = 'success'
              return (key.record.loading = false)
            } else {
              list.push(key)
              return uploadPromises.push(Promise.resolve())
            }
          }

          // 返回上传阿里云的 Promise
          const upload = oss.aliOss
            ?.ossUploadImage({
              file: key.file,
              fileId: Number(userStore.user.id),
            })
            .then((response: any) => {
              const url = response && response.url
              if (url) {
                key.record.aliOriginImgUrl = url

                // 已上传的文件，记录到本地存储
                store[storeOriginKey] = {
                  [storeNewKey]: '', // [storeNewKey]: { aliImgUrl: '' },
                  aliOriginImgUrl: key.record.aliOriginImgUrl,
                }
                sessionStorage.store = JSON.stringify(store)

                list.push(key)
              } else {
                console.log('图片上传失败')
                messageSimple.error('图片上传失败')
                key.record.loading = false
                key.record.status = 'fail'
              }
            })
            .catch((error: any) => {
              console.error('图片上传失败', error)
              messageSimple.error('图片上传失败', error.message)
              key.record.loading = false
              key.record.status = 'fail'
            })

          return uploadPromises.push(upload)
        }
      })
      // 2、上传后端的图片转换
      if (!uploadPromises.length) return
      Promise.all(uploadPromises)
        .then(() => {
          if (!list.length) return

          convertImages({
            // debug: true,
            uid: userStore.user.id,
            list: JSON.stringify(
              list.map((key: any) => {
                return {
                  ...key,
                  file: undefined,
                  record: {
                    ...key.record,
                    imgUrl: undefined,
                  },
                }
              })
            ),
          })
            .then((res: any) => {
              // console.log('convertImagesSuccess', res);
              if (res.data.code === 0) {
                const { uploadedImages } = res.data.data
                uploadedImages &&
                  uploadedImages.forEach((uploadImage: any) => {
                    const { key, url } = uploadImage
                    let originInfo = this.imgsMap.get(key)
                    let record = originInfo.record || {}
                    record.aliImgUrl = url.replace('http://', 'https://')
                    record.status = 'success'
                    this.imgsMap.set(key, originInfo)

                    const storeOriginKey = `${originInfo.webFileSize}_${originInfo.originFile}`
                    const storeNewKey = `${originInfo.webFileSize}_${originInfo.newFile}`
                    store[storeOriginKey] = {
                      ...store[storeOriginKey],
                      [storeNewKey]: record.aliImgUrl || '', // [storeNewKey]: { aliImgUrl: record.aliImgUrl || '' }
                    }
                  })
                messageSimple.success('已处理，请点击保存按钮')
              } else {
                console.error('图片转换异常，请重试', res.data)
                messageSimple.error('图片转换异常，请重试')
                list.forEach((key: any) => {
                  key.record.status = 'fail'
                })
              }
            })
            .finally(() => {
              this.hidePictureItemLoading(targetImgsMap)
              sessionStorage.store = JSON.stringify(store)
            })
        })
        .catch((error) => {
          this.hidePictureItemLoading(targetImgsMap)
          console.error('图片转换异常，请重试', error)
          messageSimple.error('图片转换异常，请重试')
          list.forEach((key: any) => {
            key.record.status = 'fail'
          })
        })
    },
    watermarkConvert(targetImgsMap: any) {
      const myConvert = () => {
        // let base64Arr: any = []
        targetImgsMap.forEach(async (key: any) => {
          // 未转换过，才可以转换
          if (
            (!key.record.loading && key.record.status !== 'success') ||
            key.canConvert
          ) {
            delete key.canConvert

            if (!key.customWatermark.length) {
              key.record.loading = false
              return messageSimple.warning('请先添加水印！')
            } else {
              key.record.loading = true
            }

            // 大图时，保证loading先渲染
            await new Promise((resolve) => setTimeout(() => resolve(true), 50))

            await this.semAcquire(key.key)
            this.updateFilePath(key) // 确保输出文件路径是最新的

            // TODO 生成水印逻辑提取，应用到dom里面
            const maxWidth = 592 // 水印编辑弹框中，图片最大宽
            const maxHeight = 646 // 水印编辑弹框中，图片最大高
            let rate = key.image.width / key.image.height
            let scale = // 原图缩放倍速
              rate > 1
                ? key.image.width / maxWidth
                : key.image.height / maxHeight

            var canvas: HTMLCanvasElement = document.createElement('canvas')
            var ctx: CanvasRenderingContext2D | null = canvas.getContext('2d')
            canvas.width = key.image.width
            canvas.height = key.image.height
            ctx?.drawImage(key.image, 0, 0)

            key.customWatermark.forEach((watermark: any) => {
              const {
                scaleFactor,
                leftRotateOffset,
                topRotateOffset,
                width,
                height,
              } = watermark.watermarkCanvas
              let { left, top, leftPercent, topPercent } =
                watermark.canvas.style
              // 如果存在百分比，重新计算位置
              if (leftPercent || topPercent) {
                const showWidth = rate > 1 ? maxWidth : maxHeight * rate
                const showHeight = rate > 1 ? maxWidth / rate : maxHeight
                watermark.canvas.style.left = (showWidth * leftPercent) / 100
                watermark.canvas.style.top = (showHeight * topPercent) / 100
                left = watermark.canvas.style.left
                top = watermark.canvas.style.top
                delete watermark.canvas.style.leftPercent
                delete watermark.canvas.style.topPercent
              }

              // 缩放位置2
              const actualLeft = (left - leftRotateOffset / scaleFactor) * scale
              const actualTop = (top - topRotateOffset / scaleFactor) * scale

              ctx?.save()
              if (watermark.canvas.style.watermarkPositionType === '平铺') {
                var tileWidth = (width * scale) / scaleFactor
                var tileHeight = (height * scale) / scaleFactor

                console.log(watermark.canvas.style)
                // watermark.canvas.style.gap // 上、右间距
                // watermark.canvas.style.intersect // 隔行间距
                if (watermark.canvas.style.intersect) {
                  // ① 间距、隔行平铺 TODO 如果下面可以解决问题，这里可以去除
                  var rowOffset: number = 0 // 初始行偏移量为
                  for (var y = 0; y < canvas.height; y += tileHeight) {
                    for (
                      var x = rowOffset;
                      x < canvas.width;
                      x += tileWidth * 2
                    ) {
                      ctx?.drawImage(
                        watermark.watermarkCanvas,
                        x,
                        y,
                        tileWidth,
                        tileHeight
                      )
                      if (x + tileWidth < canvas.width) {
                        // 避免超出画布边界
                        ctx?.drawImage(
                          watermark.watermarkCanvas,
                          x + tileWidth,
                          y,
                          tileWidth,
                          tileHeight
                        )
                      }
                    }
                    // 每行后偏移一个砖块宽度，实现交错效果
                    rowOffset =
                      rowOffset > 0 ? 0 : watermark.canvas.style.intersect
                  }
                } else {
                  // ② 平铺 TODO gap和rowOffset组合rowOffset失效
                  var initGap = 50
                  var gap = watermark.canvas.style.gap
                  var rowOffset = 0
                  for (
                    var x = 0;
                    x < canvas.width;
                    x += tileWidth + initGap + gap + rowOffset
                  ) {
                    for (
                      var y = 0;
                      y < canvas.height;
                      y += tileHeight + initGap + gap
                    ) {
                      ctx?.drawImage(
                        watermark.watermarkCanvas,
                        x,
                        y,
                        tileWidth,
                        tileHeight
                      )
                    }
                    rowOffset =
                      rowOffset > 0 ? 0 : watermark.canvas.style.intersect
                  }
                }
              } else {
                // ③ 水印真实位置
                ctx?.drawImage(
                  watermark.watermarkCanvas,
                  actualLeft,
                  actualTop,
                  (width * scale) / scaleFactor,
                  (height * scale) / scaleFactor
                )
              }
              ctx?.restore()
            })
            // console.log(key, rate, scale)

            console.time('watermark-download：' + key.key)
            let format = key.record.newFormat
            // marks 提高压缩效率，jpeg比jpg压缩效率高
            format =
              format.includes('png') || format.includes('webp')
                ? format
                : 'jpeg'

            if (window.isElectron) {
              // key.record.status = 50 // 水印打成功，进度设置为50%
              window.ipcRendererApi?.send('base64-img-to-file', {
                ...JSON.parse(JSON.stringify(key)),
                base64: canvas.toDataURL(`image/${format}`, key.quality || 0.9),
              })

              // base64Arr.push({
              //   ...JSON.parse(JSON.stringify(key)),
              //   base64: canvas.toDataURL(`image/${format}`, key.quality || 0.9),
              // })
            } else {
              // previewImageByCanvas(canvas) // 浏览器预览
              // 注意：这里下载是异步的
              downloadImageByCanvas(canvas, {
                format: `image/${format}`,
                name: `${key.filename || Date.now()}.${format}`,
                quality: key.quality || 0.9,
                key: key.key,
              })

              if (key.quality < 0.9) {
                messageSimple.warning('图片下载中，时间可能较长，请稍等')
              }

              this.semRelease(key.key)

              // console.log(base64)
              key.record.status = 'success'
              // 缩放位置3
              // key.record.status = 'wait'
              key.record.loading = false
            }
          }
        })
      }

      const watermarkStore = useWatermarkStore()
      if (watermarkStore.loading) {
        // 先启动loading
        targetImgsMap.forEach((key: any) => {
          if (!key.record.loading && key.record.status !== 'success') {
            key.record.loading = true
            key.canConvert = true
          }
        })

        // 监听loading是否结束，结束后开始转换
        setIntervalHandler({
          timeout: 500,
          canCallback: () => !watermarkStore.loading,
          callback: () => myConvert(),
        })
      } else {
        myConvert()
      }
    },
    pdfConvert(targetImgsMap: any, options: any) {
      this.loading = true
      options.outputPath = this.filePath
      const maps = mapToArray(targetImgsMap).filter((item) => item.checked)

      // TODO PDF can limit 张数
      // params.maps.length = 50
      // messageSimple.warning('最多添加50张图片')

      const params = {
        maps,
        options,
      }
      // console.log(params)
      window.ipcRendererApi?.send('magick-pdf', params)
    },
    // 确保输出文件路径是最新的
    updateFilePath(key: any) {
      key.newFile = `${this.filePath}${key.newFileName}.${key.record.newFormat}`
    },
    /** 去支付 */
    goPay,
    // 删除图片
    deleteImg(key: any) {
      const _this = this
      ElMessageBox.confirm('只删除列表记录，不删除文件', '确认删除该图片？', {
        distinguishCancelAndClose: true,
        confirmButtonText: '确定', // 保留
        cancelButtonText: '取消', // 不保留
        customClass: 'customStyleByMessageBox',
        type: 'warning',
        // center: true
      })
        .then((e: any) => {
          if (e === 'confirm') {
            _this.imgsMap.delete(key)
            messageSimple.success('删除成功')
          }
        })
        .catch((e: any) => {})
    },
    bindEvent() {
      // 选择文件或图片结果返回
      this.unBindEvent()
      window.ipcRendererApi?.on('select-callback', () => this.loading = true)
    },
    unBindEvent() {
      window.ipcRendererApi?.removeAllListeners('select-callback')
    },
    /** 获取拖拽的文件 */
    dragHandler() {
      dragHandler(this.imageInfoHandler)
    },
    /** 获取拖拽的文件夹 */
    onDragChange(e: any) {
      onDragChange(e, this.imageInfoHandler)
    },
    /** web端选择图片 */
    async chooseWebImages(choose: any = {}) {
      this.loading = true
      chooseWebImages(choose, this.imageInfoHandler)
    },
    /** 获取图片信息，返回缩略图、原始图宽高 */
    async getImageInfo(content: File | Blob) {
      if (!content) return Promise.resolve({})
      return getImageInfo(content)
    },
    /** 修改图片转换格式 */
    newFormatChange(key: any, newFormat: string) {
      key.multiType = this.getMultiType(newFormat, key.record.originFormat)
      key.newFile = `${this.filePath}${key.newFileName}.${newFormat}`
      key.record.newFormat = newFormat
      key.record.loading = false
      key.record.status = 'wait'
      if (key.record.newFormat.includes('ico')) {
        key.record.icoSizeValue = icoSizeValue
      } else {
        delete key.record.icoSizeValue
      }
      delete key.record.aliImgUrl
      document.body.click()
      setTimeout(() => {
        document.body.click()
        this.newFormatBySearch = ''
      }, 50)
    },
    /** 修改所有图片转换格式 */
    newFormatAllChange(format: string) {
      const newFormat = format.toLowerCase()
      this.newFormatAll = newFormat

      this.imgsMap.forEach((key: any) => {
        // 转换成功后的，不支持修改格式
        if (key.record.status !== 'success') {
          key.multiType = this.getMultiType(newFormat, key.record.originFormat)
          key.newFile = `${this.filePath}${key.newFileName}.${newFormat}`
          key.record.newFormat = newFormat
          key.record.loading = false
          key.record.status = 'wait'
          if (key.record.newFormat.includes('ico')) {
            key.record.icoSizeValue = icoSizeValue
          } else {
            delete key.record.icoSizeValue
          }
          delete key.record.aliImgUrl
        }
      })

      document.body.click()
      setTimeout(() => {
        document.body.click()
        this.newFormatBySearch = ''
      }, 50)
    },

    // web
    webUpload,
    webUploadFolder,

    /** 隐藏每个处理中图片的loading */
    hidePictureItemLoading(targetImgsMap: any) {
      targetImgsMap.forEach((target: any) => {
        target.record.loading = false
      })
    },
    /** 下载图片 */
    download(key: any) {
      downloadImageByXhr(
        key.record.aliImgUrl,
        `${key.newFileName}.${key.record.newFormat}`
      )
    },
    /** 获取图片类型 */
    getMultiType(newFormat: string, originFormat: string) {
      let multiType: number | string = ''
      return multiType
    },
    /** 获取图片信息，并添加到 Map imgsMap 里面 */
    async imageInfoHandler(result: any) {
      if (!result) return
      this.loading = true

      const isCompress = this.pageType === pageType.compress
      const isWatermark = this.pageType === pageType.watermark
      const isPdf = this.pageType === pageType.pdf
      let compressStore: any
      if (isCompress) {
        compressStore = useCompressStore()
      }
      let watermarkStore: any
      if (isWatermark) {
        watermarkStore = useWatermarkStore()
      }

      // TODO PDF can limit 张数
      // res.length = 30
      // messageSimple.warning('最多使用30张图片')

      // result.forEach(async (res: any) => {})
      for (const res of result) {
        const { key, size, fileBuffer } = res
        let { originPixel, file } = res

        if (!this.imgsMap.has(key)) {
          const filenameFull = window.isMac
            ? key.split('/').pop()
            : key.split('\\').pop()
          const originFormat = filenameFull.split('.').pop()
          let newFormat = this.newFormatAll
          const filename = filenameFull.replace(`.${originFormat}`, '')
          let blob: any = null

          if (isCompress) {
            newFormat = originFormat
          }
          if (isWatermark) {
            newFormat = ['jpg', 'jpeg', 'png', 'webp', 'bmp'].includes(
              newFormat.toLowerCase()
            )
              ? newFormat.toLowerCase()
              : 'webp'
          }

          if (fileBuffer) { // && !originFormat.toLocaleLowerCase().includes('pdf')
            // 仅electron使用
            blob = new Blob([fileBuffer], {
              type: `image/${originFormat}`,
            })
          }
          file = file || blob || ''
          if (!file.size) return messageSimple.error('文件大小不能为0kb')
          const imgInfo: any = file.size && (await this.getImageInfo(file)) || {}
          const imgUrl = imgInfo.thumbnail || '' // 缩略图
          originPixel =
            originPixel || `${imgInfo.width || '--'}x${imgInfo.height || '--'}` // 像素
          const newFile = `${this.filePath}${filename}.${newFormat}`
          const multiType = this.getMultiType(newFormat, originFormat)

          let pictureParams: any = {
            key, // 图片路径key，如：                            /Users/<USER>/Desktop/***/1、首页.jpg
            filename, // 文件名，不含后缀，如：                   1、首页
            newFileName: filename, // 新的文件名，支持修改，如：  1、首页custom
            newFile, // 新的图片路径，如：                       /Users/<USER>/Desktop/图片转换器/1、首页.jpg
            originFile: key, // 原始图片路径，如：               /Users/<USER>/Desktop/***/1、首页.jpg
            // icoSizeType,  // 'originImg', 'customImg' ico格式使用
            record: {
              originFormat, // 原始图片后缀，如：                jpg
              newFormat, // 新的图片后缀，如：                   jpg
              status: 'wait',
              loading: false,
              edit: false,
              size,
              imgUrl, // 缩略图
              originPixel, // 像素
              // fileType: "folder",
              // isFrist: 1,
              // icoSizeValue: icoSizeValue // ico格式使用
            },
            showWatermark: this.showWatermark, // 是否打水印
          }
          if (pictureParams.record.newFormat.includes('ico')) {
            pictureParams.record.icoSizeValue = icoSizeValue
          }
          if (
            [
              pageType.convert,
              pageType.raw,
              pageType.heic,
              pageType.livp,
            ].includes(this.pageType)
          ) {
            pictureParams = {
              ...pictureParams,
              tempPath: this.tempPath, // 仅electron使用
              /* multiType type 为 commonInfo 时触发
                1. 合并转换所有帧
                2. 分开转换所有帧
                3. 只转第一帧
                4. 合并转换所有图层
                5. 分开转换所有图层 */
              multiType,
              type: 'commonInfo', // commonInfo 或 其他
              // colorType: "", // colorType 设置颜色空间
              // specialMergeAll: "", // 特殊格式，需要设置时填入格式名称
              // todo 选择尺寸，后期再说
              // icoSizeType: 'originImg',
              // mergeAll: false, // 未用到
              // isTwoFormat: false, // 输出格式也为多层多帧格式并且不是二次转换
              isBatch: true, // 是否批量操作
              cancel: false, // 是否取消操作
            }
            if (!window.isElectron) {
              pictureParams.file = file // marks 缓存file
            }
          }
          if (isCompress) {
            pictureParams = {
              ...pictureParams,
              compressType: compressStore.compressType, // 默认标准压缩
              clearVal: compressStore.compressRadio[compressStore.compressType], // 压缩比例，默认75
              whPercent: 100, // whPercent pixel 二选一，默认100%
              // pixel: { width: 0, height: 0 },
              // targetSize: '', // 压缩大小，非必填
              // rename: '',
              // newPath: newFile,
              // originPath: key,
            }
            pictureParams.record.targetSize =
              compressStore.getCompressSize(pictureParams)
          }
          if (isWatermark || isPdf) {
            // 设置压缩质量：大于2M时0.7，大于5M时0.6，大于8M时0.5，其他为0.9
            const sizeM = file.size / 1024 / 1024
            const quality =
              sizeM >= 2 ? (size >= 5 ? (size >= 8 ? 0.5 : 0.6) : 0.7) : 0.9 // 水印可适当放宽

            // 转PDF、打水印 设置需要展示原图
            pictureParams.file = file // marks 缓存file
            pictureParams.width = imgInfo.width
            pictureParams.height = imgInfo.height
            pictureParams.quality = quality

            // marks 缓存image、base64
            const options = { limitWidth: isPdf ? 4096 : 0 } // pdf限宽4k
            fileToCanvas(
              file,
              (resCanvas: any = {}) => {
                if (isPdf) {
                  // console.time('pdfToDataURL' + res.key)
                  const newFormat = 'jpeg' // marks 提高压缩效率，jpeg比jpg压缩效率高
                  pictureParams.record.newFormat = newFormat
                  pictureParams.image = resCanvas.image // 没必要耗时可以忽略
                  pictureParams.base64 = resCanvas.canvas.toDataURL(
                    `image/${pictureParams.record.newFormat}`,
                    pictureParams.quality
                  )
                  // console.timeEnd('pdfToDataURL' + res.key)
                } else {
                  pictureParams.image = resCanvas.image // 没必要耗时可以忽略，watermarkConvert
                }
              },
              options
            )
          }
          if (isPdf) {
            pictureParams = {
              ...pictureParams,
              rotate: 0, // 0deg
              opacity: 100,
              scaleX: 1, // 1 或 -1
              scaleY: 1, // 1 或 -1
              checked: true,
            }
          }
          if (isWatermark) {
            pictureParams.customWatermark = JSON.parse(
              JSON.stringify(
                watermarkStore.globalWatermark.customWatermark || []
              )
            )
          }
          if (!window.isElectron) {
            // 仅 web store 图片使用
            pictureParams.webFileSize = file.size || 0
          }

          this.imgsMap.set(key, pictureParams)
        } else {
          messageSimple.warning('已过滤同名文件！')
        }
      }
      // console.log('imgsMap', imgsMap.value)

      this.loading = false
    },
    /** 新的输出文件名 */
    newFileNameChange(key: string) {
      let target = this.imgsMap.get(key)
      const newFile = `${this.filePath}${target.newFileName}.${target.record.newFormat}`

      // 转化成功后支持修改文件名
      if (window.isElectron && target.record.status === 'success') {
        window.ipcRendererApi?.send('change-name', {
          oldPath: target.newFile,
          newPath: newFile,
        })
      }

      target.newFile = newFile
      target.record.loading = false
    },
    // 判断是否有进行中的任务
    hasLoading() {
      let loading = false
      this.imgsMap.forEach((img) => {
        if (img.record.loading) loading = true
      })
      const pictureAliStore = usePictureAliStore()
      if (this.loading || pictureAliStore.loading) loading = true
      return loading
    },

    // electron
    /**
     * 获取输出文件路径、临时文件路径
     * 设置处：filePath =、filePath.value =
     */
    async getPath() {
      const commonStore = useCommonStore()
      const paths: any = await commonStore.getPath()
      this.filePath = paths[0]
      this.tempPath = paths[1]
    },
    /** 选择图片 */
    electronUpload(e: any) {
      const isWatermark = this.pageType === pageType.watermark
      const isPdf = this.pageType === pageType.pdf
      let options = {}
      if (isWatermark) { options = { exts: imgWatermarkExts } }
      if (isPdf) { options = { exts: imgToPdfExts } }

      window.ipcRendererApi
        ?.invoke('select-pic', options)
        .then((res: any) => this.imageInfoHandler(res))
    },
    /** 选择文件夹 */
    electronUploadFolder(e: any) {
      window.ipcRendererApi
        ?.invoke('select-folder')
        .then((res: any) => this.imageInfoHandler(res))
    },
    /** 打开输出文件 */
    electronOpenFile(e: any, filePath: string | undefined) {
      const commonStore = useCommonStore()
      commonStore.electronOpenFile(e, filePath)
    },
    /** 打开文件夹 */
    electronOpenFolder(e: any) {
      const commonStore = useCommonStore()
      commonStore.electronOpenFolder(e)
    },
    /** 取消转换 */
    magickCancel(key: any) {
      const [, value] = key

      window.ipcRendererApi?.send('magick-cancel', value.originFile)
      this.semRelease(value.key)

      value.record.loading = false
      value.record.status = 'cancel'
    },
    /**
     * 转换或压缩进度回调
     * @param type 'magick-execute-progress'、'magick-compress-progress'
     */
    progressCallback(type = 'magick-execute-progress') {
      if (!window.isElectron) return

      // 不根据_this.imgsMap，根据_this[`${_this.pageType}ImgsMap`]
      const _this: any = this
      window.ipcRendererApi?.removeAllListeners(type)
      window.ipcRendererApi?.on(type, (event: any, res: any) => {
        // console.log(res.status, res.statusType)
        const key = res.key

        if (res.statusType === 'load') {
          res.status = res.status / 2
        }
        if (res.statusType === 'save') {
          res.status = res.status / 2 + 50
        }
        if (['success', 'fail', 'cancel', 'wait'].includes(res.status)) {
          res.record.loading = false
          // console.timeEnd(res.newFile)
          // console.log(type, res)
          if (['fail'].includes(res.status)) {
            console.error(type, res)
          }
          if (['cancel'].includes(res.status)) {
            console.log('cancel')
          }

          _this.semRelease(key)
        } else {
          if (!res.status && res.status !== 0) {
            // 库加载失败
            if (res.dataStr && res.dataStr.includes('Library not loaded')) {
              alert(res.dataStr)

              _this.semRelease(key)
            }

            return console.log(res)
          }
        }

        // 更新状态
        let originInfo = _this.imgsMap.get(key)
        let record = originInfo.record || {}
        record.status = res.status
        record.loading = res.record.loading
        _this.imgsMap.set(key, originInfo)
      })
    },
    /**
     * 水印进度回调
     */
    waterProgressCallback() {
      if (!window.isElectron) return

      // 不根据_this.imgsMap，根据_this[`${_this.pageType}ImgsMap`]
      const _this: any = this
      window.ipcRendererApi?.removeAllListeners('base64-img-to-file-progress')
      window.ipcRendererApi?.on(
        'base64-img-to-file-progress',
        (event: any, res: any) => {
          console.log(res)
          const key = res.key

          _this.semRelease(key)
          console.timeEnd('watermark-download：' + key)

          // 更新状态
          const status = res.status
          let originInfo = _this.imgsMap.get(key)
          let record = originInfo.record || {}
          record.status = status
          record.loading = false
          _this.imgsMap.set(key, originInfo)
        }
      )
    },
  },
})
