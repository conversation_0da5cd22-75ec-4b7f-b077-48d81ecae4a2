import { defineStore } from 'pinia'
import { messageSimple } from '@/utils/message'
// import { ElMessage, ElMessageBox } from 'element-plus'
import { useCommonStore } from '@/pinia/common'
import {
  getImageInfo,
  // fileToCanvas,
} from '@/utils'
import { mapToArray } from '@/utils/util'
import { pageType } from '@/utils/enum'
import { chooseWebImages, dragHandler, onDragChange } from '@/utils/drag'
import { imgWatermarkExts } from '@/utils/enum/configData'
import { goLogin, goPay } from '@/utils/user'

let currentImgSize = 0
export const useBatchRenameStore = defineStore({
  id: 'batchRename',
  state: () => ({
    imgsMap: new Map(),
    startNum: 1, // 起始序号
    gap: 1, // 间隔数
    prefix: '图片名称',
    tip: '开始处理',
    pageType: '',
    checkAll: true,
    loading: false,
  }),
  getters: {},
  actions: {
    init(name: any) {
      // this.loading = false
      this.pageType = pageType[name as keyof typeof pageType]
      this.bindEvent()
      setTimeout(this.dragHandler, 1000)
    },
    bindEvent() {
      // 选择文件或图片结果返回
      this.unBindEvent()
      window.ipcRendererApi?.on('select-callback', () => (this.loading = true))
    },
    unBindEvent() {
      window.ipcRendererApi?.removeAllListeners('select-callback')
    },
    /** 获取拖拽的文件 */
    dragHandler() {
      dragHandler(this.imageInfoHandler)
    },
    /** 获取拖拽的文件夹 */
    onDragChange(e: any) {
      onDragChange(e, this.imageInfoHandler)
    },
    /** web端选择图片 */
    chooseWebImages(choose: any = {}) {
      this.loading = true
      chooseWebImages(choose, this.imageInfoHandler)
    },
    /** 获取图片信息，返回缩略图、原始图宽高 */
    async getImageInfo(content: File | Blob) {
      if (!content) return Promise.resolve({})
      return getImageInfo(content)
    },
    /** 获取图片信息，并添加到 Map imgsMap 里面 */
    async imageInfoHandler(result: any) {
      if (!result) return
      this.loading = true

      const commonStore = useCommonStore()
      const filePath = commonStore.filePath
      const showWatermark = commonStore.watermark()

      // TODO PDF can limit 张数
      // res.length = 30
      // messageSimple.warning('最多使用30张图片')

      // result.forEach(async (res: any) => {})
      for (const res of result) {
        const { key, size, fileBuffer } = res
        let { originPixel, file } = res

        const filenameFull = window.isMac
          ? key.split('/').pop()
          : key.split('\\').pop()
        const originFormat = filenameFull.split('.').pop()

        if (imgWatermarkExts.includes(originFormat.toLowerCase())) {
          if (!this.imgsMap.has(key)) {
            const filename = filenameFull.replace(`.${originFormat}`, '')
            let blob: any = null

            if (fileBuffer) {
              // 仅electron使用
              blob = new Blob([fileBuffer], {
                type: `image/${originFormat}`,
              })
            }
            file = file || blob || ''
            // if (!file.size) return messageSimple.error('文件大小不能为0kb')
            const imgInfo: any =
              (file.size && (await this.getImageInfo(file))) || {}
            const newFormat = imgInfo.format || 'jpg'
            const imgUrl = imgInfo.thumbnail || '' // 缩略图
            originPixel =
              originPixel ||
              `${imgInfo.width || '--'}x${imgInfo.height || '--'}` // 像素
            const newFile = `${filePath}${filename}.${newFormat}`

            let pictureParams: any = {
              key, // 图片路径key，如：                            /Users/<USER>/Desktop/***/1、首页.jpg
              filename, // 文件名，不含后缀，如：                   1、首页
              newFileName: filename, // 新的文件名，支持修改，如：  1、首页custom
              newFile, // 新的图片路径，如：                       /Users/<USER>/Desktop/图片转换器/1、首页.jpg
              originFile: key, // 原始图片路径，如：               /Users/<USER>/Desktop/***/1、首页.jpg
              // icoSizeType,  // 'originImg', 'customImg' ico格式使用
              record: {
                originFormat, // 原始图片后缀，如：                jpg
                newFormat, // 新的图片后缀，如：                   jpg
                status: 'wait',
                loading: false,
                edit: false,
                size,
                imgUrl, // 缩略图
                originPixel, // 像素
                // fileType: "folder",
                // isFrist: 1,
                // icoSizeValue: icoSizeValue // ico格式使用
              },
              checked: true,
              showWatermark, // 是否打水印
            }
            // fileToCanvas(file, (resCanvas: any) => {
            //   pictureParams.canvas = resCanvas.canvas
            // })

            this.imgsMap.set(key, pictureParams)
          } else {
            messageSimple.warning('已过滤同名文件！')
          }
        } else {
          console.log('已过滤不支持的文件格式：' + originFormat)
          messageSimple.warning('已过滤不支持的文件格式：' + originFormat)
        }
      }
      // console.log('imgsMap', this.imgsMap)

      this.loading = false
    },
    beforeConvert(targetImgsMap: any) {
      if (goLogin()) return

      const commonStore = useCommonStore()
      const showWatermark = commonStore.watermark()
      showWatermark ? goPay() : this.convert(targetImgsMap)
    },
    convert(targetImgsMap: any) {
      if (!window.isElectron) {
        return messageSimple.warning('需要下载app')
      }

      this.loading = true
      let imgs: any = new Map()
      let index = 0
      ;(targetImgsMap || this.imgsMap).forEach((key: any) => {
        if (key.checked) {
          const changeName = this.getName(index)
          key.newFile = `${key.newFile
            .split('/')
            .slice(0, -1)
            .join('/')}/${changeName}.${key.record.originFormat}`
          // key.oldPath = key.originFile
          // key.newPath = key.newFile
          imgs.set(key.key, key)
        }
        index++
      })
      currentImgSize = imgs.size
      this.batchSave(imgs)
    },
    batchSave(targetImgsMap: any) {
      // window.ipcRendererApi?.send('batch-base64-img-to-file', {
      window.ipcRendererApi?.send('batch-change-name', {
        maps: [...mapToArray(targetImgsMap)],
        options: {},
      })
    },
    batchSaveCallback() {
      if (!window.isElectron) return
      const _this = this
      window.ipcRendererApi?.removeAllListeners('batch-change-name')
      window.ipcRendererApi?.on('batch-change-name', (event: any, res: any) => {
        console.log(res)
        currentImgSize--
        if (currentImgSize === 0) {
          messageSimple.success('转换成功')
          _this.loading = false

          const commonStore = useCommonStore()
          commonStore.showConvertSuccessDialog = true
        }
      })
    },
    getName(index: number) {
      const nums = Number(this.startNum) + Number(this.gap) * index
      return this.prefix + nums
    },
  },
})
