import { defineStore } from 'pinia'
import { isValidVip } from '@/utils/user'
import { webUpload, webUploadFolder } from '@/utils'

export const useCommonStore = defineStore('common', {
  state: () => ({
    filePath: '',
    tempPath: '',
    loading: false,
    showWatermark: true,
    showExportDirectoryDialog: false,
    showConvertSuccessDialog: false,
  }),
  getters: {},
  actions: {
    /** 是否显示水印 */
    watermark() {
      this.showWatermark = !isValidVip()
      return this.showWatermark
    },
    // web
    /** 选择图片 */
    webUpload,
    /** 选择文件夹 */
    webUploadFolder,
    // electron
    /**
     * 选择图片
     * @param options { singleSelections, exts } // imgExts
     * singleSelections单选还是多选 true or false、exts限定图片格式
     */
    electronUpload(e: any, callback: Function, options: any = {}) {
      this.createPath([this.filePath, this.tempPath])
      window.ipcRendererApi
        ?.invoke('select-pic', options)
        .then((res: any) => callback && callback(res))
    },
    /**
     * 选择文件夹
     * @param e 
     * @param callback 
     * @param options { singleSelections, exts } // imgExts
     * singleSelections单选还是多选 true or false、exts限定图片格式
     */
    electronUploadFolder(e: any, callback: Function, options: any = {}) {
      this.createPath([this.filePath, this.tempPath])
      window.ipcRendererApi
        ?.invoke('select-folder', options)
        .then((res: any) => callback && callback(res))
    },
    /** 打开输出文件 */
    electronOpenFile(e: any, filePath: string | undefined) {
      this.createPath([filePath || this.filePath, this.tempPath])
      window.ipcRendererApi?.send('open-file', filePath || this.filePath)
    },
    /** 打开文件夹 */
    electronOpenFolder(e?: any) {
      this.createPath([this.filePath, this.tempPath])
      window.ipcRendererApi?.send('open-folder', this.filePath)
    },
    /** 创建指定的文件夹 */
    createPath(paths: string[] = []) {
      if (!window.isElectron) return
      window.ipcRendererApi?.invoke('create-path', paths)
    },
    /** 获取输出文件路径和临时文件路径 */
    async getPath() {
      if (!window.isElectron) return [this.filePath, this.tempPath]

      if (!this.filePath) {
        this.filePath =
          localStorage.filePath ||
          (await window.ipcRendererApi?.invoke('get-out-path'))
        localStorage.filePath = this.filePath
      }

      if (!this.tempPath) {
        this.tempPath = await window.ipcRendererApi?.invoke('get-temp-path')
      }

      return [this.filePath, this.tempPath]
    },
  },
})
