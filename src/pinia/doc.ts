// doc
// "type": 1, // 1 文件 2 文件夹

import { defineStore } from 'pinia'
import {
  defaultDocList
} from '@/mock/doc'
import { GetRecycleList, RemoveRecycleFile, DeleteRecycleFile, ClearRecycleFile } from '@/api/recycle'
import { GetFavoriteList, Star, UnStar } from '@/api/favorite'
import {
  GetDirectoryTree, GetOpenFolder, CreateFolder, SaveFile, GetFile, BatchMove, BatchCopy
} from '@/api/doc'
import { CreateRecycleFile } from '@/api/recycle'

export const useDocStore = defineStore({
  id: 'doc',
  state: () => ({
    favoriteList: [] as any,
    list: defaultDocList as any,
    folderList: [] as any,
    recycleList: [] as any,
  }),
  getters: {
    // getCollect: (state) => state.collect,
    // getList: (state) => state.list,
    // getRecycle: (state) => state.recycle,
  },
  actions: {
    // 获取目录树
    GetDirectoryTree() {
      console.log('GetDirectoryTree')
      return GetDirectoryTree()
        .then((_: any) => {
          if (_.data.code === 0) {
            const data = _.data.data
            const list = data.map((_: any) => {
              return {
                ..._,
                name: _.name || '新建思维导图',
              }
            })
            // console.log(list, data)
            this.setFolderList(list)
          }
        })
        .catch(err => {
          console.log(err)
        })
    },
    // 获取当前目录文件和文件夹
    GetOpenFolder(params?: any) {
      console.log('GetOpenFolder', params && params.folder_id)
      return GetOpenFolder(params)
        .then((_: any) => {
          if (_.data.code === 0) {
            const data = _.data.data
            const list = data.items.map((_: any) => {
              return {
                ..._,
                name: _.name || '新建思维导图',
                date: new Date(_.visit_time_str).getTime(),
                action: '...',
                contentEditable: false
                // top: false,
              }
            })
            // console.log(list, data)

            this.setList(list)
            return _
          }
        })
        .catch(err => {
          console.log(err)
        })
    },
    // 创建或更新文件夹
    CreateFolder(params: any) {
      console.log('CreateFolder')
      return CreateFolder(params)
        .then((_: any) => {
          if (_.data.code === 0) {
            return _
          }
          return false
        })
        .catch(err => {
          console.log(err)
        })
    },
    // 创建或更新文件
    SaveFile(params?: any) {
      console.log('SaveFile')
      return SaveFile({
        ...params
      })
        .then((_: any) => {
          if (_.data.code === 0) {
            return _
          }
          return false
        })
        .catch(err => {
          console.log(err)
        })
    },
    // 获取文件内容，未使用
    GetFile({
      id,
      uid,
    }: any) {
      return GetFile({
        file_id: id,
        user_id: uid,
      })
        .then((_: any) => {
          if (_.data.code === 0) {
          }
        })
        .catch(err => {
          console.log(err)
        })
    },
    BatchMove(params?: any) {
      return BatchMove({
        data: [...params]
      })
        .then((_: any) => {
          if (_.data.code === 0) {
            return true
          }
          return false
        })
        .catch(err => {
          console.log(err)
        })
    },
    BatchCopy(params?: any) {
      return BatchCopy({
        data: [...params]
      })
        .then((_: any) => {
          if (_.data.code === 0) {
            return true
          }
          return false
        })
        .catch(err => {
          console.log(err)
        })
    },
    // 获取收藏夹列表
    GetFavoriteList(params?: any) {
      console.log('GetFavoriteList')
      return GetFavoriteList()
        .then((_: any) => {
          if (_.data.code === 0) {
            const data = _.data.data
            const list = data.items.map((_: any) => {
              return {
                ..._,
                name: _.name,
                date: new Date(_.updated_str).getTime(),
                action: '...',
                star: true,
                contentEditable: false,
                top: false,
              }
            })
            // console.log(list, data)
            this.setFavoriteList(list)
          }
        })
        .catch(err => {
          console.log(err)
        })
    },
    // 收藏
    Star(params?: any) {
      console.log('Star')
      return Star(params)
        .then((_: any) => {
          if (_.data.code === 0) {
            this.GetFavoriteList({
              file_id: params && params.file_id || undefined
            })
            return true
          }
          return false
        })
        .catch(err => {
          console.log(err)
        })
    },
    // 取消收藏
    UnStar(params?: any) {
      console.log('UnStar')
      return UnStar(params)
        .then((_: any) => {
          if (_.data.code === 0) {
            this.GetFavoriteList({
              file_id: params && params.file_id || undefined
            })
            return true
          }
          return false
        })
        .catch(err => {
          console.log(err)
        })
    },
    // 移入回收站
    CreateRecycleFile({
      details,
    }: any) {
      return CreateRecycleFile({
        details,
      })
        .then((_: any) => {
          return _
        })
        .catch(err => {
          console.log(err)
        })
    },
    // 获取回收站列表
    GetRecycleList(params?: any) {
      console.log('GetRecycleList')
      return GetRecycleList()
        .then((_: any) => {
          if (_.data.code === 0) {
            const data = _.data.data
            const list = data.items.map((_: any) => {
              return {
                ..._,
                name: _.name,
              }
            })
            // console.log(list, data)
            this.setRecycleList(list)
          }
        })
        .catch(err => {
          console.log(err)
        })
    },
    // 从回收站移除，还原
    RemoveRecycleFile(params?: any) {
      return RemoveRecycleFile(params)
        .then((_: any) => {
          if (_.data.code === 0) {
            return true
          }
          return false
        })
        .catch(err => {
          console.log(err)
        })
    },
    // 从回收站彻底删除
    DeleteRecycleFile(params?: any) {
      return DeleteRecycleFile(params)
        .then((_: any) => {
          if (_.data.code === 0) {
            return true
          }
          return false
        })
        .catch(err => {
          console.log(err)
        })
    },
    // 清空回收站
    ClearRecycleFile() {
      return ClearRecycleFile()
        .then((_: any) => {
          if (_.data.code === 0) {
            return true
          }
          return false
        })
        .catch(err => {
          console.log(err)
        })
    },
    setList(list: Array<any>) {
      this.list = list
    },
    setFolderList(list: Array<any>) {
      this.folderList = list
    },
    setFavoriteList(list: Array<any>) {
      this.favoriteList = list
    },
    setRecycleList(list: Array<any>) {
      this.recycleList = list
    },
  }
})
