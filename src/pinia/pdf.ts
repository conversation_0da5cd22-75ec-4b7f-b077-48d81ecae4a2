import dayjs from 'dayjs'
import { defineStore } from 'pinia'
// import { messageSimple } from '@/utils/message'
// import { ElMessage, ElMessageBox } from 'element-plus'

export const usePdfStore = defineStore({
  id: 'pdf',
  state: () => ({
    paperSize: '', // A4、A3、''，默认原图
    orientation: 'horizontal', // horizontal、vertical，默认水平
    spacing: 0, // 0、50，默认无间距
    outputType: 'single', // single 单个pdf、multiple 多个pdf，默认输出为单个pdf
    outputName: '图片转PDF' + dayjs(new Date()).format('YYYYMMDDHHmmss'),
    checkedNums: 0,
    callbackNums: 0,
    currentPicture: {} as any,
    checkAll: false,
    showPdfPictureEdit: false,
    showPdfConvertEdit: false,
  }),
  getters: {},
  actions: {
    setCurrent(current) {
      this.currentPicture = current
    },
    /** 更新单个图片设置 */
    pdfSimpleChange({ rotate, transformX, transformY, opacity }) {
      // 旋转、下左右翻转
      if (this.currentPicture.record.status !== 'success') {
        this.currentPicture.record.rotate = rotate // 旋转度数，0-360
        // this.currentPicture.record.opacity = opacity // 透明度0-100
        this.currentPicture.record.transformX = !!transformX // 左右翻转
        this.currentPicture.record.transformY = !!transformY // 上下翻转
        this.currentPicture.record.status = 'wait'
      }
    },
  },
})
