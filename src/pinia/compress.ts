/**
 * 图片压缩相关
 */
import { defineStore } from 'pinia'
import { usePictureStore } from './picture'
// import { messageSimple } from '@/utils/message'
// import { ElMessage, ElMessageBox } from 'element-plus'

export const useCompressStore = defineStore({
  id: 'compress',
  state: () => ({
    compressType: '标准压缩', // 默认标准压缩
    compressRadio: {
      清晰优先: 85,
      标准压缩: 75,
      缩小优先: 60,
      自定义: 75,
    } as Record<string, number>,
    currentCompress: {} as any,
    showCompressDialog: false,
  }),
  getters: {},
  actions: {
    getCompressSize(key: any, simpleClearVal?: any) {
      const [, originSize, , unit] = key.record.size.match(/(\d+(\.\d+)?)(\w+)/)
      const clearVal = simpleClearVal || key.clearVal || 0
      const result = `${((originSize || 0) * clearVal / 100).toFixed(1)}${unit}`

      return result
    },
    setCurrent(currentKey: any) {
      this.currentCompress = currentKey
    },
    /** 更新压缩比例 */
    compressChange(clearVal: any) {
      const pictureStore = usePictureStore()
      pictureStore.imgsMap.forEach((key: any) => {
        // 转换成功后的，不支持修改格式
        if (key.record.status !== 'success') {
          key.compressType = this.compressType
          key.clearVal = clearVal
          key.record.targetSize = this.getCompressSize(key)
          key.record.loading = false
          key.record.status = 'wait'
        }
      })
    },
    /** 更新单个图片压缩比例 */
    compressSimpleChange(clearVal?: any, compressType?: string) {
      if (this.currentCompress.record.status !== 'success') {
        this.currentCompress.compressType = compressType
        this.currentCompress.clearVal = clearVal
        this.currentCompress.record.targetSize = this.getCompressSize(
          this.currentCompress,
          clearVal
        )
        this.currentCompress.record.loading = false
        this.currentCompress.record.status = 'wait'
      }
    },
    /** 取消压缩 */
    magickCancel(key: any) {
      const [, value] = key

      window.ipcRendererApi?.send('magick-compress-cancel', value.originFile)
      value.record.loading = false
      value.record.status = 'cancel'
    },
  },
})
