/**
 * 用户操作相关
 */

// demo
//  import { storeToRefs } from 'pinia'
//  import { useUserStore } from '@/pinia/user' // '@/pinia'
//  const userStore = useUserStore()
//  console.log(userStore, userStore.token, userStore.getMindNums)
//  const { mindNums } = storeToRefs(userStore)
//  console.log(mindNums.value)

// user
// "type": 1,//是否VIP, 1 未付费用户，2 季度会员，3 年费会员， 4 终身会员， 5 半年会员


import { GetTotal } from '@/api/doc'
import { defineStore } from 'pinia'

export const useUserStore = defineStore({
  id: 'user',
  state: () => ({
    token: localStorage.token || '',

    user: JSON.parse(localStorage.user || '{}'),
    // mindNums: 0, // 测试时使用
    mindNums: Number(sessionStorage.mindNums || 0),
    folderNums: Number(sessionStorage.folderNums || 0),
  }),
  getters: {
    getToken: (state) => state.token,
    getMindNums: (state) => state.mindNums
  },
  actions: {
    getTotal() {
      return GetTotal().then(_ => {
        if (_.data.code === 0) {
          this.updateNums(_.data.data)
        }
      })
    },
    updateNums(data: any = {}) {
      sessionStorage.mindNums = data.file_nums || 0
      sessionStorage.folderNums = data.folder_nums || 0

      this.mindNums = data.file_nums || 0
      this.folderNums = data.folder_nums || 0
    },
    setToken(token: string) {
      this.token = token
    },
    setUser(user: any = {}) {
      this.user = user
    }
  }
})
