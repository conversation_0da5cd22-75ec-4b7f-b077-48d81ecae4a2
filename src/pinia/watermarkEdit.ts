/**
 * 图片编辑相关
 */
import qs from 'qs'
import { defineStore } from 'pinia'
import { getDetail, saveDetail, getCategoryList } from '@/api/watermark'
import { messageSimple } from '@/utils/message'
const query = qs.parse(location.search.replace('?', ''))

export const useWatermarkEditStore = defineStore({
  id: 'watermarkEdit',
  state: () => ({
    canvas: {
      type: 'canvas', // img、text、canvas
      style: {
        opacity: 1,
        rotate: 0,
        zoom: 1,
        gap: 0,
        intersect: 0,
        watermarkPositionType: '自定义',
        width: 200,
        height: 200,
        backgroundColor: 'transparent', // #FFFFFF
      },
    } as any, // 画布数据
    list: [] as any, // 数据集合
    detail: {} as any, // 数据集合

    currentIndex: -1, // 当前选中的索引，-1是canvas
    currentType: 'canvas', // 当前选中的图层类型：img、text、canvas

    canvasRef: null as any, // 画布的ref
  }),
  getters: {},
  actions: {
    /**
     * 获取数据
     */
    getDetail() {
      // scale: 1, // 废弃
      // rotate: 0, // 废弃
      // opacity: 1, // 废弃

      return getDetail({
        id: query.id,
      })
        .then((_: any) => {
          if (_.data.code === 0) {
            delete _.data.content.shelve
            delete _.data.content.sticky
            delete _.data.content.updated_at
            delete _.data.content.created_at
            delete _.data.content.status
            delete _.data.content.sort
            delete _.data.content.name
            this.detail = _.data.content

            if (_.data.content.canvas) {
              this.canvas = JSON.parse(_.data.content.canvas)
            }
            if (_.data.content.list) {
              this.list = JSON.parse(_.data.content.list)
            }
          } else {
            messageSimple.error('详情获取失败')
            console.log(_)
          }
        })
        .catch((e) => {
          messageSimple.error('详情获取异常：', e.message)
          console.log(e)
        })
    },
    getCategoryList() {
      getCategoryList()
        .then((_: any) => {
          console.log(_)
          if (_.data.code === 0) {
          } else {
            messageSimple.error('详情获取失败')
            console.log(_)
          }
        })
        .catch((e) => {
          messageSimple.error('详情获取异常：', e.message)
          console.log(e)
        })
    },
    /**
     * 编辑或新增
     */
    saveDetail(canvas, list, detail) {
      console.log('save')
      return saveDetail({
        ...detail.value,
        canvas: JSON.stringify(canvas.value),
        list: JSON.stringify(list.value),
      }).catch((e) => {
        console.log(e)
        messageSimple.error('保存异常：', e.message)
      })
    },
  },
})
