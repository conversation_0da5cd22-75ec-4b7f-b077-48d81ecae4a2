import { defineStore } from 'pinia'
import { messageSimple } from '@/utils/message'
// import { ElMessage, ElMessageBox } from 'element-plus'
import { useCommonStore } from '@/pinia/common'
import { getImageInfo } from '@/utils'
import { mapToArray } from '@/utils/util'
import { pageType } from '@/utils/enum'
import { chooseWebImages, dragHandler, onDragChange } from '@/utils/drag'
import { imgWatermarkExts } from '@/utils/enum/configData'
import { goLogin, goPay } from '@/utils/user'
import { getListAll } from '@/api/batch'
// import { getCropImage } from '@/utils/image'
import type Cropper from 'cropperjs'

let currentImgSize = 0
export const useBatchCropStore = defineStore({
  id: 'batchCrop',
  state: () => ({
    imgsMap: new Map(),
    currentIndex: 0,
    categoryList: [] as any,
    currentImg: {
      record: {
        x1: 0,
        y1: 0,
        width: 0,
        height: 0,
      },
    } as any,
    cropper: null as Cropper | null,
    cropperRef: null as any,
    option: {
      guides: false, // 是否显示网格线
      movable: false, // 是否可以移动图片
      zoomable: false, // 是否可以缩放图片（以图片左上角为原点进行缩放）
      // 当 autoCrop属性 被设置为 true时, crop 事件将在 ready 事件之前被触发.
      // 当设置了 data 属性时, 另一个 crop 事件将在 ready 事件之前被触发.
      autoCrop: true, // 是否自动裁剪
      cropBoxMovable: true, // 是否允许通过拖动来移动裁剪框
      cropBoxResizable: false, // 是否允许通过拖动来调整裁剪框的大小
      // 'crop': 创建一个新的裁剪框
      // 'move': 图片容器可移动
      // 'none': 什么也不做
      dragMode: 'none',
      // 0: 没有限制
      // 1: 限制裁剪框不超过图片容器的范围。
      // 2: 限制最片容器尺寸以在裁剪容器中展示。 如果图片容器和裁剪容器的比例不同，则图片容器以cover模式填充（图片容器保持原有比例，最长边和裁剪容器大小一致，短边等比缩放，可能会有部分区域不可见)。
      // 3: 限制图片容器尺寸以在裁剪器中展示。 如果图片容器和裁剪容器的比例不同，则图片容器以contain模式填充（图片容器保持原有比例，最短边和裁剪容器大小一直，长边等比缩放，可能会有留白）。
      viewMode: 2,
      minCropBoxWidth: 25,
      minCropBoxHeight: 25,
    },
    pageType: '',
    applyToAll: false,
    loading: false,
    showSearchResult: true,
  }),
  getters: {},
  actions: {
    init(name: any) {
      // this.loading = false
      this.pageType = pageType[name as keyof typeof pageType]
      this.bindEvent()
      setTimeout(this.dragHandler, 1000)
    },
    bindEvent() {
      // 选择文件或图片结果返回
      this.unBindEvent()
      window.ipcRendererApi?.on('select-callback', () => (this.loading = true))
    },
    unBindEvent() {
      window.ipcRendererApi?.removeAllListeners('select-callback')
    },
    clear() {
      this.imgsMap.clear()
    },
    /** 获取拖拽的文件 */
    dragHandler() {
      dragHandler(this.imageInfoHandler)
    },
    /** 获取拖拽的文件夹 */
    onDragChange(e: any) {
      onDragChange(e, this.imageInfoHandler)
    },
    /** web端选择图片 */
    chooseWebImages(choose: any = {}) {
      this.loading = true
      chooseWebImages(choose, this.imageInfoHandler)
    },
    getCurrent() {
      const mapArray = Array.from(this.imgsMap.entries())
      if (!mapArray.length) return

      const [, target] = mapArray[this.currentIndex]
      this.currentImg = target

      return target
    },
    /** 获取图片信息，返回缩略图、原始图宽高 */
    async getImageInfo(content: File | Blob) {
      if (!content) return Promise.resolve({})
      return getImageInfo(content)
    },
    /** 获取图片信息，并添加到 Map imgsMap 里面 */
    async imageInfoHandler(result: any) {
      if (!result) return
      this.loading = true

      const commonStore = useCommonStore()
      const filePath = commonStore.filePath
      const showWatermark = commonStore.watermark()

      // TODO can limit 张数
      // res.length = 30
      // messageSimple.warning('最多使用30张图片')

      // result.forEach(async (res: any) => {})
      for (const res of result) {
        const { key, fileBuffer } = res
        let { file } = res

        const filenameFull = window.isMac
          ? key.split('/').pop()
          : key.split('\\').pop()
        const originFormat = filenameFull.split('.').pop()

        if (imgWatermarkExts.includes(originFormat.toLowerCase())) {
          if (!this.imgsMap.has(key)) {
            const filename = filenameFull.replace(`.${originFormat}`, '')
            let blob: any = null

            if (fileBuffer) {
              // 仅electron使用
              blob = new Blob([fileBuffer], {
                type: `image/${originFormat}`,
              })
            }
            file = file || blob || ''
            // if (!file.size) return messageSimple.error('文件大小不能为0kb')
            const imgInfo: any =
              (file.size && (await this.getImageInfo(file))) || {}
            const newFormat = imgInfo.format || 'jpg'
            const imgUrl = imgInfo.thumbnail || '' // 缩略图
            const newFile = `${filePath}${filename}.${originFormat}`

            let pictureParams: any = {
              key, // 图片路径key，如：                            /Users/<USER>/Desktop/***/1、首页.jpg
              filename, // 文件名，不含后缀，如：                   1、首页
              newFileName: filename, // 新的文件名，支持修改，如：  1、首页custom
              newFile, // 新的图片路径，如：                       /Users/<USER>/Desktop/图片转换器/1、首页.jpg
              originFile: key, // 原始图片路径，如：               /Users/<USER>/Desktop/***/1、首页.jpg
              record: {
                originFormat, // 原始图片后缀，如：                jpg
                newFormat, // 新的图片后缀，如：                   jpg
                status: 'wait',
                loading: false,
                imgUrl, // 缩略图

                // img.record.aspectRatio = currentImg.value.record.aspectRatio; // 记录比例
                // img.record.autoCropWidth = currentImg.value.record.autoCropWidth; // 自动裁剪宽度
                // img.record.autoCropHeight = currentImg.value.record.autoCropHeight; // 自动裁剪高度
                // img.record.pixel_with = currentImg.value.record.pixel_with; // 记录输出尺寸
                // img.record.pixel_height = currentImg.value.record.pixel_height; // 记录输出尺寸
                // img.record.scale = currentImg.value.record.scale; // 记录缩放
                // img.record.name = currentImg.value.name; // 记录尺寸选中名字
              },
              checked: true,
              showWatermark, // 是否打水印
              file,
            }
            // fileToCanvas(file, (result: any) => {
            //   pictureParams.image = result.image
            // })

            this.imgsMap.set(key, pictureParams)
          } else {
            messageSimple.warning('已过滤同名文件！')
          }
        } else {
          console.log('已过滤不支持的文件格式：' + originFormat)
          messageSimple.warning('已过滤不支持的文件格式：' + originFormat)
        }
      }
      // console.log('imgsMap', this.imgsMap)

      this.getCurrent()
      this.loading = false
    },
    beforeConvert(type: string) {
      if (goLogin()) return

      const commonStore = useCommonStore()
      const showWatermark = commonStore.watermark()
      showWatermark ? goPay() : this.convert(type)
    },
    async convert(type: any) {
      // if (!window.isElectron) {
      //   return messageSimple.warning('需要下载app')
      // }

      this.loading = true
      await new Promise((resolve) => setTimeout(() => resolve(true), 50))

      let tempCurrentIndex = this.currentIndex
      let imgs: any = new Map()
      let index = 0
      const isSingleImage = type === 'current'

      // mark crop
      for (const img of this.imgsMap) {
        const [, key] = img // [key, proxy]

        const options = key.record.pixel_with
          ? {
              width: key.record.pixel_with,
              height: key.record.pixel_height,
            }
          : {}
        // mark crop 未固定宽高，则增加等待输出图片的清晰度
        // {
        //   width: key.record.autoCropWidth * 2,
        //   height: key.record.autoCropHeight * 2,
        // }
        // console.log('cropper options', options)

        const sizeKB = key.file.size / 1024
        const isLargeImg = sizeKB > 1024 * 5
        const exportFormat = isLargeImg ? 'image/jpeg' : 'image/webp'
        if (isSingleImage) {
          if (index === this.currentIndex) {
            // test
            const base64 = this.cropper
              ?.getCroppedCanvas(options)
              .toDataURL(exportFormat, 0.9)
            key.base64 = base64
            imgs.set(key.key, {
              key: key.key,
              record: { ...key.record },
              base64: key.base64,
              originFile: key.originFile,
              newFile: key.newFile,
              filename: key.filename,
              showWatermark: key.showWatermark,
            })
          }
        } else {
          this.currentIndex = index
          // mark crop 大图增加等待时间
          let timeout = 500
          if (sizeKB > 1024 * 1) timeout = sizeKB / 2
          if (sizeKB > 1024 * 5) timeout = sizeKB / 3
          if (sizeKB > 1024 * 10) timeout = sizeKB / 4
          if (sizeKB > 1024 * 20) timeout = sizeKB / 6
          if (sizeKB > 1024 * 40) timeout = sizeKB / 8
          await new Promise((resolve) =>
            setTimeout(() => resolve(true), timeout)
          )
          // console.log('timeout', timeout)
          // this.downSimple()

          // 如果自己画（废弃）：1、img拉伸 2、按比例裁剪时需计算裁剪宽高 3、canvas画
          // var img = new Image()
          // img.crossOrigin = 'Anonymous'
          // img.onload = function () {
          //   ctx.drawImage(
          //     img,
          //     item.style.left,
          //     item.style.top,
          //     item.style.width,
          //     item.style.height
          //   )
          // }
          // img.src = item.img

          const base64 = this.cropper
            ?.getCroppedCanvas(options)
            .toDataURL(exportFormat, 0.9)
          key.base64 = base64
          imgs.set(key.key, {
            key: key.key,
            record: { ...key.record },
            base64: key.base64,
            originFile: key.originFile,
            newFile: key.newFile,
            filename: key.filename,
            showWatermark: key.showWatermark,
          })
        }
        index++
      }
      currentImgSize = isSingleImage ? 1 : imgs.size
      this.currentIndex = tempCurrentIndex

      if (!window.isElectron) {
        imgs.forEach((img: any) => {
          // console.log(img.base64)
          var aLink: any = document.createElement('a')
          aLink.download = img.filename || 'demo'
          aLink.href = img.base64
          aLink.click()
        })
        this.loading = false
      } else {
        this.batchSave(imgs)
      }
    },
    batchSave(targetImgsMap: any) {
      window.ipcRendererApi?.send('batch-base64-img-to-file', {
        maps: [...mapToArray(targetImgsMap)],
        options: {},
      })
    },
    batchSaveCallback() {
      if (!window.isElectron) return
      const _this = this
      window.ipcRendererApi?.removeAllListeners(
        'batch-base64-img-to-file-progress'
      )
      window.ipcRendererApi?.on(
        'batch-base64-img-to-file-progress',
        (event: any, res: any) => {
          console.log(res)
          currentImgSize--
          if (currentImgSize === 0) {
            messageSimple.success('转换成功')
            _this.loading = false

            const commonStore = useCommonStore()
            commonStore.showConvertSuccessDialog = true
          }
        }
      )
    },
    downSimple(type?: string) {
      var aLink: any = document.createElement('a')
      aLink.download = this.currentImg.filename || 'demo'

      if (type === 'blob') {
        this.cropper?.getCroppedCanvas().toBlob((data: any) => {
          aLink.href = window.URL.createObjectURL(data)
          aLink.click()
        })
      } else {
        const base64 = this.cropper?.getCroppedCanvas().toDataURL()
        aLink.href = base64
        aLink.click()
      }
    },
    getListAll(params?: any) {
      const isSearch = !!params
      if (!params?.name && sessionStorage.categoryList) {
        this.categoryList = JSON.parse(sessionStorage.categoryList)
        this.showSearchResult = !!this.categoryList.length
        return
      }
      getListAll(params)
        .then((_: any) => {
          if (_.data.code === 0 && _.data.data.length > 0) {
            let categoryList: any = [
              {
                id: 0,
                name: '自定义',
                list: [],
              },
            ]

            _.data.data.forEach((item: any) => {
              categoryList.push({
                id: item.category.id,
                name: item.category.name,
                status: item.category.status,
                sticky: item.category.name.sticky,
                list: item.size,
              })
            })

            this.categoryList = categoryList
            !isSearch && this.updateCategorySession()
          } else {
            this.categoryList = []
            console.log(_)
            // messageSimple.warning('未搜索到结果')
          }
          if (isSearch) this.showSearchResult = !!this.categoryList.length
        })
        .catch((e) => {
          messageSimple.error('搜索异常：', e.message)
          console.log(e)
        })
    },
    updateCategorySession() {
      sessionStorage.categoryList = JSON.stringify(this.categoryList)
    },
    updateCropBoxData() {
      if (!this.cropper) return
      const cropBoxData = this.cropper.getCropBoxData()
      this.currentImg.record.left = cropBoxData.left
      this.currentImg.record.top = cropBoxData.top
      this.currentImg.record.autoCropWidth = cropBoxData.width
      this.currentImg.record.autoCropHeight = cropBoxData.height
      // console.log('updateCropBoxData', cropBoxData)
    },
  },
})
