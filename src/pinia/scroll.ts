import { onMounted, onUnmounted } from 'vue-demi'

export const useElementScroll = (target: string, callback: any): Function | null => {
    if (!target) {
        throw new Error('目标元素不能为空')
    }

    let el: HTMLElement | null = null

    const cleanup = () => {
        el && el.removeEventListener('scroll', callback)
    }

    onMounted(() => {
        el = document.querySelector(target)
        el && el.addEventListener('scroll', callback, { passive: true })
    })

    onUnmounted(() => {
        cleanup()
    })

    return cleanup
}