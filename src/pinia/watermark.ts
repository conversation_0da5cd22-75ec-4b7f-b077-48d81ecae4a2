/**
 * 图片编辑相关
 * getCustomTextWatermark、getCustomImageWatermark，watermarkEdit 包含数据结构
 */
import { nextTick } from 'vue'
import { useDraggable } from '@vueuse/core'
import { defineStore } from 'pinia'
// import Compressor from 'compressorjs'
import { saveDetail, getListAll } from '@/api/watermark'
import { messageSimple } from '@/utils/message'
import { APPLOGO } from '@/utils/configData/config'
// import { loadImage, downloadImageByCanvas } from '@/utils'
// import html2canvas from 'html2canvas'

export const useWatermarkStore = defineStore({
  id: 'watermark',
  state: () => ({
    demoImgWidth: 592, // 水印编辑弹框中，demo图片的宽
    demoImgHeight: 395, // 水印编辑弹框中，demo图片的高
    maxWidth: 592, // 水印编辑弹框中，图片最大宽
    maxHeight: 646, // 水印编辑弹框中，图片最大高
    canvasImgWidth: 0, // 水印编辑弹框中，图片的宽
    canvasImgHeight: 0, // 水印编辑弹框中，图片的高
    canvasImg: null as any, // canvas img
    canvasImgRect: null as any, // canvas img 矩形信息
    draggables: [] as any, // canvas img 矩形信息
    watermarkRefs: [] as any, // canvas img 矩形信息
    /**
     * 当前选中的水印索引，-1是未选中
     */
    currentIndex: -1,
    /**
     * 分类水印集合
     */
    categoryWatermarkList: [] as any,
    /**
     * 当前图片水印集合
     */
    currentWatermark: {
      customWatermark: [],
    } as any,
    /**
     * 所有图片水印集合
     */
    globalWatermark: {
      customWatermark: [],
    } as any,
    // watermarkPositionType: '自定义',
    watermarkpositionRadio: {
      自定义: '自定义',
      九宫格: '九宫格',
      // 平铺: '平铺',
    } as any,
    minZoom: 0.5,
    loading: false,
    showWatermarkPictureEditDialog: false,
    isGlobal: false,
    isUpdate: false, // 水印是否已更新
  }),
  getters: {
    dialogWatermark: (state) => {
      return state.isGlobal
        ? state.globalWatermark.customWatermark
        : state.currentWatermark.customWatermark
    },
  },
  actions: {
    getCustomTextWatermark() {
      return {
        id: Date.now(),
        canvas: {
          isCustom: true,
          isText: true,
          type: 'canvas',
          style: {
            opacity: 1,
            rotate: 0,
            zoom: 1,
            gap: 0,
            intersect: 0,
            width: 150,
            height: 50,
            backgroundColor: 'transparent',
          },
        },
        list: [
          {
            type: 'text',
            style: {
              fontFamily: 'Microsoft YaHei',
              fontSize: 20,
              color: '#333',
              fontWeight: '',
              fontStyle: '',
              textDecoration: '',
              left: 5,
              top: 10,
            },
            text: '请输入水印文字',
          },
        ],
      }
    },
    getCustomImageWatermark() {
      return {
        id: Date.now(),
        canvas: {
          isCustom: true,
          type: 'canvas',
          style: {
            opacity: 1,
            rotate: 0,
            zoom: 1,
            gap: 0,
            intersect: 0,
            width: 120,
            height: 120,
            backgroundColor: 'transparent',
          },
        },
        list: [
          {
            type: 'img',
            style: { left: 10, top: 10, width: 100, height: 100 },
            img: APPLOGO,
          },
        ],
      }
    },
    setCurrent(currentKey: any) {
      this.currentWatermark = currentKey
    },
    setDialogWatermark(customWatermark: any) {
      // console.log('setDialogWatermark')
      if (this.isGlobal) {
        this.globalWatermark.customWatermark = customWatermark
      } else {
        this.currentWatermark.customWatermark = customWatermark
      }
    },
    updateCanvasImgSize() {
      if (this.isGlobal) {
        this.canvasImgWidth = this.demoImgWidth
        this.canvasImgHeight = this.demoImgHeight
      } else {
        let rate = this.currentWatermark?.width / this.currentWatermark?.height

        // 宽大于高
        // if (rate > 1) {
        //   this.canvasImgWidth = this.maxWidth
        //   const scale = this.canvasImgWidth / this.currentWatermark?.width
        //   this.canvasImgHeight = this.currentWatermark?.height * scale // 'auto'
        // } else {
        //   this.canvasImgHeight = this.maxHeight
        //   const scale = this.canvasImgHeight / this.currentWatermark?.height
        //   this.canvasImgWidth = this.currentWatermark?.width * scale // 'auto'
        // }

        // 宽大都取左侧，高大都取右侧
        this.canvasImgWidth = rate > 1 ? this.maxWidth : this.maxHeight * rate
        this.canvasImgHeight = rate > 1 ? this.maxWidth / rate : this.maxHeight
      }
    },
    sudoku(x: any, y: any) {
      const { left, top, width, height } = this.canvasImgRect
      const watermarkWidth = this.watermarkRefs[
        this.currentIndex
      ].style.width.replace('px', '')
      const watermarkHeight = this.watermarkRefs[
        this.currentIndex
      ].style.height.replace('px', '')

      const sectionWidth = width / 3
      const sectionHeight = height / 3
      const offsetWidth = (sectionWidth - watermarkWidth) / 2
      const offsetHeight = (sectionHeight - watermarkHeight) / 2
      const target = {
        x: left + (sectionWidth * x - watermarkWidth) - offsetWidth,
        y: top + (sectionHeight * y - watermarkHeight) - offsetHeight,
      }

      this.draggables[this.currentIndex].position.x = target.x
      this.draggables[this.currentIndex].position.y = target.y
    },
    async updateView(type?: any) {
      // console.log('updateView', type)

      this.canvasImg =
        this.canvasImg ||
        document.querySelector('.watermark-dialog .canvas-img')
      if (!this.canvasImg) return

      this.canvasImgRect = this.canvasImg.getBoundingClientRect()

      const { left, top, width, height } = this.canvasImgRect

      // 1、重置：draggables、watermarkRefs
      this.draggables.length = 0
      this.watermarkRefs.length = 0
      // console.log(222, canvasImgRect, draggables, watermarkRefs)

      // 2、更新重置后的dom信息
      await nextTick()

      return new Promise((resolve) => {
        // 3、生成 draggables、watermarkRefs
        this.dialogWatermark.forEach((watermark: any, index: number) => {
          const _this = this
          const ref = this.watermarkRefs[index] // watermark ref

          if (!ref) return
          // watermark.canvas.style.height * scale = ref.offsetHeight
          // watermark.canvas.style.width * scale = ref.offsetWidth
          const watermarkWidth = ref.offsetWidth
          const watermarkHeight = ref.offsetHeight
          if (
            !watermark.canvas.style.left &&
            watermark.canvas.style.left !== 0
          ) {
            watermark.canvas.style.left =
              (_this.canvasImgWidth - watermarkWidth) / 2 // 默认居中
          }
          if (!watermark.canvas.style.top && watermark.canvas.style.top !== 0) {
            watermark.canvas.style.top =
              (_this.canvasImgHeight - watermarkHeight) / 2 // 默认居中
          }

          // 百分比 恢复
          watermark.canvas.style.left = watermark.canvas.style.leftPercent
            ? (watermark.canvas.style.leftPercent / 100) * width
            : watermark.canvas.style.left || 0
          watermark.canvas.style.top = watermark.canvas.style.topPercent
            ? (watermark.canvas.style.topPercent / 100) * height
            : watermark.canvas.style.top || 0
          delete watermark.canvas.style.leftPercent
          delete watermark.canvas.style.topPercent

          const watermarkLeft = watermark.canvas.style.left
          const watermarkTop = watermark.canvas.style.top
          // console.log(
          //   JSON.stringify(watermark.canvas.style),
          //   watermarkLeft,
          //   watermarkTop,
          //   width,
          //   height,
          //   left,
          //   top,
          //   _this.canvasImgWidth,
          //   watermarkWidth,
          //   watermarkHeight
          // )

          // TODO 自增位置，暂不处理
          const initialValue = {
            x: left + watermarkLeft,
            y: top + watermarkTop,
          }

          _this.draggables[index] = useDraggable(ref, {
            onStart() {
              _this.currentIndex = index
            },
            /**
             * onMove
             * @param event 鼠标位置
             * @param info 背景图的左上角
             */
            onMove(event: any, info: any) {
              const rect = ref.getBoundingClientRect()
              const { x, y } = event
              const angle = watermark.canvas.style.rotate || 0
              const radians = (angle * Math.PI) / 180
              const rotateWidth =
                Math.abs(rect.width * Math.cos(radians)) +
                Math.abs(rect.height * Math.sin(radians))
              const rotateHeight =
                Math.abs(rect.height * Math.cos(radians)) +
                Math.abs(rect.width * Math.sin(radians))

              // TODO 偏移值目前计算的不准
              const rotatedX = (rotateWidth - rect.width) / 2
              const rotatedY = (rotateHeight - rect.height) / 2

              const newX = x - left
              const newY = y - top

              const offsetSize = 50
              // 限定拖拽范围，至少在矩形框内保留宽高${offsetSize}px
              if (newX < offsetSize - watermarkWidth) {
                _this.draggables[index].position.x =
                  left + offsetSize - watermarkWidth
              } else if (newX > width - offsetSize) {
                _this.draggables[index].position.x = left + width - offsetSize
              } else {
                _this.draggables[index].position.x = x + rotatedX
              }

              if (newY < offsetSize - watermarkHeight) {
                _this.draggables[index].position.y =
                  top + offsetSize - watermarkHeight
              } else if (newY > height - offsetSize) {
                _this.draggables[index].position.y = top + height - offsetSize
              } else {
                _this.draggables[index].position.y = y + rotatedY
              }

              // console.log(radians, rotatedX, rotatedY, x, y)
            },
            // onMove(event: any, info: any) {
            //   const { x, y } = event
            //   const newX = x - left
            //   const newY = y - top
            //   const offsetSize = 50
            //   // 限定拖拽范围，至少在矩形框内保留宽高${offsetSize}px
            //   if (newX < offsetSize - watermarkWidth) {
            //     _this.draggables[index].position.x =
            //       left + offsetSize - watermarkWidth
            //   } else if (newX > width - offsetSize) {
            //     _this.draggables[index].position.x = left + width - offsetSize
            //   } else {
            //   }
            //   if (newY < offsetSize - watermarkHeight) {
            //     _this.draggables[index].position.y =
            //       top + offsetSize - watermarkHeight
            //   } else if (newY > height - offsetSize) {
            //     _this.draggables[index].position.y = top + height - offsetSize
            //   } else {
            //   }

            //   console.log(
            //     'onMove',
            //     event.x, // 距离屏幕
            //     event.y, // 距离屏幕
            //     info.clientX, // 距离鼠标
            //     info.clientY, // 距离鼠标
            //     newX,
            //     newY,
            //     _this.draggables[index].position.x, // 距离屏幕
            //     _this.draggables[index].position.y, // 距离屏幕
            //     JSON.stringify(ref.getBoundingClientRect())
            //   )
            // },
            onEnd() {
              _this.updateCurrentPosition()
              watermark.canvas.style.watermarkPositionType = '自定义'
            },
            initialValue,
          })
        })

        // console.log(
        //   '444',
        //   list,
        //   watermarkRefs: watermarkRefs,
        //   draggables: draggables
        // )
        resolve('success')
      })
    },
    updateCurrentPosition() {
      if (this.currentIndex < 0) return

      const index = this.currentIndex
      const draggable: any = this.draggables[index]
      const actualLeft: any = draggable.x - this.canvasImgRect.left
      const actualTop: any = draggable.y - this.canvasImgRect.top

      // 更新到pinia，可以直接修改，或通过 *Store.set* 进行修改
      if (this.isGlobal) {
        if (!this.globalWatermark.customWatermark[index]) return
        this.globalWatermark.customWatermark[index].canvas.style.left =
          parseInt(actualLeft)
        this.globalWatermark.customWatermark[index].canvas.style.top =
          parseInt(actualTop)
      } else {
        if (!this.currentWatermark.customWatermark[index]) return
        this.currentWatermark.customWatermark[index].canvas.style.left =
          parseInt(actualLeft)
        this.currentWatermark.customWatermark[index].canvas.style.top =
          parseInt(actualTop)
      }

      // console.log('updateCurrentPosition', actualLeft, actualTop)
    },
    pxToPercent(value: any, size: any) {
      const rate: any = value / size
      return rate?.toFixed(1) * 100 || 0
    },
    percentToPx(value: any, size: any) {
      const rate: any = value * size
      return rate?.toFixed(1) / 100 || 0
    },
    async getCanvasByJson(watermark: any) {
      // 创建 canvas 并设置宽高
      const canvas = document.createElement('canvas')
      const ctx: any = canvas.getContext('2d')

      // 缩放因子
      const scaleFactor = 4

      // 原始的宽高，不考虑缩放
      const originalWidth = watermark.canvas.style.width * scaleFactor
      const originalHeight = watermark.canvas.style.height * scaleFactor

      // 设置 canvas 的宽高为原始尺寸
      canvas.width = originalWidth
      canvas.height = originalHeight

      // 应用透明背景色
      ctx.fillStyle = watermark.canvas.style.backgroundColor || 'transparent'
      ctx.fillRect(0, 0, canvas.width, canvas.height)

      for (const item of watermark.list) {
        ctx.save() // 保存初始状态

        if (item.type === 'text') {
          // 设置文本样式
          ctx.font = `${item.style.fontWeight || 'normal'} ${
            item.style.fontStyle || 'normal'
          } ${(item.style.fontSize || 20) * scaleFactor}px ${
            item.style.fontFamily || 'Microsoft YaHei'
          }`
          ctx.fillStyle = item.style.color || '#000'

          // 处理文本旋转，但位置需要乘以缩放因子
          const textAngle = item.style.rotate || 0
          ctx.translate(
            item.style.left * scaleFactor,
            item.style.top * scaleFactor + item.style.fontSize * scaleFactor
          )
          ctx.rotate((textAngle * Math.PI) / 180)
          ctx.translate(
            -(item.style.left * scaleFactor),
            -(item.style.top * scaleFactor + item.style.fontSize * scaleFactor)
          )

          // 绘制文本时，位置也要乘以缩放因子
          ctx.fillText(
            item.text?.slice(0, 20) || '',
            item.style.left * scaleFactor,
            item.style.top * scaleFactor + item.style.fontSize * scaleFactor
          )
        } else {
          await new Promise((resolve, reject) => {
            var img = new Image()
            img.crossOrigin = 'Anonymous'
            img.onload = function () {
              ctx.drawImage(
                img,
                item.style.left * scaleFactor,
                item.style.top * scaleFactor,
                item.style.width * scaleFactor * (item.style.zoom || 1), // 局部设置需要保
                item.style.height * scaleFactor * (item.style.zoom || 1) // 局部设置需要保留
              )
              resolve('')
            }
            img.src = item.img
            img.onerror = reject // 添加错误处理
          })
        }

        ctx.restore()
      }

      // 处理整体缩放
      const zoom = watermark.canvas.style.zoom || 1
      const angle = watermark.canvas.style.rotate || 0
      const radians = (angle * Math.PI) / 180

      // 创建缩放后的新 canvas
      const scaledCanvas: any = document.createElement('canvas')
      const scaledCtx: any = scaledCanvas.getContext('2d')
      scaledCanvas.width = canvas.width * zoom
      scaledCanvas.height = canvas.height * zoom

      // 应用缩放
      scaledCtx.scale(zoom, zoom)
      scaledCtx.drawImage(canvas, 0, 0)

      // 如果需要旋转，创建一个旋转后的新 canvas
      if (angle !== 0) {
        const rotatedCanvas: any = document.createElement('canvas')
        const rotatedCtx: any = rotatedCanvas.getContext('2d')

        // 计算旋转后的 canvas 尺寸
        rotatedCanvas.width =
          Math.abs(scaledCanvas.width * Math.cos(radians)) +
          Math.abs(scaledCanvas.height * Math.sin(radians))
        rotatedCanvas.height =
          Math.abs(scaledCanvas.height * Math.cos(radians)) +
          Math.abs(scaledCanvas.width * Math.sin(radians))

        // 将 canvas 中心移到新的 canvas 中心，应用旋转
        rotatedCtx.translate(rotatedCanvas.width / 2, rotatedCanvas.height / 2)
        rotatedCtx.rotate(radians)

        // 绘制缩放后的 canvas，确保图片不被缩小
        rotatedCtx.translate(-scaledCanvas.width / 2, -scaledCanvas.height / 2)
        rotatedCtx.drawImage(
          scaledCanvas,
          0,
          0,
          scaledCanvas.width,
          scaledCanvas.height
        )

        // 额外添加
        rotatedCanvas.scaleFactor = scaleFactor
        rotatedCanvas.leftRotateOffset =
          (rotatedCanvas.width - scaledCanvas.width) / 2
        rotatedCanvas.topRotateOffset =
          (rotatedCanvas.height - scaledCanvas.height) / 2

        // 缩放位置1
        // console.log(
        //   'rotatedCanvas',
        //   rotatedCanvas.width,
        //   rotatedCanvas.height,
        //   rotatedCanvas.toDataURL()
        // )
        return rotatedCanvas
      } else {
        // console.log(
        //   'scaledCanvas',
        //   scaledCanvas.width,
        //   scaledCanvas.height,
        //   scaledCanvas.toDataURL()
        // )

        // 额外添加
        scaledCanvas.scaleFactor = scaleFactor
        scaledCanvas.leftRotateOffset = 0
        scaledCanvas.topRotateOffset = 0
        return scaledCanvas
      }
    },
    // async getCanvasByHtml(element: any, watermark: any, format = 'jpeg') {
    //   /**
    //    * 注意点
    //    * 1、输出为jpeg（有损）：体积最小
    //    * 2、输出为png（无损）：体积最大，可能有1px白边
    //    * 3、输出为webp（都支持，浏览器兼容低）：体积适中
    //    */
    //   // const rate = element.offsetWidth / element.offsetHeight
    //   const options = {
    //     scale: 4, // 增加渲染的清晰度
    //     useCORS: true,
    //     backgroundColor: 'transparent', // 表明使用图片背景色
    //     // width: rate > 1 ? element.offsetWidth - 1 : element.offsetWidth, // 解决1px白边
    //     // height: rate < 1 ? element.offsetHeight - 1 : element.offsetHeight, // 解决1px白边
    //     // x: 0,
    //     // y: 0,
    //   }
    //   const canvas: any = await html2canvas(element, options)
    //   // console.log(canvas.toDataURL())
    //   // downloadImageByCanvas(canvas)
    //   return canvas
    // },
    async updateWatermarkAll(imgsMap: any) {
      this.loading = true
      if (this.isGlobal) {
        for (const watermark of this.globalWatermark.customWatermark) {
          // 百分比 记录
          watermark.canvas.style.leftPercent = this.pxToPercent(
            watermark.canvas.style.left,
            this.demoImgWidth
          )
          watermark.canvas.style.topPercent = this.pxToPercent(
            watermark.canvas.style.top,
            this.demoImgHeight
          )

          watermark.watermarkCanvas = await this.getCanvasByJson(watermark)
        }

        // 更新全部水印
        imgsMap.forEach((key: any) => {
          // 转换成功后的，不支持修改格式
          if (key.record.status !== 'success') {
            let customWatermark: any = []
            this.globalWatermark.customWatermark.forEach((item: any) => {
              let newWatermark = JSON.parse(JSON.stringify(item))

              // 创建新的水印
              const newWatermarkCanvas: any = document.createElement('canvas')
              newWatermarkCanvas.width = item.watermarkCanvas.width
              newWatermarkCanvas.height = item.watermarkCanvas.height
              newWatermarkCanvas.scaleFactor = item.watermarkCanvas.scaleFactor
              newWatermarkCanvas.leftRotateOffset =
                item.watermarkCanvas.leftRotateOffset
              newWatermarkCanvas.topRotateOffset =
                item.watermarkCanvas.topRotateOffset
              const ctx: any = newWatermarkCanvas.getContext('2d')
              ctx.drawImage(item.watermarkCanvas, 0, 0)
              newWatermark.watermarkCanvas = newWatermarkCanvas

              customWatermark.push(newWatermark)
            })

            // 重置局部水印，以全局水印为准
            key.customWatermark = customWatermark
            key.record.loading = false
            key.record.status = 'wait'
          }
        })
      } else {
        for (const watermark of this.currentWatermark.customWatermark) {
          watermark.watermarkCanvas = await this.getCanvasByJson(watermark)
        }
      }
      this.loading = false
    },
    // addWatermarkAll(imgsMap: any) {
    //   // 更新全部水印
    //   imgsMap.forEach((key: any) => {
    //     // 转换成功后的，不支持修改格式
    //     if (key.record.status !== 'success') {
    //       // 重置局部水印，以全局水印为准
    //       key.customWatermark = JSON.parse(
    //         JSON.stringify(this.globalWatermark.customWatermark)
    //       )
    //       key.record.loading = false
    //       key.record.status = 'wait'
    //     }
    //   })
    // },
    // removeWatermarkAll(imgsMap: any, index: number) {
    //   imgsMap.forEach((key: any) => {
    //     // 转换成功后的，不支持修改格式
    //     if (key.record.status !== 'success') {
    //       key.customWatermark.splice(index, 1)
    //       key.record.loading = false
    //       key.record.status = 'wait'
    //     }
    //   })
    // },
    updateCategorySession() {
      sessionStorage.categoryWatermarkList = JSON.stringify(
        this.categoryWatermarkList
      )
    },
    watermarkIsChange() {},
    // addTextWatermark() {
    //   this.categoryWatermarkList[0].list.push(
    //     JSON.parse(JSON.stringify(this.getCustomTextWatermark()))
    //   )
    // },
    // addImageWatermark() {
    //   this.categoryWatermarkList[0].list.push(
    //     JSON.parse(JSON.stringify(this.getCustomImageWatermark()))
    //   )
    // },
    // removeCustomWatermark(index: number) {
    //   this.categoryWatermarkList[0].list.splice(index, 1)
    // },
    getListAll() {
      if (sessionStorage.categoryWatermarkList) {
        return (this.categoryWatermarkList = JSON.parse(
          sessionStorage.categoryWatermarkList
        ))
      }
      getListAll()
        .then((_: any) => {
          if (_.data.code === 0 && _.data.data.length > 0) {
            let categoryWatermarkList: any = [
              {
                id: 0,
                name: '自定义',
                list: [],
              },
            ]

            _.data.data.forEach((item: any) => {
              categoryWatermarkList.push({
                id: item.category.id,
                name: item.category.name,
                status: item.category.status,
                sticky: item.category.name.sticky,
                list: item.templates?.map((watermark: any) => {
                  let _canvas = JSON.parse(watermark.canvas)
                  // 设置默认值
                  _canvas.style = {
                    ..._canvas.style,
                    opacity: _canvas.style.opacity || 1,
                    rotate: _canvas.style.rotate || 0,
                    zoom: _canvas.style.zoom || 1,
                  }

                  return {
                    ...watermark,
                    canvas: _canvas,
                    list: JSON.parse(watermark.list),
                  }
                }),
              })
            })

            this.categoryWatermarkList = categoryWatermarkList
            this.updateCategorySession()
          } else {
            messageSimple.error('详情获取失败')
            console.log(_)
          }
        })
        .catch((e) => {
          messageSimple.error('详情获取异常：', e.message)
          console.log(e)
        })
    },
    /**
     * 编辑或新增
     */
    saveDetail(canvas: any, list: any, detail: any) {
      console.log('save')
      saveDetail({
        ...detail,
        canvas: JSON.stringify(canvas),
        list: JSON.stringify(list),
      }).then((_: any) => console.log(_))

      setTimeout(() => {
        messageSimple.success('保存成功')
        history.back()
      }, 1000)
    },
  },
})
