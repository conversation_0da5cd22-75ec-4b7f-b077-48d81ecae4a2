export {} // 这是一个空的导出语句，使文件成为一个模块

window.ipcRendererApi?.on('start-login-callback', (event, message) => {
    // 回调，将用户信息写入H5
    window.execLenovoLogin && window.execLenovoLogin(message)
    // 将用户信息写入配置
    let userStr: any = window.localStorage.user
    userStr = JSON.parse(userStr)
    const token = window.localStorage.token
    window.ipcRendererApi?.send('start-login', { 'type': 'CONFIG', "userType": userStr.type, "token": token })
})
