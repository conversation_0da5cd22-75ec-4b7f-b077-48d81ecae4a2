// directives/scrollEffect.js
export const scrollEffect = {
  mounted(el: any) {
    // 节流函数，在指定时间内只执行一次函数
    function throttle(func: any, delay: any) {
      let timer: any = null
      return function (...args: any) {
        if (!timer) {
          func(...args) // 使用箭头函数避免 this 绑定问题
          timer = setTimeout(() => {
            timer = null
          }, delay)
        }
      }
    }

    let scrollTimer: any = null
    const SCROLL_END_DELAY = 500 // 滚动结束的延迟时间，单位：毫秒

    const handleScrollStart = () => {
      // 滚动开始，添加类名
      el.classList.add('auto-scrollbar')
    }

    const handleScrollEnd = () => {
      // 设置新的定时器，当滚动停止一段时间后移除类名
      scrollTimer = setTimeout(() => {
        el.classList.remove('auto-scrollbar')
      }, SCROLL_END_DELAY)
    }

    // 节流处理滚动开始事件
    const throttledScrollStart = throttle(handleScrollStart, 100)

    const combinedScrollHandler = () => {
      // 清除之前的定时器
      if (scrollTimer) {
        clearTimeout(scrollTimer)
      }
      throttledScrollStart()
      handleScrollEnd()
    }

    // 监听滚动事件
    el.addEventListener('scroll', combinedScrollHandler)

    // 在组件卸载时移除事件监听器
    const unmount = () => {
      el.removeEventListener('scroll', combinedScrollHandler)
    }
    el.__unmountScrollEffect = unmount
  },
  unmounted(el: any) {
    if (el.__unmountScrollEffect) {
      el.__unmountScrollEffect()
      delete el.__unmountScrollEffect
    }
  },
}
