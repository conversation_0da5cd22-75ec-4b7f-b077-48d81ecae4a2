<template>
  <div ref="navbar" class="navbar flex" mode="horizontal">
    <!-- 不带返回箭头 -->
    <div
      v-if="isPictureIndex"
      class="left flex flex1"
      :class="{
        'is-mac': isElectron && isMac,
      }"
    >
      <img
        class="icon"
        :src="projectConfig[hostName].APPLOGOSIDE"
        draggable="false"
      />
      <p class="title">{{ projectConfig[hostName].APPNAME }}</p>
    </div>
    <!-- 带返回箭头 -->
    <div
      v-else
      class="left flex flex1 detail-back"
      :class="{
        'is-mac': isElectron && isMac,
      }"
    >
      <img
        class="back"
        src="/img/pic/second2/<EMAIL>"
        @click="goNext('index')"
      />
      <img class="line" src="/img/pic/second2/<EMAIL>" />
      <p class="title">{{ projectConfig[hostName].APPNAME }}</p>
    </div>
    <div class="right flex">
      <!-- <p>下载客户端</p> -->
      <div
        v-if="user.type !== 4"
        class="menu"
        style="width: 120px"
        @click="goNext('pay')"
      >
        <!-- 开通VIP -->
        <img
          v-if="!user.type || user.type === 1"
          class="vip"
          src="/img/mind_map/gif/<EMAIL>"
          draggable="false"
        />
        <!-- 续费VIP -->
        <img
          v-else
          class="vip"
          src="/img/mind_map/gif/<EMAIL>"
          draggable="false"
        />
        <img
          class="vip-pop"
          src="/img/mind_map/table/<EMAIL>"
          draggable="false"
        />
        <p class="vip-pop-tip">周年大促</p>
      </div>
      <UserDialog></UserDialog>
      <el-popover
        :popper-class="{
          customerPopover: true,
          winCustomerPopover: !isMac,
        }"
        effect="light"
        placement="bottom"
        trigger="hover"
        width="88px"
        :show-arrow="false"
      >
        <template #reference>
          <div
            class="menu-action more"
            :class="{
              'menu-action-mac': isMac,
            }"
          >
            <div draggable="false"></div>
          </div>
        </template>
        <template #default>
          <div class="btn" @click="customerHandler">联系客服</div>
          <div v-if="isElectron" class="btn about" @click="aboutDialog = true">
            关于
          </div>
        </template>
      </el-popover>

      <div v-if="isElectron && !isMac" class="menu-action min" @click="min">
        <div draggable="false"></div>
      </div>
      <div
        v-if="isElectron && !isMac"
        class="menu-action screen"
        :class="{
          'screen-narrow': clientFullScreen,
        }"
        @click="screen"
      >
        <div draggable="false"></div>
      </div>
      <div v-if="isElectron && !isMac" class="menu-action close" @click="close">
        <div draggable="false"></div>
      </div>
    </div>

    <el-dialog class="aboutDialog" width="360px" v-model="aboutDialog">
      <div class="modal-body">
        <img :src="APPLOGOABOUT" />
        <div class="desc">{{ APPNAME }}</div>
        <div class="version">版本：V{{ VERSION }}</div>
      </div>
      <div class="copyright">
        Copyright&nbsp;©2013-2030.All&nbsp;Rights&nbsp;Reserved
      </div>
    </el-dialog>
  </div>
</template>
<script lang="ts" setup>
import { ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { storeToRefs } from 'pinia'
import { usePayStore } from '@/pinia/pay'
import { useUserStore } from '@/pinia/user'
import { GetCustomer } from '@/api/user'
import { needLogin } from '@/utils'
import UserDialog from '@/components/Home/HeaderUserLoginDialog.vue'
import {
  projectConfig,
  APPLOGOABOUT,
  APPNAME,
  VERSION,
} from '@/utils/configData/config'
import bridge from '@/utils/bridge'
let hostName = window.hostName
const { proxy } = getCurrentInstance()
const payStore = usePayStore()
const userStore = useUserStore()
let { user } = storeToRefs(userStore)
const route = useRoute()
// user.type = 4
const isElectron = window.isElectron
const isMac = window.isMac
const isOldVersion =
  window.isElectron && bridge.compareVersions(VERSION, '*******') <= 0
const isPictureIndex = route.name === 'index' || isOldVersion
let aboutDialog = ref(false)

const min = () => {
  window.ipcRendererApi?.send('min')
}
let clientFullScreen = ref(false)
const screen = () => {
  if (clientFullScreen.value) {
    clientFullScreen.value = false
    window.ipcRendererApi?.send('exit-screen')
  } else {
    clientFullScreen.value = true
    window.ipcRendererApi?.send('full-screen')
  }
}
const close = () => {
  proxy?.$messageBox
    .confirm('正在处理中任务将取消', '确定关闭软件？', {
      distinguishCancelAndClose: true,
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      customClass: 'customStyleByMessageBox',
      type: 'warning',
      // center: true
    })
    .then((e) => {
      // console.log('then', e)
      if (e === 'confirm') {
        window.ipcRendererApi?.send('close-window')
      }
    })
    .catch((e) => {
      console.log('catch', e)
      // if (e === 'cancel') {}
    })
}

const router = useRouter()
const goNext = (type: string) => {
  if (type === 'pay') {
    if (!needLogin(userStore)) {
      payStore.setShowPayDialog(true)
    }
    return
  }
  if (type === 'template') {
    return window.open('/template')
  }
  router.push({
    name: type,
  })
}

let customerUrl = ''
const customerHandler = () => {
  if (customerUrl) return bridge.openSystem(customerUrl)

  GetCustomer().then((_) => {
    if (_.data.code === 0 && _.data.data) {
      customerUrl = _.data.data.qqUrl
      bridge.openSystem(customerUrl)
    }
  })
}
</script>
<style lang="scss">
.customerPopover {
  margin-left: 0 !important;
  min-width: auto !important;
  text-align: center !important;
  z-index: 4000 !important;
  cursor: pointer;

  div {
    user-select: none;
  }

  .about {
    margin-top: 7.5px;
    padding-top: 7.5px;
  }
}
.winCustomerPopover {
  margin-left: -35px !important;
}
.aboutDialog {
  height: 386px;
  margin: calc((100vh - 386px) / 2) auto !important; // dialog custom 上下居中
  padding: 0;
  text-align: center;

  .el-dialog__header {
    padding: 0 !important; // dialog custom

    .el-dialog__headerbtn {
      top: -5px;
      right: -5px;
    }

    .el-dialog__close {
      width: 20px;
      height: 20px;
      svg {
        width: 20px;
        height: 20px;
      }
    }
  }

  .el-dialog__body {
    // dialog custom 内边距
    padding: 32px 24px 20px !important;
  }

  img {
    width: 68px;
    height: 68px;
    margin: 30px 0 20px;
  }

  .desc {
    font-weight: 600;
    font-size: 24px;
    color: #000000;
    line-height: 33px;
    margin-bottom: 32px;
  }

  .version {
    font-size: 14px;
    color: #333333;
    line-height: 20px;
    text-shadow: 0px 0px 10px rgba(0, 0, 0, 0.2);
  }

  .copyright {
    margin-top: 110px;
    font-size: 15px;
    color: #999999;
    line-height: 21px;
  }
}
</style>
<style lang="scss" scoped>
.navbar {
  height: 48px;
  -webkit-app-region: drag;
  border-bottom: 1px solid #e3e6f2;
  background-color: #f1f5fe;
  z-index: 111;

  .left {
    position: relative;
    .icon {
      position: relative;
      top: 11px;
      width: 26px;
      height: 26px;
      margin-left: 19px;
    }
    .title {
      padding-top: 13px;
      line-height: 22px;
      margin-left: 12px;
      font-weight: 600; // electron
      font-size: 16px;
      color: #333333;
    }
  }
  .detail-back {
    .back {
      position: relative;
      top: 9px;
      width: 30px;
      height: 30px;
      padding: 5px;
      margin-left: 9px;
      -webkit-app-region: no-drag;
      cursor: pointer;
    }
    .line {
      position: relative;
      top: 14px;
      margin: 0 12px 0 7px;
      width: 1px;
      height: 22px;
    }
    .title {
      position: relative;
      top: 13px;
      padding-top: 0;
      margin-left: 0;
    }
  }
  .is-mac {
    margin-left: 86px;
  }

  .right {
    padding: 8px 0;
    user-select: none;
    // width: 190px;
    .menu {
      position: relative;
      -webkit-app-region: no-drag;
      cursor: pointer;

      img {
        margin: 0 8px;
        width: 32px;
        height: 48px;
      }

      .vip {
        position: relative;
        top: 4px;
        width: 85px;
        height: 25px;

        &:hover {
          opacity: 0.7;
        }
      }

      .vip-pop {
        position: absolute;
        width: 60px;
        height: 17px;
        left: 55px;
        top: -5px;
        z-index: 1;
      }

      .vip-pop-tip {
        position: absolute;
        left: 64px;
        top: -2px;
        width: 50px;
        line-height: 12px;
        margin-left: 5px;
        font-size: 12px;
        // font-weight: 600; // electron
        color: #ffffff;
        z-index: 2;
      }
    }

    .menu-action {
      width: 28px;
      line-height: 32px;
      font-size: 14px;
      color: #333333;
      -webkit-app-region: no-drag;
      cursor: pointer;
    }

    .menu-action-mac {
      width: 34px;
    }

    .more {
      div {
        position: relative;
        width: 20px;
        height: 20px;
        top: 5.5px;
        background: url('/img/pic/public/<EMAIL>');
        background-size: contain;

        &:hover {
          background: url('/img/pic/public/<EMAIL>');
          background-size: contain;
        }
      }
    }

    .min {
      div {
        position: relative;
        width: 20px;
        height: 20px;
        top: 5.5px;
        background: url('/img/pic/public/<EMAIL>');
        background-size: contain;

        &:hover {
          background: url('/img/pic/public/<EMAIL>');
          background-size: contain;
        }
      }
    }

    .screen {
      div {
        position: relative;
        width: 20px;
        height: 20px;
        top: 5.5px;
        background: url('/img/pic/public/<EMAIL>');
        background-size: contain;

        &:hover {
          background: url('/img/pic/public/<EMAIL>');
          background-size: contain;
        }
      }
    }

    .screen-narrow {
      div {
        position: relative;
        width: 20px;
        height: 20px;
        top: 5.5px;
        background: url('/img/pic/public/<EMAIL>');
        background-size: contain;

        &:hover {
          background: url('/img/pic/public/<EMAIL>');
          background-size: contain;
        }
      }
    }

    .close {
      div {
        position: relative;
        width: 20px;
        height: 20px;
        top: 5.5px;
        background: url('/img/pic/public/<EMAIL>');
        background-size: contain;

        &:hover {
          background: url('/img/pic/public/<EMAIL>');
          background-size: contain;
        }
      }
    }
  }
}
</style>
