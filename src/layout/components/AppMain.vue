<template>
  <section class="app-main" :style="{
    height: isFull ? '100vh' : 'calc(100vh - 48px);'
  }">
    <router-view v-slot="{ Component, route }">
      <component
        :is="Component"
        v-if="!route.meta.keepAlive"
        :key="route.path || route.name"
      />
    </router-view>
  </section>
</template>

<script lang="ts" setup>
import { useRoute } from 'vue-router'
// pinia demo
// import { useCommonStore } from '@/pinia/common'
// import { storeToRefs } from 'pinia'

// const commonStore: any = useCommonStore()
// const { showRouter } = storeToRefs(commonStore)
// console.log(showRouter.value)

const route = useRoute()
const isFull = window.isIframe || route.name === 'index'
</script>

<style scoped lang="scss">
.app-main {
  position: relative;
  height: calc(100vh - 48px);
  // padding: 20px 30px 0px 0;
  overflow: hidden;
  background-color: white;
}
</style>
