<template>
  <div class="app-wrapper">
    <!--顶部导航-->
    <Navbar v-if="!isIframe" />
    <!--主体内容-->
    <div class="main-container">
      <!--主页面-->
      <AppMain />
    </div>
  </div>
</template>

<script lang="ts" setup>
import { onBeforeMount, onMounted, onBeforeUnmount } from 'vue'
import Navbar from './components/Navbar.vue'
import AppMain from './components/AppMain.vue'

const isIframe = window.isIframe

onBeforeMount(() => {})

onMounted(() => {})

onBeforeUnmount(() => {})
</script>

<style lang="scss" scoped>
.app-wrapper {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;

  .navbar {
    position: absolute;
    width: 100%;
    z-index: 1;
    border-bottom-color: transparent;
    background-color: transparent !important;
  }

  .main-container {
    position: relative;

    .app-main {
      position: relative;
      background-color: #f0f2f5;
      background: url(/img/pic/second2/<EMAIL>);
      background-size: cover;
      overflow-y: auto;
    }
  }
}
</style>
