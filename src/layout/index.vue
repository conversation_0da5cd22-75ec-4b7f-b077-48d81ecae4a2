<template>
  <div class="app-wrapper">
    <div class="drawer-bg" @click="handleClickOutside" />
    <!--顶部导航-->
    <Navbar v-if="!isIframe" />
    <!--侧边栏-->
    <Sidebar />
    <!--主体内容-->
    <div class="main-container">
      <!--主页面-->
      <AppMain />
    </div>
  </div>
</template>

<script lang="ts" setup>
import { onBeforeMount, onMounted, onBeforeUnmount } from "vue";
import Sidebar from "./components/Sidebar/index.vue";
import Navbar from "./components/Navbar.vue";
import AppMain from "./components/AppMain.vue";

const isIframe = window.isIframe

onBeforeMount(() => {
  // window.addEventListener('resize', resizeHandler)
});

onMounted(() => {
  // const isMob = isMobile()
  // if (isMob) {
  //   store.dispatch('app/toggleDevice', 'mobile')
  //   store.dispatch('app/closeSideBar', true)
  // }
});

onBeforeUnmount(() => {
  // window.removeEventListener('resize', resizeHandler)
});

const handleClickOutside = () => {
  // store.dispatch('app/closeSideBar', false)
};
</script>

<style lang="scss" scoped>
.app-wrapper {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;

  .main-container {
    position: relative;
    height: 100%;
    padding-left: 157px;
    transition: margin-left 0.28s;
    overflow: hidden;
  }
}
</style>
