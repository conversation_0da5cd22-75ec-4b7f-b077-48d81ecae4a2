import { createApp } from 'vue'
import { createPinia } from 'pinia'

// element-plus
// import ElementPlus from 'element-plus'
// import zhCn from 'element-plus/es/locale/lang/zh-cn'
import * as ElIconModules from '@element-plus/icons-vue'
import { VueMasonryPlugin } from 'vue-masonry'
import { ElMessage, ElMessageBox } from 'element-plus'
import { isMac } from '@/utils'
import filter from '@/utils/filters'
import '@/renderer/login'
import '@/renderer/tool'
import haluoUtil from '@haluo/util'
// import { projectConfig } from '@/utils/configData/config'
import { loadJs, loadCss, useDebugTool } from '@/utils/index'
import { messageSimple } from '@/utils/message'
import 'element-plus/dist/index.css'
import '@/styles/index.scss'

import App from './App.vue'
import router from './router'
// import bridge from '@/utils/bridge'
import oss from '@/utils/aliOss'
import { scrollEffect } from './directives/scrollEffect'

// TODO 临时清除serviceWorker
navigator?.serviceWorker?.getRegistrations().then(function(registrations) {
  for(let registration of registrations) {
      registration.unregister();
  }
});

// Register service worker manually
// if ('serviceWorker' in navigator) {
//   window.addEventListener('load', () => {
//     navigator.serviceWorker
//       .register('/sw.js', {
//         // Service Worker 的作用域不能超出其文件所在的目录
//         // 指定作用域，不指定则默认为 sw.js 文件所在的路径
//         // 包含/picture/matting这个页面下所有请求
//         scope: '/picture/matting',
//       })
//       .then((registration) => {
//         console.log(
//           'ServiceWorker1 registration successful with scope: ',
//           registration.scope
//         )
//       })
//       .catch((error) => {
//         console.warn('ServiceWorker1 registration failed: ', error)
//       })

//     navigator.serviceWorker
//       .register('/sw.js', {
//         // Service Worker 的作用域不能超出其文件所在的目录
//         // 指定作用域，不指定则默认为 sw.js 文件所在的路径
//         // 包含/js/matting/这个路径下所有请求，但是fetch形式的不支持（需要配对应页面的路径）
//         scope: '/js/matting/',
//       })
//       .then((registration) => {
//         console.log(
//           'ServiceWorker2 registration successful with scope: ',
//           registration.scope
//         )
//       })
//       .catch((error) => {
//         console.warn('ServiceWorker2 registration failed: ', error)
//       })
//   })
// }

const app = createApp(App)

// 统一注册Icon图标
for (const iconName in ElIconModules) {
  if (Reflect.has(ElIconModules, iconName)) {
    const item = ElIconModules[iconName]
    app.component(iconName, item)
  }
}

// 注册全局指令
app.directive('scroll-effect', scrollEffect)
useDebugTool()
app.use(oss)
app.use(VueMasonryPlugin)
app.use(createPinia())
app.use(router)

app.config.globalProperties.$filter = { ...filter, ...haluoUtil }
app.config.globalProperties.$messageSimple = messageSimple
app.config.globalProperties.$message = ElMessage
app.config.globalProperties.$messageBox = ElMessageBox

window.isElectron = !!window.ipcRendererApi
window.isIframe = window.top !== window.self
window.isMac = isMac()
window.isWindow = !window.isMac
window.loadJs = loadJs
window.loadCss = loadCss
window.isWN = location.hostname === 'wnpic.miaoji66.com'
window.isMJ = location.hostname === 'pic.miaoji66.com'
window.isLenovo =
  window.isWindow &&
  window.isWN &&
  window.navigator.userAgent.includes('lianxiang')

// 定时更新联想登录信息
const updateUser = () => {
  if (window.isLenovo) {
    console.log('执行每5小时任务！')
    if (localStorage.token) {
      window.ipcRendererApi?.send('start-login', { type: 'login' })
    }
  }
}
updateUser()
setInterval(updateUser, 5 * 60 * 60 * 1000)

// 强更
const box = () => {
  app.mount('#app')

  // bridge.getVersion()
  // ElMessageBox.alert(` `, '当前版本过旧，请更新最新版本', {
  //   confirmButtonText: '更新',
  //   showClose: false,
  //   customClass: 'customStyleByMessageBox',
  //   type: 'warning',
  // }).then(() => {
  //   console.log('更新')
  //   bridge.openSystem('https://lestore.lenovo.com/detail/L104285') // 兼容联想老包
  //   // bridge.openSystem(projectConfig[window.hostName].DOWNLOADURL)
  //   box()
  // })
}
box()
