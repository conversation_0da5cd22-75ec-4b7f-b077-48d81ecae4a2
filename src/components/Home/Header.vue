<template>
  <div
    class="header fixed"
    :class="{
      homeBG: isHome,
      templateBG: route.name === 'template',
    }"
  >
    <div class="wrap flex">
      <div class="left flex flex1">
        <img
          class="icon"
          :src="
            isHome
              ? projectConfig[hostName].APPLOGOHOME
              : projectConfig[hostName].APPLOGOTEMPLATE
          "
        />
        <p class="title">{{ projectConfig[hostName].APPNAME }}</p>
      </div>
      <div class="right flex">
        <!-- 支付 -->
        <div
          v-if="userStore.user.type !== 4"
          class="pay"
          style="width: 120px"
          @click="goNext('pay')"
        >
          <img
            v-if="userStore.user.type === 1"
            class="vip"
            src="/img/mind_map/gif/<EMAIL>"
          />
          <img v-else class="vip" src="/img/mind_map/gif/<EMAIL>" />
          <img
            class="vip-pop"
            src="/img/mind_map/table/<EMAIL>"
          />
          <p class="vip-pop-tip">周年大促</p>
        </div>
        <!-- 下载 -->
        <template v-if="!VERSION">
          <div v-if="isMiaoJi" class="download item flex">
            <p class="download-btn">下载客户端</p>
            <div class="download-list">
              <div
                class="download-item flex"
                @click="openNext('', 'win')"
              >
                <img
                  class="windows"
                  src="/img/mind_map/web/window.png"
                  alt=""
                />
                <p>Windows</p>
              </div>
              <img class="line" src="/img/mind_map/web/line.png" alt="" />
              <div class="download-item flex" @click="downloadDialog = true">
                <img class="mac" src="/img/mind_map/web/mac.png" alt="" />
                <p>Mac</p>
              </div>
            </div>
          </div>
          <div
            v-else
            class="download item flex"
            @click="openNext(projectConfig[hostName].DOWNLOADURL, 'win')"
          >
            <p class="download-btn">下载客户端</p>
          </div>
        </template>
        <!-- 登录 -->
        <UserDialog ref="loginRef"></UserDialog>
      </div>
    </div>

    <el-dialog
      class="downloadDialog"
      width="438px"
      height="316px"
      v-model="downloadDialog"
    >
      <p class="title">选择版本</p>
      <div class="flex">
        <div
          class="btn"
          :class="{
            choose: !chooseM,
          }"
          @click="chooseM = false"
        >
          <p>Intel芯片版本</p>
          <img
            class="choose-icon"
            src="/img/mind_map/pop-up/<EMAIL>"
            alt=""
          />
        </div>
        <div
          class="btn"
          :class="{
            choose: chooseM,
          }"
          @click="chooseM = true"
        >
          <p>Apple芯片版本</p>
          <img
            class="choose-icon"
            src="/img/mind_map/pop-up/<EMAIL>"
            alt=""
          />
        </div>
      </div>
      <div class="download btn" @click="downloadHandler"><p>下载</p></div>
      <div class="desc">
        <p>如何查看适合版本？</p>
        <p>1、一般现在新买电脑都是“Apple芯片版本”</p>
        <p>2、具体查看：「关于本机 」> 「系统报告」>「处理器名称」或「芯片」</p>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRoute } from 'vue-router'
import bridge from '@/utils/bridge'
import { DOMAIN, projectConfig, APPID, VERSION } from "@/utils/configData/config"
// import { needLogin } from '@/utils'
import { usePayStore } from '@/pinia/pay'
import { useUserStore } from '@/pinia/user'
import UserDialog from '@/components/Home/HeaderUserLoginDialog.vue'
defineProps<{
  type: string
}>()

const route = useRoute()
const payStore = usePayStore()
const userStore = useUserStore()
const loginRef = ref()
const isHome = route.name === 'home' || route.name === undefined
// const isMiaoJi = location.host === 'pic.miaoji66.com' // 等mac好了再开启
const isMiaoJi = false

// const openNext = (url: string) => {
//   if (!url) return
//   bridge.openSystem(url)
// }
// 打开下载链接，os：mac、mac_m、win，默认win
const openNext = (url: string, os?: string) => {
  // 兼容老的，后期去除
  if (url) {
    return bridge.openSystem(url)
  }

  const ch = route.query.channel || 10024 // ch：项目渠道，默认10024为官方渠道
  const type: any = route.query.type || '' // package_type 1大包、2小包
  let packageType: any = os === 'win' ? 2 : 1 // package_type 1大包、2小包
  if ([1, 2, '1', '2'].includes(type)) {
    packageType = type
  }
  bridge.openSystem(`${DOMAIN}/openapi/download?app=${APPID}${ch ? `&ch=${ch}` : ''}&package_type=${packageType}&os=${os}`)
}


let hostName = window.hostName

const goNext = (type: string) => {
  if (!localStorage.token) {
    loginRef.value.showLogin()
    return
  }
  if (type === 'pay') {
    return payStore.setShowPayDialog(true)
  }
}

// mac系列下载弹框
let chooseM = ref(true)
let downloadDialog = ref(false)
const downloadHandler = () => {
  if (chooseM.value) {
    openNext('', 'mac_m') // m系列
  } else {
  openNext('', 'mac') // intel系列
}
  downloadDialog.value = false
}
</script>

<style lang="scss">
// whdialog
.downloadDialog {
  margin: calc((100vh - 316px) / 2) auto !important; // dialog custom 上下居中
  padding: 0;

  .el-dialog__header {
    padding: 0 !important; // dialog custom
  }

  .el-dialog__body {
    // dialog custom 内边距
    padding: 32px 24px 20px !important;
  }

  .title {
    line-height: 24px;
    font-size: 16px;
    font-weight: 600; // electron
    color: rgba(0, 0, 0, 0.85);
    text-align: center;
  }

  .flex {
    margin-top: 25px;
    text-align: center;
    .btn {
      position: relative;
      width: 187px;
      height: 61px;
      font-size: 14px;
      border-radius: 4px;
      color: rgba(0, 0, 0, 0.65);
      border: 1px solid rgba(151, 151, 151, 0.3);
      cursor: pointer;

      &:first-child {
        margin-right: 16px;
      }

      .choose-icon {
        display: none;
        position: absolute;
        right: -1px;
        bottom: -1px;
        width: 24px;
        height: 21px;
      }
    }
    .choose {
      background: rgba(24, 144, 255, 0.1);
      border: 2px solid #1890ff;

      .choose-icon {
        display: block;
      }
    }
  }

  .download {
    width: 120px;
    height: 32px;
    margin: 32px auto 0;
    font-size: 14px;
    color: #ffffff;
    background: #1890ff;
    border-radius: 2px;
    cursor: pointer;

    &:hover {
      opacity: 0.7;
    }

    p {
      width: 100%;
      text-align: center;
      line-height: 32px;
    }
  }

  .desc {
    margin-top: 24px;
    font-size: 12px;
    color: #999999;
    line-height: 22px;
  }
}
.header {
  .loginButton {
    top: 15px !important;
    width: 80px !important;
    height: 30px !important;
    border-radius: 2px !important;
    border: 1px solid #fff !important;
    color: #fff !important;
    &:hover {
      // border: 1px solid #1890ff !important;
      // color: #1890ff !important;
      opacity: 0.9;
    }
  }

  .el-tooltip__trigger {
    position: relative;
    height: 32px;
    top: 15px;
    .username {
      color: #fff;
    }
  }
}
</style>
<style lang="scss" scoped>
.header {
  width: 100%;
  min-width: 1366px;
  // height: 60px;
  line-height: 60px;
  top: 0;
  background: #fff;
  z-index: 1;

  .wrap {
    margin: 0 auto;
    max-width: 1366px;
    min-width: 600px;
    // min-width: 1200px;

    .left {
      position: relative;
      left: 60px;

      .icon {
        position: relative;
        height: 25px;
        top: 17.5px;
      }

      .title {
        margin-left: 12px;
        font-weight: 600; // electron
        font-size: 16px;
        color: #ffffff;
        line-height: 60px;
      }
    }

    .right {
      position: relative;
      right: 60px;
      line-height: normal;

      .pay {
        position: relative;
        margin-top: 11.5px;
        cursor: pointer;

        img {
          margin: 0 8px;
          width: 32px;
          height: 48px;
        }

        .vip {
          position: relative;
          top: 4px;
          width: 85px;
          height: 25px;

          &:hover {
            opacity: 0.7;
          }
        }

        .vip-pop {
          position: absolute;
          width: 60px;
          height: 17px;
          left: 55px;
          top: -5px;
          z-index: 1;
        }

        .vip-pop-tip {
          position: absolute;
          left: 64px;
          top: -2px;
          width: 50px;
          line-height: 12px;
          margin-left: 5px;
          font-size: 12px;
          color: #ffffff;
          z-index: 2;
        }
      }

      .download {
        font-size: 14px;
        font-weight: 400;
        margin: 0 40px;
        color: #fff;
        cursor: pointer;
        z-index: 1;

        .download-btn {
          line-height: 60px;
          &:hover {
            // color: #1890ff;
            opacity: 0.9;
          }
        }

        .download-list {
          display: none;
          position: absolute;
          margin-left: -20px;
          width: 120px;
          height: 105px;
          top: 60px;
          padding: 12px 5px;
          border-radius: 6px;
          border: 1px solid #eaeaea;
          box-sizing: border-box;
          background: #ffffff;

          .download-item {
            width: 108px;
            height: 32px;
            padding: 6px 10px;
            border-radius: 5px;
            overflow: hidden;

            &:hover {
              background: #f4f4f4;
            }

            .windows {
              width: 20px;
              height: 20px;
            }

            .mac {
              width: 20px;
              height: 20px;
            }

            p {
              position: relative;
              left: 11px;
              font-size: 14px;
              color: #333333;
              line-height: 20px;
              user-select: none;
            }
          }

          .line {
            width: 90px;
            height: 1px;
            margin: 0px 10px 8px;
          }
        }

        &:hover {
          .download-list {
            display: block;
          }
        }
      }
    }
  }
}

.homeBG {
  background-color: transparent;
}

.templateBG {
  background-color: #f6f7f8;
}


@media screen and (max-height: 880px) {
  .homeBG {
    background: rgba(0, 0, 0, 0.65);
  }
}
</style>
