<template>
  <!-- 会员弹框 -->
  <el-popover
    v-if="user.nickname"
    :popper-class="{
      vipPopover: true,
      winVipPopover: !isMac
    }"
    :width="316"
    :teleported="false"
    :visible-arrow="false"
    placement="bottom"
    trigger="hover"
  >
    <template #reference>
      <div class="flex" :class="{
        'hide': props.hide
      }">
        <div class="menu">
          <img
            class="avatar"
            :src="'/img/mind_map/table/<EMAIL>'"
            draggable="false"
          />
        </div>
        <p class="username dotdotdot1">{{ user.nickname }}</p>
      </div>
    </template>
    <div class="userInfo flex">
      <img
        class="avatar"
        :src="'/img/mind_map/table/<EMAIL>'"
      />
      <div>
        <div class="box flex">
          <p class="name dotdotdot1">{{ user.nickname }}</p>
          <p
            class="status member"
            :style="{
              paddingLeft: user.type !== 1 ? '16px' : '4px'
            }"
          >
            <img
              v-if="user.type !== 1"
              src="/img/mind_map/icon/<EMAIL>"
              alt=""
              :style="{
                filter: isExpire ? 'grayscale(1)' : ''
              }"
            />
            {{
              user.type === 1
                ? '未开通VIP'
                : isExpire
                ? 'VIP 已过期'
                : '已开通 VIP'
            }}
          </p>
        </div>
        <div v-if="user.id" class="flex">
          <p class="id">ID: {{ user.id }}</p>
        </div>
      </div>
    </div>
    <div class="flex">
      <p class="equity flex1">
        VIP权益
        <span v-if="![1].includes(user.type)" class="tip">(有效期至：{{ user.type === 4 ? '终身' : user.vip_expire && user.vip_expire.split("T")[0] }})
        </span>
      </p>
      <el-button v-if="user.type !== 4" class="open" @click="goNext('pay')">{{
        !user.type || user.type === 1 ? '开通VIP' : '续费VIP'
      }}</el-button>
    </div>
    <div class="equityInfo">
      <div class="flex">
        <p class="flex1">· 支持百种格式</p>
        <p class="flex1">· 高清无损压缩</p>
      </div>
      <div class="flex">
        <p class="flex1">· AI创意玩法</p>
        <p class="flex1">· 支持高分辨率</p>
      </div>
      <div class="flex">
        <p class="flex1">· 批量智能处理</p>
        <p class="flex1">· 极速出图</p>
      </div>
      <div class="flex">
        <p class="flex1">· 操作简单、便捷</p>
        <p class="flex1">· 海量模板随心选</p>
      </div>
      <div class="flex">
        <p class="flex1">· 持续更新</p>
        <p class="flex1">· 专属客服</p>
      </div>
    </div>
    <el-button class="exit" @click="exit">退出</el-button>
  </el-popover>
  <el-button v-else class="loginButton" :class="{
    'hide': props.hide
  }" type="primary" link @click="showLogin">登录</el-button>
  <!-- 促销弹框 -->
  <div v-if="promotionVisible" class="promotion">
    <div class="box">
      <img
        class="icon pay"
        src="/img/mind_map/table/<EMAIL>"
        @click="goNext('pay')"
      />
      <img
        src="/img/mind_map/btn/<EMAIL>"
        alt=""
        class="close"
        @click="promotionVisible = false"
      />
      <!-- <p class="pay-tip" @click="goNext('pay')">立即查看</p> -->
    </div>
  </div>
  <!-- 登录弹框 -->
  <div v-if="loginVisible" class="login">
    <div v-if="qrCodeUrl" class="box">
      <img class="icon" src="/img/mind_map/table/<EMAIL>" />
      <img
        :src="qrCodeUrl"
        class="qrcode"
      />
      <div v-if="isQRCodeExpire" class="expireBox" @click="showLogin">
        <div class="qrcode expire"></div>
        <img class="refresh" src="/img/mind_map/btn/<EMAIL>" alt="">
        <p>二维码失效，点击刷新</p>
      </div>
      <div>
        <img
          src="/img/pic/public/<EMAIL>"
          alt=""
          class="close btn"
          @click="closeDialog('loginVisible')"
        />
      </div>
      <p v-if="isQRCodeExpire" class="tip" style="color: #fff;">扫码无反应？点击 刷新</p>
      <p v-else class="tip">扫码无反应？点击 <span @click="showLogin">刷新</span></p>
    </div>
  </div>
  <!-- 遮罩 -->
  <div
    v-if="promotionVisible || loginVisible"
    class="mask"
    @click="
      () => {
        // promotionVisible = false
        // closeDialog('loginVisible')
      }
    "
  />
</template>

<script setup lang="ts">
import { ref, onUnmounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import dayjs from 'dayjs'
import bridge from '@/utils/bridge'
// import { CHANNEL } from '@/utils/configData/config'
import {
  GetWXQRCode,
  GetWXQRLoginResult,
  GetLenovoLoginResult,
  GetUserInfoAndToken
} from '@/api/user'
import { storeToRefs } from 'pinia'
import { usePayStore } from '@/pinia/pay'
import { useUserStore } from '@/pinia/user'

const props = withDefaults(
  defineProps<{
    hide: boolean
  }>(),
  {
    hide: false,
  }
)

const payStore = usePayStore()
const userStore = useUserStore()
const route = useRoute()
const router = useRouter()
const { proxy } = getCurrentInstance()
const isMac = window.isMac
let timer: any = null
let { user } = storeToRefs(userStore)
// user.value.type = 4
// user.value.vip_expire = '2200-10-13T23:23:45+08:00'
const isExpire = ref(new Date() > new Date(user.value.vip_expire))
const promotionVisible = ref(false)
const loginVisible = ref(false)
let qrCodeUrl = ref('')
let token = ref('')
let isQRCodeExpire = ref(false)
let qrcodeExpire = 0

// 弹框促销，每天只弹一次
const today = dayjs(new Date()).format('YYYY-MM-DD')
const storeDay = localStorage.promotionVisibleDay
promotionVisible.value =
  today !== storeDay && (!userStore.token || user.value.type === 1)
localStorage.promotionVisibleDay = today

const isLenovo = window.isLenovo

// electron客户端调用此方法进行登录，并获取登录结果
window.execLenovoLogin = (token: string) => {
  localStorage.lenovoToken = token
  getLoginResult(token)
}

// 1
const showLogin = () => {
  isLenovo
    ? bridge.login()
    : GetWXQRCode()
        .then(_ => {
          qrcodeExpire = 300
          isQRCodeExpire.value = false

          loginVisible.value = true
          const data = _.data
          const { scene_id, url } = data
          qrCodeUrl.value = url
          console.log(data, scene_id, url)

          clearInterval(timer)
          timer = setInterval(() => {
            qrcodeExpire -= 2
            if (qrcodeExpire <= 0) {
              isQRCodeExpire.value = true
              // console.log(qrcodeExpire, isQRCodeExpire.value)
              return clearInterval(timer)
            }
            getLoginResult(scene_id)
          }, 2000)
        })
        .catch(err => {
          console.log(err)
        })
}
// 2
const getLoginResult = (scene_id_or_token: number | string) => {
  if (!scene_id_or_token) return
  const loginResultFN = isLenovo ? GetLenovoLoginResult : GetWXQRLoginResult
  loginResultFN(scene_id_or_token)
    .then(_ => {
      if (_.data.code === 0) {
        if (!_.data.data) {
          proxy.$message.error('登录信息获取失败')
        }
        token.value = _.data.data
        localStorage.token = token.value
        userStore.setToken(token.value)
        getUserInfo()
        clearInterval(timer)
      } else {
        console.log('getLoginResult', _)
      }
    })
    .catch(err => {
      console.log(err)
    })
}
// 3
const getUserInfo = () => {
  GetUserInfoAndToken()
    .then(_ => {
      if (_.data.code === 0) {
        const userInfo = _.data.data || {}
        userStore.setUser(userInfo)
        localStorage.user = JSON.stringify(userInfo)
        closeDialog('loginVisible')
        const returnPage: any = route.query.returnPage
        if (returnPage) {
          location.replace(returnPage)
        } else {
          // location.reload()
          loginVisible.value = false
        }

        bridge.updateAppIni({
          userType: userInfo.type,
          token: localStorage.token,
        })
      }
    })
    .catch(err => {
      console.log(err)
    })
}
const closeDialog = (type?: string) => {
  if (type === 'loginVisible') {
    loginVisible.value = false
  }
  if (type === 'promotionVisible') {
    promotionVisible.value = false
  }
  clearInterval(timer)
}

const exit = () => {
  console.log('exit')
  const clear = () => {
    Object.keys(user.value).forEach(item => {
      user.value[item] = ''
    })
    token.value = ''
    localStorage.token = ''
    localStorage.user = ''
    userStore.setToken('')
    userStore.setUser({})
    sessionStorage.mindNums = ''
    location.reload()
  }

  proxy.$messageBox
    .confirm('退出后，您将无法享受VIP权益', '确定退出登录吗？', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      customClass: 'customStyleByMessageBox',
      type: 'warning'
    })
    .then(() => {
      bridge.logout()
      clear()
    })
}
const goNext = (type: string) => {
  if (!localStorage.token) {
    promotionVisible.value = false
    showLogin()
    return
  }
  if (type === 'pay') {
    promotionVisible.value = false
    return payStore.setShowPayDialog(true)
  }
  router.push({
    name: type
  })
}
onUnmounted(() => {
  clearInterval(timer)
})

defineExpose({
  showLogin
})
</script>

<style lang="scss">
.vipPopover {
  margin-left: -30px!important;
}
.winVipPopover {
  margin-left: -30px!important;
}
</style>
<style lang="scss" scoped>
.vipPopover {
  width: 316px;
  padding: 16px;

  .userInfo {
    margin-bottom: 8px;

    .avatar {
      width: 58px;
      height: 58px;
      padding: 3px;
      margin-right: 10px;
      // border: 1px solid rgba(0, 0, 0, 0.2);
      border-radius: 29px;
    }

    .box {
      margin: 6px 0;

      .name {
        max-width: 152px;
        font-size: 14px;
        font-weight: 600;
        color: #333333;
        line-height: 20px;
        margin-right: 8px;
      }

      img {
        position: absolute;
        left: -10px;
        top: 2px;
        width: 20px;
        height: 18px;
      }

      .status {
        position: relative;
        height: 20px;
        padding: 0 4px;
        margin-left: 5px;
        line-height: 20px;
        border-radius: 3px;
        font-size: 12px;
        font-weight: 600;
        color: #666666;
        background: #f4f4f4;
        text-align: center;
      }

      .member {
        margin-left: 8px;
      }
    }

    .id {
      font-size: 12px;
      color: #333333;
    }
  }

  .equity {
    line-height: 28px;
    font-size: 16px;
    font-weight: 600;
    color: #333333;

    .tip {
      margin: 0 9px 0 8px;
      font-size: 12px;
      color: #999999;
      line-height: 17px;
    }
  }

  .open {
    width: 74px;
    height: 28px;
    padding: 7px 12px;
    // line-height: 28px;
    font-size: 14px;
    font-weight: 600;
    border: 0;
    border-radius: 3px;
    color: #662b04;
    background: linear-gradient(90deg, #f6deb5 0%, #eabc6c 100%);
    &:hover {
      opacity: 0.7;
    }
  }

  .equityInfo {
    margin-top: 8px;
    padding: 12px 20px;
    width: 100%;
    height: 125px;
    background: #f8faff;
    border-radius: 8px;

    .flex1 {
      margin-bottom: 4px;
      font-size: 12px;
      color: #000000;
      line-height: 17px;
    }
  }

  .exit {
    position: relative;
    left: 260px;
    top: 8px;
    width: 30px;
    height: 28px;
    padding: 0;
    margin-bottom: 5px;
    border: 0;
    font-size: 12px;
    font-weight: normal;
    color: #333;
    background: transparent;
  }
}

// 头像
.menu {
  position: relative;
  -webkit-app-region: no-drag;
  cursor: pointer;

  img {
    margin: 0 8px;
    width: 32px;
    height: 32px;
  }

  .avatar {
    position: relative;
    margin: 0;
    left: 10px;
  }
}

// 用户名
.username {
  line-height: 32px;
  max-width: 200px;
  margin: 0 12px;
  font-size: 14px;
  font-weight: 400;
  color: #333333;
  -webkit-app-region: no-drag;
  cursor: pointer;
}

// 优惠和登录弹框
.promotion,
.login {
  position: relative;
  z-index: 2001;

  .box {
    position: fixed;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    margin: auto;
    // 432 * 528
    width: 360px;
    height: 456px;
    border-radius: 8px;
    // transform: scale(1.2);
  }

  .icon {
    width: 360px;
    // height: 456px;
  }

  .pay {
    cursor: pointer;
  }

  .qrcode {
    width: 240px;
    height: 240px;
    position: absolute;
    left: 60px;
    top: 103px;
  }
  .expireBox {
    position: absolute;
    left: 0;
    top: 0;
    cursor: pointer;

    .expire {
      width: 214px;
      height: 214px;
      left: 73px;
      top: 116px;
      background: #000000;
      opacity: 0.6;
    }
    .refresh {
      position: absolute;
      left: 148.5px;
      top: 174px;
      width: 63px;
      height: 63px;
    }
    p {
      position: absolute;
      left: 60px;
      top: 254px;
      width: 240px;
      font-size: 14px;
      font-weight: 700;
      text-align: center;
      color: #FFFFFF;
    }
  }

  .tip {
    position: absolute;
    bottom: 90px;
    left: 108px;
    font-size: 14px;
    color: #333333;
    background-color: #fff;
    user-select: none;
    span {
      color: #389bfd;
      cursor: pointer;
    }
  }
}

.promotion {
  .close {
    position: absolute;
    top: 18px;
    right: -40px;
    width: 42px;
    height: 42px;
    cursor: pointer;
  }
  .pay-tip {
    position: absolute;
    bottom: 85px;
    left: 140px;
    font-size: 20px;
    font-weight: 600;
    color: #fff;
    cursor: pointer;
  }
}
.login {
  div {
    position: absolute;
    top: 34px;
    right: 42px;
    width: 25px;
    height: 20px;
    background-color: #fff;

    .close {
      position: absolute;
      top: 0;
      right: 0;
      width: 20px;
      height: 20px;
      cursor: pointer;
      background-color: #fff;
    }
  }
}

.loginButton {
  position: relative;
  top: 2px;
  margin: 0 15px;
  font-size: 14px;
  color: #333333;
  font-weight: normal;
  -webkit-app-region: no-drag;

  &:hover {
    font-size: 14px;
    color: #333333;
    opacity: 0.8;
  }
}
</style>
