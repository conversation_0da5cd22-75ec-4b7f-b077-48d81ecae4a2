<script setup lang="ts">
const info = {
  'pic.mymind365.com': {
    company: '苏州妙吉信息科技有限公司',
    ICP: '苏ICP备2021036247号-2',
  },
  'pic.bestmind365.com': {
    company: '上海淘艺文化传媒有限公司',
    ICP: '沪ICP备2022029399号',
  },
}
const current = info[window.hostName] || info['pic.mymind365.com']
</script>

<template>
  <div class="footer">
    <p>
      Copyright © {{ new Date().getFullYear() }} {{ current.company }} 版权所有
    </p>
    <p>
      网站备案号：<a
        href="https://beian.miit.gov.cn/"
        target="_blank"
        style="color: #fff"
        rel="noreferrer"
        >{{ current.ICP }}</a
      >
      ｜
      <a
        href="https://www.miaoji66.com/private.html"
        target="_blank"
        style="color: #fff"
        rel="noreferrer"
        >隐私协议</a
      >
      ｜
      <a
        href="https://www.miaoji66.com/agreement.html"
        target="_blank"
        style="color: #fff"
        rel="noreferrer"
        >用户协议</a
      >
    </p>
  </div>
</template>

<style lang="scss" scoped>
.footer {
  height: 112px;
  padding-top: 24px;
  background: #1c1c1e;

  p {
    font-size: 16px;
    color: #ffffff;
    line-height: 32px;
    text-align: center;
  }
}
</style>
