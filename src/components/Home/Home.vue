<template>
  <div class="home">
    <div class="box first">
      <div class="content flex">
        <div class="left">
          <img class="header-new-icon" src="/img/mind_map/web/header_new_icon.png" />
          <p class="title">简单易用的在线思维导图</p>
          <p class="desc">
            专业强大作图工具，支持思维导图、流程图、鱼骨图等多种图形绘制
          </p>
          <el-button type="primary" @click="goNext('list')">开始使用<img class="arrow-start"
              src="/img/mind_map/web/arrow_start.png" /></el-button>
          <div class="btn-tip">
            全球 <span class="nums">{{ nums }}</span> 用户选择
          </div>
        </div>
        <div class="right">

          <img class="right header-mind" src="/img/mind_map/web/header_mind.png" />
        </div>
      </div>
    </div>
    <div class="box two">
      <div class="content">
        <div class="title">专业导图软件，功能强大，操作简单</div>
        <div class="desc">提供海量贴纸、图标、模板等素材，不间断更新</div>
        <img class="main-mind" src="/img/mind_map/web/main_mind.png" />
      </div>
    </div>
    <div class="box three">
      <div class="content">
        <div class="main-title">不止于思维导图，提供更多专业功能</div>
        <div class="main-desc">区别传统思维导图工具，拥有更多个性化、贴心功能</div>
        <div class="list flex" @click="goNext('list')">
          <div class="item flex">
            <img src="/img/mind_map/web/icon_1.png" alt="">
            <div class="info">
              <p class="title">文件云存储</p>
              <p class="desc">基于云的跨端思维导图软件，多端云同步存储</p>
            </div>
          </div>
          <div class="item flex">
            <img src="/img/mind_map/web/icon_2.png" alt="">
            <div class="info">
              <p class="title">种类多，类型全</p>
              <p class="desc">思维导图、鱼骨图、组织结构图、时间轴、树状图...</p>
            </div>
          </div>
          <div class="item flex">
            <img src="/img/mind_map/web/icon_3.png" alt="">
            <div class="info">
              <p class="title">自动保存</p>
              <p class="desc">每步操作自动同步至云端，无须担心忘记</p>
            </div>
          </div>
        </div>
        <div class="list flex" @click="goNext('list')">
          <div class="item flex">
            <img src="/img/mind_map/web/icon_4.png" alt="">
            <div class="info">
              <p class="title">多格式导出</p>
              <p class="desc">支持导出png、svg、pdf等多种格式</p>
            </div>
          </div>
          <div class="item flex">
            <img src="/img/mind_map/web/icon_5.png" alt="">
            <div class="info">
              <p class="title">文件管理</p>
              <p class="desc">可创建文件夹、移入、移出、重命名等</p>
            </div>
          </div>
          <div class="item flex">
            <img src="/img/mind_map/web/icon_6.png" alt="">
            <div class="info">
              <p class="title">备注/超链接</p>
              <p class="desc">插入备注/超链接，鼠标点击即可实现跳转</p>
            </div>
          </div>
        </div>
        <div class="list flex" @click="goNext('list')">
          <div class="item flex">
            <img src="/img/mind_map/web/icon_7.png" alt="">
            <div class="info">
              <p class="title">文件分享</p>
              <p class="desc">做好脑图可一键批量分享，并支持加密、限时操作</p>
            </div>
          </div>
          <div class="item flex">
            <img src="/img/mind_map/web/icon_8.png" alt="">
            <div class="info">
              <p class="title">海量模板</p>
              <p class="desc">专业团队，持续更新，让您放心用</p>
            </div>
          </div>
          <div class="item flex">
            <img src="/img/mind_map/web/icon_9.png" alt="">
            <div class="info">
              <p class="title">高颜值主题样式</p>
              <p class="desc">提供几十种预置样式，一键使用 </p>
            </div>
          </div>
        </div>
        <div class="list flex last" @click="goNext('list')">
          <div class="item flex">
            <img src="/img/mind_map/web/icon_10.png" alt="">
            <div class="info">
              <p class="title">贴纸&图标</p>
              <p class="desc">内置上百种贴纸&图标，满足多场景需求</p>
            </div>
          </div>
          <div class="item flex">
            <img src="/img/mind_map/web/icon_11.png" alt="">
            <div class="info">
              <p class="title">防误删</p>
              <p class="desc">文件删除误操作，回收站里可找回（过期自动删）</p>
            </div>
          </div>
          <div class="item flex">
            <img src="/img/mind_map/web/icon_12.png" alt="">
            <div class="info">
              <p class="title">支持概要&关联线</p>
              <p class="desc">操作简单、便捷，更省心 </p>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="box four">
      <div class="content flex">
        <div class="mind-list left flex">
          <div class="flex1">
            <img src="/img/mind_map/web/mind_list1.png" style="margin-bottom:6px;" alt="模板-图1_1">
            <img src="/img/mind_map/web/mind_list1.png" alt="模板-图1_2">
          </div>
          <div class="flex1">
            <img src="/img/mind_map/web/mind_list2.png" style="margin-bottom:6px;" alt="模板-图2_1">
            <img src="/img/mind_map/web/mind_list2.png" alt="模板-图2_2">
          </div>
          <div class="flex1 last">
            <img src="/img/mind_map/web/mind_list3.png" style="margin-bottom:6px;" alt="模板-图3_1">
            <img src="/img/mind_map/web/mind_list3.png" alt="模板-图3_2">
          </div>
          <div class="transparent_top"></div>
          <div class="transparent_bottom"></div>
        </div>
        <div class="right">
          <div class="title">海量专业模板</div>
          <div class="desc">各行业精品模板可供参照和使用，专业案例，激发您的灵感</div>
          <el-button type="primary" @click="goNext('template')">进入模板库<img class="arrow"
              src="/img/mind_map/web/arrow.png" /></el-button>
        </div>
      </div>
    </div>
    <div class="box five">
      <div class="content">
        <div class="main-title">众多用户的选择</div>
        <div class="main-desc">VIP服务，有口皆碑</div>
        <div class="list flex">
          <div class="item">
            <img class="avatar" src="/img/mind_map/web/avatar1.png" alt="">
            <img class="five-star" src="/img/mind_map/web/fivestar.png" alt="">
            <p class="desc">尝试过很多思维导图软件，感觉这个最简洁、美观。基本上可以满足我日常头脑风暴。
              最重要的是操作足够简单，易用。推荐一下！</p>
            <p class="user">用户：霞**路</p>
          </div>
          <div class="item">
            <img class="avatar" src="/img/mind_map/web/avatar2.png" alt="">
            <img class="five-star" src="/img/mind_map/web/fivestar.png" alt="">
            <p class="desc">一直不明白思维导图怎么用，无意间发现这个，才豁然开朗。很适合像我这样的新手使用。</p>
            <p class="user">用户：木*子</p>
          </div>
          <div class="item">
            <img class="avatar" src="/img/mind_map/web/avatar3.png" alt="">
            <img class="five-star" src="/img/mind_map/web/fivestar.png" alt="">
            <p class="desc">我是学生党，用这个进行知识点梳理简直太方便了。简单拖拽，自动对齐，还能云端保存，非常赞。已经安利给同学。</p>
            <p class="user">用户：时**e</p>
          </div>
        </div>
        <div class="list flex">
          <div class="item">
            <img class="avatar" src="/img/mind_map/web/avatar4.png" alt="">
            <img class="five-star" src="/img/mind_map/web/fivestar.png" alt="">
            <p class="desc">自动保存功能非常棒，完全不用担心没点保存按钮。非常人性化。比之前用的好太多了。非常推荐！</p>
            <p class="user">用户：大**上</p>
          </div>
          <div class="item">
            <img class="avatar" src="/img/mind_map/web/avatar5.png" alt="">
            <img class="five-star" src="/img/mind_map/web/fivestar.png" alt="">
            <p class="desc">软件对我这样的小白很友好。没有乱起八糟功能，使用起来非常简单。工作中的要点很轻松就可以梳理清楚，不错。</p>
            <p class="user">用户：Y*t</p>
          </div>
          <div class="item">
            <img class="avatar" src="/img/mind_map/web/avatar6.png" alt="">
            <img class="five-star" src="/img/mind_map/web/fivestar.png" alt="">
            <p class="desc">朋友推荐的，用起来确实可以。主要操作上比较简单，非常容易上手。常用的功能也都有，而且做完后还支持加密分享，满足我的要求。</p>
            <p class="user">用户：平**奇</p>
          </div>
        </div>
      </div>
    </div>
    <div class="box six">
      <!-- <img src="/img/mind_map/web/5.png" /> -->
      <p>即刻加入我们，发散你的思维</p>
      <el-button type="primary" @click="goNext('list')">开始使用</el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
// import 'animate.css';
import util from "@haluo/util";
import { onMounted } from "vue";
import { useRouter } from "vue-router";
const router = useRouter();
const goNext = (type: string) => {
  router.push({
    name: type,
  });
};

const nums = util.number.formatMoney(
  parseInt((Date.now() / 211 + 3421) / 1000 + "")
);

onMounted(() => {
  const mindList = {
    0: 1924.88,
    1: 1964,
    2: 2092.17,
  }
  const list: any = document.querySelectorAll('.mind-list .flex1')
  const num = 0.2
  setInterval(() => {
    let top0 = Number(list[0].style.top.replace('px', '') || 0)
    let top1 = Number(list[1].style.top.replace('px', '') || 0)
    let top2 = Number(list[2].style.top.replace('px', '') || 0)

    // console.log('before', top0, top1, top2);
    if (top0 < -mindList[0]) {
      list[0].style.top = 0 + 'px'
    } else {
      list[0].style.top = top0 - num + 'px'
    }

    if (0 <= top1) {
      list[1].style.top = -mindList[1] + 'px'
    } else {
      list[1].style.top = top1 + num + 'px'
    }

    if (top2 < -mindList[2]) {
      list[2].style.top = 0 + 'px'
    } else {
      list[2].style.top = top2 - num + 'px'
    }
    // console.log('after', top0, top1, top2);
  }, num);
})


</script>

<style lang="scss" scoped>
.home {
  position: relative;
  padding-top: 60px;
  min-width: 1200px;
  margin: 0 auto;

  .box {
    position: relative;
    overflow: hidden;

    .content {
      width: 1200px;
      margin: 0 auto;
    }
  }

  .first {
    height: 720px;
    background: url(/img/mind_map/web/header_wave.png) no-repeat left bottom/contain;

    .left {
      flex: 1;

      @keyframes pronamei {
        0% {
          top: -10px;
        }

        50% {
          top: 0;
        }

        100% {
          top: -10px;
        }
      }

      @-webkit-keyframes pronamei {
        0% {
          top: -10px;
        }

        50% {
          top: 0;
        }

        100% {
          top: -10px;
        }
      }

      .header-new-icon {
        margin-top: 141px;
        width: 128px;
        height: 48px;
        margin-left: 576px;

        position: relative;
        animation: pronamei 1s infinite both;
        -webkit-animation: pronamei 1s infinite both;
        -moz-animation: pronamei 1s infinite both;
        -o-animation: pronamei 1s infinite both;
      }

      .title {
        height: 90px;
        font-size: 64px;
        font-weight: 600;
        color: #333333;
        line-height: 90px;
      }

      .desc {
        margin-top: 20px;
        height: 33px;
        font-size: 24px;
        color: #999999;
        line-height: 33px;
      }

      .el-button {
        margin-top: 110px;
        padding: 15px 33px;
        width: 210px;
        height: 62px;
        background: #1890ff;
        border-radius: 12px;

        font-size: 24px;
        font-weight: 600; // electron
        color: #ffffff;
        line-height: 33px;

        .arrow-start {
          width: 32px;
          height: 32px;
          margin-left: 16px;
        }

        &:hover {
          opacity: .8;
        }
      }

      .btn-tip {
        margin-top: 20px;
        height: 21px;
        font-size: 17px;
        font-weight: 400;
        color: #333333;
        line-height: 21px;

        .nums {
          font-size: 20px;
          font-weight: 600; // electron
        }
      }
    }

    .right {
      .header-mind {
        margin-top: 157px;
        width: 400px;
        height: 466px;
      }
    }
  }

  .two {
    height: 922px;
    // background: url(/img/mind_map/web/cover2.png) repeat left bottom/contain;

    .content {
      text-align: center;

      .title {
        margin-top: 80px;
        height: 67px;
        font-size: 48px;
        font-weight: 600;
        color: #333333;
        line-height: 67px;
      }

      .desc {
        margin-top: 24px;
        height: 33px;
        font-size: 24px;
        color: #999999;
        line-height: 33px;
      }

      .main-mind {
        margin-top: 54px;
        width: 1024px;
        height: 587px;
      }
    }
  }

  .three {
    height: 1000px;
    background-color: #fff;

    .content {
      text-align: center;

      .main-title {
        margin-top: 80px;
        font-size: 48px;
        font-weight: 600;
        color: #333333;
        line-height: 67px;
      }

      .main-desc {
        margin: 24px 0 60px;
        font-size: 24px;
        color: #999999;
        line-height: 33px;
      }

      .list {
        padding: 0 16px 20px;

        .item {
          margin: 0 50px 0 0;
          padding: 18px 16px;

          &:last-child {
            margin-right: 0;
          }

          img {
            width: 108px;
            height: 108px;
          }

          .info {
            text-align: left;

            .title {
              margin-top: 12px;
              font-size: 22px;
              font-weight: 600;
              color: #333333;
              line-height: 30px;
            }

            .desc {
              margin-top: 5px;
              width: 220px;
              font-size: 18px;
              font-weight: 400;
              color: #999999;
              line-height: 25px;
            }
          }

          &:hover {
            width: 360px;
            height: 144px;
            background: #FFFFFF;
            box-shadow: 0px 2px 11px 0px rgba(216, 216, 216, 0.5);
            border-radius: 9px;
            cursor: pointer;
          }
        }
      }

      .last {
        padding-bottom: 0;
      }
    }
  }

  .four {
    padding-top: 80px;
    height: 923px;
    // background: url(/img/mind_map/web/cover3.png) repeat left bottom/contain;
    overflow: hidden;

    .mind-list {
      position: relative;
      width: 740px;

      .transparent_bottom,
      .transparent_top {
        position: absolute;
        width: 100%;
        height: 40px;
        left: 0;
        z-index: 1
      }

      .transparent_top {
        top: 0;
        background-image: -webkit-linear-gradient(180deg, #F9F9FA 0, hsla(0, 0%, 100%, 0));
        background-image: linear-gradient(180deg, #F8F9F9 0, hsla(0, 0%, 100%, 0))
      }

      .transparent_bottom {
        bottom: 0;
        background-image: -webkit-linear-gradient(0deg, #F9F9FA 0, hsla(0, 0%, 100%, 0));
        background-image: linear-gradient(0deg, #F8F9F9 0, hsla(0, 0%, 100%, 0))
      }

      margin-right: 99px;
      width: 740px;
      height: 743px;
      // background: linear-gradient(180deg, #F9F9FA 0%, rgba(244, 244, 245, 0) 16%, rgba(236, 236, 236, 0) 49%, rgba(244, 245, 245, 0) 85%, #F8F9F9 100%);
      overflow: hidden;

      .flex1 {
        position: relative;
        margin-right: 14.5px;

        img {
          width: 100%;
        }
      }

      .last {
        margin-right: 0;
      }
    }

    .right {
      .title {
        margin-top: 216px;
        width: 288px;
        height: 67px;
        font-size: 48px;
        font-weight: 600;
        color: #333333;
        line-height: 67px;
      }

      .desc {
        margin-top: 24px;
        width: 360px;
        height: 66px;
        font-size: 24px;
        color: #999999;
        line-height: 33px;
      }

      .el-button {
        margin-top: 40px;
        width: 210px;
        height: 62px;
        border-radius: 8px;
        border: 2px solid #1890FF;
        background-color: transparent;

        font-size: 24px;
        font-weight: 400;
        color: #1890FF;
        line-height: 62px;

        .arrow {
          width: 32px;
          height: 32px;
        }

        &:hover {
          opacity: .7;
        }
      }
    }
  }

  .five {
    height: 983px;
    background-color: #fff;


    .content {
      text-align: center;

      .main-title {
        margin-top: 80px;
        font-size: 48px;
        font-weight: 600;
        color: #333333;
        line-height: 67px;
      }

      .main-desc {
        margin: 24px 0 80px;
        font-size: 24px;
        color: #999999;
        line-height: 33px;
      }

      .list {
        .item {
          position: relative;
          width: 380px;
          height: 248px;
          margin: 0 30px 80px 0;
          padding: 0 18px;
          box-shadow: 0px 2px 16px 12px rgba(0, 0, 0, 0.04);
          border-radius: 12px;

          &:last-child {
            margin-right: 0;
          }

          .avatar {
            display: block;
            position: relative;
            top: -32px;
            width: 64px;
            height: 64px;
            margin: 0 auto;
          }

          .five-star {
            display: block;
            position: relative;
            top: -12px;
            width: 98px;
            height: 14px;
            margin: 0 auto;
          }

          .desc {
            position: relative;
            top: 10px;
            width: 344px;
            height: 72px;
            font-size: 16px;
            font-weight: 400;
            color: #000000;
            line-height: 24px;
            text-align: left;
          }

          .user {
            position: absolute;
            left: 148px;
            bottom: 24px;
            width: 85px;
            height: 20px;
            font-size: 14px;
            font-weight: 600; // electron
            color: #666666;
            line-height: 20px;
          }
        }
      }
    }
  }

  .six {
    height: 376px;
    text-align: center;
    background: url(/img/mind_map/web/5.png);

    p {
      margin-top: 98px;
      font-size: 64px;
      font-weight: 600;
      color: #FFFFFF;
      line-height: 90px;

    }

    .el-button {
      margin-top: 40px;
      width: 135px;
      height: 49px;
      border-radius: 3px;
      background: transparent;
      border: 1px solid #ffffff;

      font-size: 22px;
      font-weight: 600;
      color: #ffffff;

      &:hover {
        opacity: .8;
      }
    }
  }
}
</style>
