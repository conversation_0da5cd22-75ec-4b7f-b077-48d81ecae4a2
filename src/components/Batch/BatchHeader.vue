<template>
  <div class="batch-header flex" style="margin-right: 309px">
    <div class="flex1">
      <div class="flex">
        <div class="flex back-wrap" @click="back">
          <img class="back-img" src="/img/pic/second/<EMAIL>" alt="" />
          <p class="back-text">返回</p>
        </div>
      </div>
    </div>
    <div
      v-if="isElectron"
      class="export-directory flex"
      @click="showExportDirectoryDialog = true"
    >
      <img class="img" src="/img/pic/second2/<EMAIL>" alt="" />
      <p>导出目录</p>
    </div>
    <div class="export flex" @click="exportImg">
      <img class="img" src="/img/pic/second2/<EMAIL>" alt="" />
      <el-popover
        popper-class="batchHeaderCustomerPopover customerPopover"
        effect="light"
        placement="bottom"
        trigger="hover"
        width="92px"
        :show-arrow="false"
      >
        <template #reference>
          <p>立即导出</p>
        </template>
        <template #default>
          <div class="btn mb13" @click="exportHandler('current')">当前图片</div>
          <div class="btn" @click="exportHandler('all')">全部图片</div>
        </template>
      </el-popover>
    </div>
  </div>
</template>

<script setup lang="ts">
// import { goLogin, goPay } from '@/utils/user'
import { storeToRefs } from 'pinia'
import { useCommonStore } from '@/pinia/common'
const commonStore = useCommonStore()
let { showExportDirectoryDialog } = storeToRefs(commonStore)
const { proxy } = getCurrentInstance()
const isElectron = window.isElectron

const props = defineProps({
  currentStore: {
    type: Object,
    default: {},
  },
})

const back = () => {
  proxy.$messageBox
    .confirm(' ', '确定返回吗？', {
      distinguishCancelAndClose: true,
      confirmButtonText: '确定', // 保留
      cancelButtonText: '取消', // 不保留
      customClass: 'customStyleByMessageBox',
      type: 'warning',
      // center: true
    })
    .then((e: any) => {
      if (e === 'confirm') {
        props.currentStore.clear()
      }
    })
    .catch((e: any) => {})
}

const exportHandler = (type: string) => {
  props.currentStore.beforeConvert(type)
}

// const upload = (e: any) => {
//   if (goLogin()) return
//   if (goPay()) return

//   if (isElectron) {
//     pictureAliStore.electronUpload(e)
//   } else {
//     pictureAliStore.webUpload(e)
//   }
// }

const exportImg = () => {
  // const url = pageInfo.value[pageType.value].currentImg.new
  // if (!url) {
  //   return messageSimple.warning('图片转换中，请稍等')
  // }
  // proxy.$filter.tools.debounce(() => {
  //   pictureAliStore.exportImg({
  //     url,
  //     filePath: filePath.value,
  //   })
  // }, 300)()
}
</script>
<style lang="scss">
.batchHeaderCustomerPopover {
  font-weight: 400;
  font-size: 14px;
  color: #666666;
  line-height: 230px;
  margin-right: -10px!important;
  z-index: 10!important;

  .btn:hover {
    opacity: .7;
  }
}
</style>
<style lang="scss" scoped>
.batch-header {
  height: 20px;
  margin: 20px 0 31px 20px;
  user-select: none;

  .back-wrap {
    cursor: pointer;
    &:hover {
      opacity: 0.7;
    }
    .back-img {
      width: 20px;
      height: 20px;
    }
    .back-text {
      font-size: 14px;
      color: #666666;
      line-height: 20px;
    }
  }

  .el-button {
    height: 20px;
    margin-left: 16px;
    padding: 0;
    font-weight: 400;
    font-size: 14px;
    color: #389bfd;
    line-height: 24px;
    border-color: transparent !important;
    &:hover {
      opacity: 0.7;
    }
  }

  .export-directory {
    font-weight: 400;
    font-size: 14px;
    color: #666666;
    line-height: 20px;
    cursor: pointer;
    &:hover {
      opacity: 0.7;
    }

    .img {
      position: relative;
      top: 0;
      margin-right: 4px;
      width: 20px;
      height: 20px;
    }
  }

  .export {
    margin-left: 20px;
    font-weight: 400;
    font-size: 14px;
    color: #389bfd;
    line-height: 20px;
    cursor: pointer;
    &:hover {
      opacity: 0.7;
    }

    .img {
      position: relative;
      top: 0.5px;
      margin-right: 4px;
      width: 20px;
      height: 20px;
    }
  }
}
</style>
