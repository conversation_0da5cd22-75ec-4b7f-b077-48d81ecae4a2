<template>
  <div class="change-size-component">
    <!-- 搜索框 -->
    <div class="search-container">
      <el-input
        class="search"
        placeholder="搜索名称、尺寸"
        :prefix-icon="Search"
        icon_search
        v-model="searchValue"
        clearable
        @input="searchValueChange"
        >搜索名称、尺寸</el-input
      >
    </div>

    <!-- 分类列表 -->
    <section v-if="showSearchResult" class="category-box">
      <div class="tabs flex">
        <a
          v-for="item in categoryList"
          class="tab"
          :key="item.id"
          :class="{
            selected: currentTypeId == item.id,
          }"
          :data-id="item.id"
          :data-name="item.name"
          :href="`#size_${item.id}${item.name}`"
          @click="(event) => handleTabClick(item, event)"
        >
          {{ item.name }}
        </a>
      </div>
      <div class="category" v-scroll-effect>
        <div class="module" v-for="item in categoryList" :key="item.id">
          <div
            v-if="item.name === '自定义' || item.list?.length"
            class="title"
            :name="`size_${item.id}${item.name}`"
            :id="`size_${item.id}${item.name}`"
            :data-id="item.id"
          >
            {{ item.name }}
          </div>
          <div v-if="item.name === '自定义'" class="custom flex">
            <div
              class="btn center"
              :class="{
                selected: currentImg.record.name === custom.name,
              }"
              v-for="custom in customArr"
              :key="custom.name"
              @click="chooseSize(custom)"
            >
              {{ custom.name }}
            </div>
          </div>
          <div class="list flex">
            <template v-for="content in item.list" :key="content.name">
              <div
                class="item"
                :class="{
                  selected: currentImg.record.name === content.name,
                }"
                @click="chooseSize(content)"
              >
                <p
                  class="desc dotdotdot1"
                  v-html="
                    content.name.replace(searchValue, `<em>${searchValue}</em>`)
                  "
                ></p>
                <div class="size-info flex">
                  <p
                    class="size"
                    v-html="
                      `${content.print_with}*${content.print_height}mm`.replace(
                        searchValue,
                        `<em>${searchValue}</em>`
                      )
                    "
                  ></p>
                  <el-tooltip
                    effect="dark"
                    :content="`${content.pixel_with}*${content.pixel_height}px`"
                    placement="bottom"
                  >
                    <img
                      class="icon"
                      src="/img/pic/second2/<EMAIL>"
                      alt=""
                    />
                  </el-tooltip>
                </div>
              </div>
            </template>
          </div>
        </div>
      </div>
    </section>

    <!-- 无搜索结果 -->
    <section v-else class="no-result">
      <img src="/img/pic/second2/icon_search_ <EMAIL>" />
      <p>未搜索到结果</p>
    </section>

    <!-- 应用到全部按钮 -->
    <div v-if="props.showApplyAll" class="apply-wrap">
      <el-button type="primary" class="btn flex" @click="applyAll">
        <img src="/img/pic/second2/<EMAIL>" class="icon" />
        <div>应用到全部</div>
      </el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useMattingStore } from '@/pinia/matting'
import { usePassportStore } from '@/pinia/passport'
import { pageType as pageTypeEnum } from '@/utils/enum'
import { messageSimple } from '@/utils/message'
import { getInstance } from '@/utils/tabsScroll'
import { Search } from '@element-plus/icons-vue'
import { useRoute } from 'vue-router'

const props = defineProps({
  showApplyAll: {
    type: Boolean,
    default: false,
  },
})

const { proxy } = getCurrentInstance()
const route = useRoute()

const getStore = () => {
  switch (route.name) {
    case pageTypeEnum.matting:
      return useMattingStore()
    case pageTypeEnum.passport:
      return usePassportStore()
    default:
      return useMattingStore()
  }
}
const currentStore: ReturnType<typeof useMattingStore | typeof usePassportStore> = getStore()

// 使用计算属性来获取当前 store 的状态
const currentImg = computed(() => currentStore.currentImg)
const categoryList = computed(() => currentStore.categoryList)
const showSearchResult = computed(() => currentStore.showSearchResult)
const cropper = computed(() => currentStore.cropper)

// 创建TabsScroll实例
const tabsScroller = getInstance('size')
const currentTypeId = tabsScroller.currentTypeId

// 处理标签点击
const handleTabClick = (item: any, event: Event) => {
  tabsScroller.tabClick(item, event)
}

// 自定义尺寸数组，用于模板渲染
const customArr = [
  { name: '自由' },
  { name: '1:1' },
  { name: '16:9' },
  { name: '9:16' },
  { name: '3:4' },
  { name: '4:3' },
  { name: '3:2' },
  { name: '2:3' },
]

let searchValue = ref('')

// 初始化获取列表
currentStore.getListAll()

// 搜索值变化
const searchValueChange = () => {
  const params = {
    name: searchValue.value,
  }
  proxy.$filter.tools.debounce(() => {
    currentStore.getListAll(params)
  }, 300)()
}

// 选择尺寸
const chooseSize = (custom: any) => {
  currentStore.chooseSize(custom)
}

// 应用到全部
const applyAll = () => {
  // 直接修改 store 中的值
  currentStore.applyToAll = true

  // 遍历并更新所有图片
  const currentImgValue = currentImg.value
  const imgsMapValue = currentStore.imgsMap

  imgsMapValue.forEach((img: any) => {
    img.record.aspectRatio = currentImgValue.record.aspectRatio // 记录比例

    img.record.left = currentImgValue.record.left
    img.record.top = currentImgValue.record.top
    img.record.autoCropWidth = currentImgValue.record.autoCropWidth // 自动裁剪宽度
    img.record.autoCropHeight = currentImgValue.record.autoCropHeight // 自动裁剪高度
    img.record.pixel_with = currentImgValue.record.pixel_with // 记录输出尺寸
    img.record.pixel_height = currentImgValue.record.pixel_height // 记录输出尺寸

    img.record.scale = currentImgValue.record.scale // 记录缩放
    img.record.name = currentImgValue.name // 记录尺寸选中名字
  })

  messageSimple.success('应用成功')
}

// 处理窗口大小变化
const handleResize = () => {
  // console.log("handleResize");
  try {
    cropper.value?.reset(); // 重新加载裁剪框
  } catch (error) {
    console.error("重新加载裁剪框失败:", error);
  }
};

onMounted(() => {
  window.addEventListener("resize", handleResize);

  // Initialize the TabsScroll instance with specific selectors for this component
  tabsScroller.init(
    '.change-size-component .category-box .tabs',
    '.change-size-component .category-box .category',
    100
  )
})
onUnmounted(() => {
  window.removeEventListener("resize", handleResize);

  // Clean up the TabsScroll instance
  tabsScroller.cleanup()
})
</script>

<style lang="scss" scoped>
.change-size-component {
  display: flex;
  flex-direction: column;
  height: 100%;

  .search-container {
    margin-bottom: 12px;
  }

  .search {
    width: 249px;
    height: 32px;
    border-radius: 20px;
    border: 1px solid rgba(0, 0, 0, 0.15);

    font-size: 14px;
    color: #999999;
    line-height: 32px;
    overflow: hidden;

    :deep(.el-input__wrapper) {
      box-shadow: none !important;
    }
  }

  .category-box {
    flex: 1;
    height: calc(100% - 50px);
    width: calc(249px + 12px);
    overflow: hidden;

    .tabs {
      flex-wrap: wrap;

      .tab {
        margin: 0 12px 10px 0;
        font-weight: 400;
        font-size: 14px;
        color: #666666;
        line-height: 20px;
        cursor: pointer;

        &:hover {
          opacity: 0.7;
        }
      }
      .selected {
        color: #006eff;
      }
    }

    .category {
      position: relative;
      height: calc(100% - 52px - 30px);
      width: 249px;
      overflow-x: hidden;
      overflow-y: auto;

      .custom {
        flex-wrap: wrap;

        .btn {
          margin: 0 12px 12px 0;
          width: 52px; // +1px
          height: 28px;
          border: 1px solid #eaeaea;
          border-radius: 4px;

          font-weight: 400;
          font-size: 14px;
          color: #333333;
          align-items: center;
          cursor: pointer;

          &:nth-child(4n) {
            margin-right: 0;
          }

          &:hover {
            opacity: 0.7;
          }
        }
        .selected {
          color: #006eff;
          border: 1px solid #006eff;
        }

        .text {
          padding: 6px 0 6px 11px;

          img {
            width: 16px;
            height: 14px;
            margin-right: 4px;
          }
        }
        .img {
          padding: 6px 0 6px 7px;
          margin-left: 12px;

          img {
            width: 18px;
            height: 16px;
            margin-right: 6px;
          }
        }
      }

      .module {
        .title {
          margin: 12px 0;
          font-weight: 600; // electron
          font-size: 14px;
          color: #333333;
          line-height: 20px;
        }

        .list {
          width: 354px;
          flex-wrap: wrap;
          user-select: none;

          .item {
            position: relative;
            margin: 0 13px 12px 0;
            padding: 12px;
            width: 115px;
            height: 63px;
            background: #ffffff;
            border-radius: 4px;
            border: 1px solid #eaeaea;
            justify-content: center;
            align-items: center;
            cursor: pointer;

            :deep(em) {
              color: #389bfd;
              font-style: normal;
            }

            &:hover {
              opacity: 0.7;
            }

            :nth-child(2n + 1) {
              margin-right: 0;
            }

            .desc {
              font-weight: 500;
              font-size: 14px;
              color: #333333;
              line-height: 20px;
            }

            .size {
              font-size: 12px;
              color: #999999;
              line-height: 17px;
              margin-top: 4px;
            }

            .icon {
              position: absolute;
              right: 13px;
              bottom: 11px;
              width: 14px;
              height: 14px;
            }
          }

          .selected {
            color: #006eff;
            border: 1px solid #006eff;
          }
        }
      }
    }
  }
  // .category-box-show-apply-all {
  //   height: calc(100% - 50px);
  // }

  .no-result {
    flex: 1;
    text-align: center;
    user-select: none;
    img {
      margin: 88px 100px 4px;
      width: 48px;
      height: 48px;
    }
    p {
      font-weight: 400;
      font-size: 14px;
      color: #666666;
      line-height: 22px;
    }
  }

  .apply-wrap {
    position: absolute;
    right: 0;
    bottom: 0;
    width: 289px;
    height: 76px;
    background: #ffffff;

    .el-button {
      margin: 20px;
      width: 249px;
      height: 36px;
      background: #389bfd;
      border: 0;
      border-radius: 6px;

      font-weight: 400;
      font-size: 14px;
      color: #ffffff;
      line-height: 20px;
      text-align: center;
      font-style: normal;

      img {
        width: 16px;
        height: 16px;
        margin-right: 6px;
      }
    }
  }

  .flex {
    display: flex;
    align-items: center;
  }

  .center {
    display: flex;
    justify-content: center;
    align-items: center;
  }
}
</style>
