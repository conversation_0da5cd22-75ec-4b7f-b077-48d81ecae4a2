<template>
  <el-drawer
    modal-class="crop-drawer"
    v-model="drawer"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :with-header="false"
  >
    <!-- 功能选择区域 -->
    <div class="segmented-container">
      <el-segmented
        v-model="props.currentStore.currentSegment"
        :options="props.currentStore.segments"
        block
      />
    </div>

    <!-- 手动精修功能区域 -->
    <section
      v-show="props.currentStore.currentSegment === props.currentStore.segmentsType.custom"
      class="function-container"
    >
      <ChangeCustom />
    </section>

    <!-- 换背景功能区域 -->
    <section v-show="props.currentStore.currentSegment === props.currentStore.segmentsType.changeBackground">
      <ChangeBackground />
    </section>

    <!-- 改尺寸功能区域 -->
    <section
      v-show="props.currentStore.currentSegment === props.currentStore.segmentsType.changeSize"
      class="function-container"
    >
      <ChangeSize />
    </section>
  </el-drawer>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import { useBatchCropStore } from '@/pinia/batchCrop'
import ChangeBackground from './ChangeBackground.vue'
import ChangeCustom from './ChangeCustom.vue'
import ChangeSize from './ChangeSize.vue'
const batchStore = useBatchCropStore()
const drawer = ref(true)
batchStore.getListAll()

const props = defineProps({
  currentStore: {
    type: Object,
    default: {},
  } as any,
})
// 监听功能选择变化
watch(props.currentStore.currentSegment, (newValue) => {
  console.log('当前选择的功能：', newValue)
  // 根据选择的功能执行相应操作
  switch (newValue) {
    case '手动精修':
      // 手动精修功能的处理逻辑
      // messageSimple.info('手动精修功能开发中')
      break
    case '换背景':
      // 换背景功能的处理逻辑
      // messageSimple.info('换背景功能开发中')
      break
    case '改尺寸':
      // 改尺寸功能的处理逻辑
      break
  }
})
</script>

<style lang="scss">
.crop-drawer {
  left: auto;
  top: 48px;
  width: 289px;
  background: transparent !important;
  z-index: 1 !important;

  .el-drawer {
    width: 289px !important;
    height: calc(100vh - 48px);
    box-shadow: none;
    .el-drawer__body {
      padding: 19px 20px 50px;
      overflow: hidden;
    }
  }
}
</style>
<style lang="scss" scoped>
.crop-drawer {
  padding-bottom: 76px;

  section {
    height: 100%;
  }

  .segmented-container {
    margin-bottom: 12px;
    padding: 0 1px;
    background: #f5f5f5 !important;
    border-radius: 4px;
    overflow: hidden;
    :deep(.el-segmented) {
      padding: 0;
      background: #f5f5f5 !important;
      // Override Element Plus segmented control variables
      --el-segmented-item-selected-bg-color: #ffffff;

      .el-segmented__item {
        width: 83px;
        height: 32px;
        background: #f5f5f5 !important;
        user-select: none;
        .el-segmented__item-label {
          line-height: 20px;
          font-size: 14px;
          font-weight: 400;
          color: #666666;
        }
      }

      .is-selected {
        border-radius: 4px;

        .el-segmented__item-label {
          font-weight: 500;
          color: #000000;
        }
      }
      .el-segmented__item-selected {
        // outline: 1px solid #dddddd;
        border: 1px solid #dddddd;
        border-radius: 4px;
        box-sizing: border-box;
      }
    }
  }

  .function-container {
    // height: calc(100% - 50px);
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    // align-items: center;

    .function-placeholder {
      text-align: center;

      img {
        width: 80px;
        height: 80px;
        margin-bottom: 16px;
      }

      p {
        font-size: 14px;
        color: #999999;
      }
    }
  }
}
</style>
