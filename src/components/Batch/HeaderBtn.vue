<template>
  <div class="picture-header flex">
    <div class="picture-btns flex flex1">
      <el-button
        class="btn add-pic"
        type="primary"
        @click="(e: any) => isElectron ? electronUpload(e, props.currentStore.imageInfoHandler) : webUpload(e)"
      >
        <img src="/img/pic/index/<EMAIL>" />
        <span>添加图片</span>
      </el-button>
      <el-button
        v-if="isElectron"
        class="btn add-folder"
        @click="(e: any) => isElectron ? electronUploadFolder(e, props.currentStore.imageInfoHandler) : webUploadFolder(e)"
        ><img
          class="templateIcon"
          src="/img/pic/index/<EMAIL>"
        />添加文件夹
      </el-button>
      <el-checkbox
        v-if="isRename"
        v-model="props.currentStore.checkAll"
        @change="checkAllChange"
      >
        <span>全选</span>
      </el-checkbox>
    </div>
    <div class="picture-btns flex">
      <div v-if="!isClassify" class="pdf-tip">*按住图片拖动，可调整位置</div>
      <el-button class="btn remove" @click="clearList"
        ><img
          class="templateIcon"
          src="/img/pic/index/<EMAIL>"
        />清空列表
      </el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { watch } from 'vue'
import { useRoute } from 'vue-router'
// import { storeToRefs } from 'pinia'
import { useCommonStore } from '@/pinia/common'
import { pageType as pageTypeEnum } from '@/utils/enum'
// import { messageSimple } from '@/utils/message'
const { proxy } = getCurrentInstance()
const commonStore = useCommonStore()
// let { imgsMap, pageType } = storeToRefs(commonStore)
const isElectron = window.isElectron

const props = defineProps({
  imgExts: {
    type: Array,
    default: [],
  },
  imgsMap: {
    type: Object,
    default: {},
  },
  currentStore: {
    type: Object,
    default: {},
  },
})

const route = useRoute()
const isClassify = route.name === pageTypeEnum.classify
const isRename = route.name === pageTypeEnum.rename
if (isRename) {
  // 监听imgsMap中每个图片的checked变化，以更新checkAll的值
  watch(
    () => Array.from(props.imgsMap.values()).map((item: any) => item.checked),
    (newValues) => {
      props.currentStore.checkAll = newValues.every((checked) => checked)
    },
    { deep: true, immediate: true }
  )
}

const electronUpload = commonStore.electronUpload
const electronUploadFolder = commonStore.electronUploadFolder
const webUpload = commonStore.webUpload
const webUploadFolder = commonStore.webUploadFolder

const checkAllChange = (e: any) => {
  console.log(e, props.currentStore.checkAll)
  props.imgsMap.forEach((item: any) => {
    item.checked = props.currentStore.checkAll
  })
}

const clearList = () => {
  // if (commonStore.hasLoading()) return messageSimple.warning('当前页面图片处理中，请稍后')

  proxy?.$messageBox
    .confirm('只清空列表记录，已处理好的文件不会删除', '确定清空列表？', {
      distinguishCancelAndClose: true,
      confirmButtonText: '确定', // 保留
      cancelButtonText: '取消', // 不保留
      customClass: 'customStyleByMessageBox',
      type: 'warning',
      // center: true
    })
    .then((e: any) => {
      if (e === 'confirm') {
        props.imgsMap.clear()
      }
    })
    .catch((e: any) => {})
}
</script>

<style lang="scss" scoped>
@import '@/assets/css/picture-btns.scss';
.picture-header {
  min-width: 901px;

  .picture-btns {
    .el-checkbox {
      margin-left: 10px;
    }
    .pdf-tip {
      margin-right: 20px;
    }
  }
}
</style>
