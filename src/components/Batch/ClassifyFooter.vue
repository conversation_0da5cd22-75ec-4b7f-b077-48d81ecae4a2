<template>
  <div v-if="isElectron || props.imgsMap.size" class="classify-footer flex">
    <div class="flex1">
      <div v-show="props.imgsMap.size" class="convert flex">
        <span class="tip type-tip">输出方式</span>
        <el-radio-group class="type" v-model="props.currentStore.type">
          <el-radio
            v-for="comKey in Object.keys(props.currentStore.typeEnum)"
            :key="comKey"
            :label="comKey"
          ></el-radio>
        </el-radio-group>
        <span class="tip operation-tip">操作</span>
        <el-radio-group
          class="operation"
          v-model="props.currentStore.operation"
        >
          <el-radio
            v-for="comKey in Object.keys(props.currentStore.operationEnum)"
            :key="comKey"
            :label="comKey"
          ></el-radio>
        </el-radio-group>
      </div>
      <!-- common 导出目录 -->
      <ExportDirectory></ExportDirectory>
    </div>
    <button
      v-show="props.imgsMap.size"
      class="start-convert btn"
      :class="{
        'start-convert-web': !isElectron,
      }"
      @click="beforeConvert(props.imgsMap)"
    >
      <div>{{ props.currentStore?.tip || '全部处理' }}</div>
    </button>
  </div>
</template>

<script lang="ts" setup>
const props = defineProps({
  imgsMap: {
    type: Object,
    default: {},
  },
  currentStore: {
    type: Object,
    default: {},
  },
})

const beforeConvert = props.currentStore.beforeConvert
const isElectron = window.isElectron
</script>

<style lang="scss" scoped>
@import '../Common/compress-model.scss';
.classify-footer {
  position: absolute;
  bottom: 0;
  background-color: #fff;
  width: 100%;
  padding: 13.5px 20px 16px;
  border-top: 1px solid #e0e0e0;

  .convert {
    height: 32px;
    padding: 2px 0;
    margin-bottom: 10px;

    .tip {
      font-size: 14px;
      color: #999999;
      text-align: left;
      position: relative;
      top: 1px;
    }
    .type-tip {
      margin: 0 8px 0 0;
    }
    .operation-tip {
      margin: 0 8px 0 64px;
    }

    :deep(.el-radio-group) {
      .el-radio {
        margin-left: 4px;
        margin-right: 12px;
        .el-radio__label {
          font-size: 14px;
          color: rgba(0, 0, 0, 0.65)!important;
          line-height: 22px;
        }
      }
    }
  }

  .dir {
    width: 683px;
    height: 28px;

    .tip {
      margin-right: 8px;
      line-height: 28px;
      font-size: 14px;
      color: #999999;
      text-align: left;
    }

    input {
      width: 503px;
      height: 28px;
      border-radius: 2px;
      padding: 0 8px;
      border: 1px solid #e0e0e0;

      font-size: 14px;
      color: #333333;
    }

    .btn {
      width: 46px;
      height: 28px;
      margin-left: 8px;
      border: 1px solid #e0e0e0;
      border-radius: 4px;
      cursor: pointer;

      font-size: 14px;
      color: #333333;
      line-height: 26px;

      &:hover {
        opacity: 0.7;
      }
    }
  }

  .start-convert {
    position: absolute;
    right: 20px;
    bottom: 25px;

    width: 120px;
    height: 49px;
    line-height: 49px;
    border-radius: 4px;
    background: #389bfd;
    color: #fff;
    font-size: 18px;
    border-radius: 4px;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    user-select: none;
    z-index: 1;
    cursor: pointer;

    &:hover {
      opacity: 0.7;
    }
  }

  .start-convert-web {
    bottom: 11px;
    width: 112px;

    height: 38px;
    margin: 0;
    font-size: 16px;

    .el-icon {
      margin-right: 3px;
      animation: rotating 2s linear infinite;
    }
  }
}
</style>
