<template>
  <div v-if="isElectron || props.imgsMap.size" class="rename-footer flex">
    <div class="flex1">
      <div v-show="props.imgsMap.size" class="convert flex">
        <span class="tip first-tip">名称前缀</span>
        <el-input
          class="picture-name"
          v-model="props.currentStore.prefix"
          placeholder="输入搜索关键词"
          :maxlength="20"
          clearable
        />
        <span class="tip">起始序号</span>
        <el-input-number
          class="start-number"
          v-model="props.currentStore.startNum"
          :min="1"
          :max="999"
          :controls="true"
          controls-position="right"
        ></el-input-number>
        <span class="tip" style="margin-left: 52px;">间隔数</span>
        <el-input-number
          class="gap-number"
          v-model="props.currentStore.gap"
          :min="1"
          :max="999"
          :controls="true"
          controls-position="right"
        ></el-input-number>
        <div class="reset btn flex" @click="reset">
          <img src="/img/pic/second2/<EMAIL>" alt="" class="icon" />
          <span>重置</span>
        </div>
      </div>
      <!-- common 导出目录 -->
      <ExportDirectory></ExportDirectory>
    </div>
    <button
      v-show="props.imgsMap.size"
      class="start-convert btn"
      :class="{
        'start-convert-web': !isElectron,
      }"
      @click="beforeConvert(props.imgsMap)"
    >
      <div>{{ props.currentStore?.tip || '全部处理' }}</div>
    </button>
  </div>
</template>

<script lang="ts" setup>
const props = defineProps({
  imgsMap: {
    type: Object,
    default: {},
  },
  currentStore: {
    type: Object,
    default: {},
  },
})

const beforeConvert = props.currentStore.beforeConvert
const isElectron = window.isElectron

const reset = () => {
  props.currentStore.prefix = '图片名称'
  props.currentStore.startNum = 1
  props.currentStore.gap = 1
}
</script>

<style lang="scss" scoped>
@import '../Common/compress-model.scss';
.rename-footer {
  position: absolute;
  bottom: 0;
  background-color: #fff;
  width: 100%;
  padding: 13.5px 20px 16px;
  border-top: 1px solid #e0e0e0;

  .convert {
    height: 32px;
    padding: 2px 0;
    margin-bottom: 10px;

    .tip {
      line-height: 28px;
      font-size: 14px;
      color: #999999;
      text-align: left;
      margin: 0 8px 0 32px;
    }
    .first-tip {
      margin-left: 0;
    }

    .picture-name {
      width: 180px;
      height: 28px;
      border-radius: 2px;
    }

    // 重置number选择样式
    :deep(.start-number), :deep(.gap-number) {
      width: 68px;
      height: 28px;
      border-radius: 2px;
      border: 1px solid #E0E0E0;

      .el-input-number__increase,
      .el-input-number__decrease {
        right: -21px;
        width: 16px;
        height: 14.5px;
        border-radius: 2px;
        border: 1px solid #E0E0E0;
        background-color: #fff;
      }
      .el-input-number__increase {
        top: -1px;
        border-bottom: 0;
        border-bottom-left-radius: 0;
        border-bottom-right-radius: 0;

        .el-icon {
          top: 1px;
          width: 0px;
          height: 0px;
          border-left: 4px solid transparent; /* left arrow slant */
          border-right: 4px solid transparent; /* right arrow slant */
          border-bottom: 5px solid #999999; /* bottom, add background color here */
          font-size: 0px;
          line-height: 0px;
          border-bottom-left-radius: 2px;
          border-bottom-right-radius: 2px;
        }

        &:hover {
          opacity: 0.7;
        }
      }
      .el-input-number__decrease {
        bottom: -1px;
        border-top: 0;
        border-top-left-radius: 0;
        border-top-right-radius: 0;

        .el-icon {
          bottom: 1px;
          width: 0px;
          height: 0px;
          border-left: 4px solid transparent;
          border-right: 4px solid transparent;
          border-top: 5px solid #999999;
          font-size: 0px;
          line-height: 0px;
          border-top-left-radius: 2px;
          border-top-right-radius: 2px;
        }

        &:hover {
          opacity: 0.7;
        }
      }
      .is-disabled {
        .el-icon {
          border-top-color: #e0e0e0;
        }
      }
      .el-input  {
        .el-input__wrapper {
          padding: 0;
          box-shadow: none;
        }
      }
    }

    .reset {
      font-weight: 400;
      font-size: 14px;
      color: #333333;
      margin: 4px 0 0 52px;
      cursor: pointer;

      .icon {
        width: 20px;
        height: 20px;
      }

      span {
        line-height: 20px;
      }
    }
  }

  .dir {
    width: 683px;
    height: 28px;

    .tip {
      margin-right: 8px;
      line-height: 28px;
      font-size: 14px;
      color: #999999;
      text-align: left;
    }

    input {
      width: 503px;
      height: 28px;
      border-radius: 2px;
      padding: 0 8px;
      border: 1px solid #e0e0e0;

      font-size: 14px;
      color: #333333;
    }

    .btn {
      width: 46px;
      height: 28px;
      margin-left: 8px;
      border: 1px solid #e0e0e0;
      border-radius: 4px;
      cursor: pointer;

      font-size: 14px;
      color: #333333;
      line-height: 26px;

      &:hover {
        opacity: 0.7;
      }
    }
  }

  .start-convert {
    position: absolute;
    right: 20px;
    bottom: 25px;

    width: 120px;
    height: 49px;
    line-height: 49px;
    border-radius: 4px;
    background: #389bfd;
    color: #fff;
    font-size: 18px;
    border-radius: 4px;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    user-select: none;
    z-index: 1;
    cursor: pointer;

    &:hover {
      opacity: 0.7;
    }
  }

  .start-convert-web {
    bottom: 11px;
    width: 112px;

    height: 38px;
    margin: 0;
    font-size: 16px;

    .el-icon {
      margin-right: 3px;
      animation: rotating 2s linear infinite;
    }
  }
}
</style>
