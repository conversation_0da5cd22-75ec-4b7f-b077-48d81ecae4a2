<template>
  <div class="content">
    <!-- 图片相关信息 -->
    <div class="item-content flex">
      <div v-if="props.imgMap[1].record.status === 'fail'" class="fail"></div>
      <div
        class="img-wrap chessboard-bg"
        :class="{
          'img-wrap-web': !isElectron,
        }"
      >
        <img
          :src="
            props.imgMap[1].record.imgUrl ||
            '/img/pic/index/<EMAIL>'
          "
        />
        <div
          v-if="isElectron"
          class="btn open-img"
          @click="electronOpenFile($event, props.imgMap[1].originFile)"
        >
          打开
        </div>
      </div>
      <div class="origin-info flex flex1">
        <div class="flex1">
          <p class="name flex">
            <span
              class="dotdotdot1"
              style="width: 200px"
              :class="{ 'show-tooltip': isOverflow }"
              @mouseover="checkOverflow"
              @mouseleave="isOverflow = false"
              :data-fullname="props.imgMap[1].filename"
              >{{ props.imgMap[1].filename }}</span
            >
          </p>
          <p>
            <span>格式</span>
            <span>{{ props.imgMap[1].record.originFormat }}</span>
          </p>
          <p>
            <span>分辨率</span>
            <span>{{ props.imgMap[1].record.originPixel }}</span>
          </p>
          <p v-if="props.imgMap[1].record.birthtime">
            <span>拍摄日期</span>
            <span>{{ props.imgMap[1].record.birthtime }}</span>
          </p>
        </div>
      </div>
      <div
        v-if="!props.imgMap[1].record.loading"
        class="delete btn"
        @click="deleteImg(props.imgMap[1].key)"
      ></div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { useCommonStore } from '@/pinia/common'
import { useBatchClassifyStore } from '@/pinia/batchClassify'

const isElectron = window.isElectron
const batchStore = useBatchClassifyStore()
const commonStore = useCommonStore()

const props = defineProps({
  index: {
    type: Number,
    default: 1,
  },
  imgMap: {
    type: Object,
    default: {},
  },
})

const deleteImg = batchStore.deleteImg
// 显示toolTip
const isOverflow = ref(false)
const checkOverflow = (event: any) => {
  const span = event.target
  isOverflow.value = span.scrollWidth > span.clientWidth
}

// electron
const electronOpenFile = commonStore.electronOpenFile
</script>

<style lang="scss" scoped>
.content {
  position: relative;

  .item-content {
    height: 144px;
    padding: 12px 0 10px;

    .img-wrap {
      position: relative;
      margin: 0 16px 0 10px;
      // background-color: #ededed;
      // @include colorBtn(#000);
      width: 120px;
      height: 120px;
      border-radius: 6px;
      background-color: transparent;

      img {
        position: relative;
        width: 100%;
        height: 100%;
        object-fit: contain;
      }

      .open-img {
        position: absolute;
        top: 47.5px;
        left: 36px;
        visibility: hidden;

        width: 48px;
        height: 25px;
        background: #389bfd;
        border-radius: 6px;

        font-weight: normal;
        font-size: 12px;
        color: #ffffff;
        line-height: 25px;
        cursor: pointer;
      }

      &:hover {
        &::after {
          position: absolute;
          content: '';
          left: 0;
          top: 0;
          width: 100%;
          height: 100%;
          border-radius: 6px;
          background: #000000;
          opacity: 0.3;
          z-index: 1;
        }
        .open-img {
          visibility: visible;
          z-index: 2;
        }
      }
    }

    .img-wrap-web {
      &:hover {
        &::after {
          opacity: 0;
        }
      }
    }

    p {
      line-height: 20px;
    }

    .origin-info {
      position: relative;
      padding-top: 8px;
      margin-bottom: -12px;
      border-bottom: 1px solid #e0e0e0;

      // hover显示全部名称
      .name {
        .dotdotdot1::after {
          content: attr(data-fullname);
          position: absolute;
          white-space: nowrap;
          top: 0;
          left: 0;
          visibility: hidden;
          opacity: 0;
          background-color: #fbfbfb;
          transition: opacity 0.3s, visibility 0.3s;
          z-index: 1;

          padding: 2px 8px;
          top: 25px;
          // left: -7px;
          @include common-dialog; // #e3e3e3

          // top: -28px;
          // background: #f0f0f0;
          // box-shadow: 0px 1px 4px 0px rgba(194, 194, 194, 0.62);
          // border-radius: 1px;
          // border: 0px solid #c3c3c3;
        }

        .show-tooltip:hover::after {
          visibility: visible;
          opacity: 1;
        }
      }

      p {
        line-height: 17px;
        margin-bottom: 4px;
        font-size: 12px;
        color: #999;

        span:first-child {
          float: left;
          width: 60px;
          color: #999;
        }
        span:last-child {
          clear: both;
          color: #666;
        }
      }

      .name {
        line-height: 20px;
        margin-bottom: 8px;
        font-size: 14px;
        color: #333333;

        span:first-child {
          color: #333333;
        }
      }
    }

    .delete {
      display: none;
      position: absolute;
      right: 12px;
      top: 12px;
      width: 16px;
      height: 16px;
      cursor: pointer;
      background-image: url('/img/pic/second2/<EMAIL>');
      background-size: contain;
    }

    &:hover {
      background-color: #fbfbfb;
      .delete {
        display: block;
      }
    }
  }
}
</style>
