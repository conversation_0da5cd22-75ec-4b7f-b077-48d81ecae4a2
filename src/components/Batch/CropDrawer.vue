<template>
  <el-drawer
    modal-class="crop-drawer"
    v-model="drawer"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :with-header="false"
  >
    <ChangeSize :showApplyAll="true" />
  </el-drawer>
</template>

<script setup lang="ts">
import { ref } from 'vue'
const drawer = ref(true)
</script>

<style lang="scss">
.crop-drawer {
  left: auto;
  top: 48px;
  width: 289px;
  padding-bottom: 76px;
  background: transparent !important;
  z-index: 1 !important;

  .el-drawer {
    width: 289px !important;
    height: calc(100vh - 48px);
    box-shadow: none;
    .el-drawer__body {
      padding: 19px 20px 50px;
      overflow: hidden;
    }
  }
}
</style>
