<template>
  <el-dialog
    class="export-directory-dialog"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    v-model="showExportDirectoryDialog"
  >
    <p class="tip">导出目录</p>
    <div class="dir flex">
      <input v-model="filePath" type="text" readonly />
    </div>
    <div class="btns flex">
      <el-button class="btn" @click="electronGetFolderPath">更改</el-button>
      <el-button class="btn open" @click="electronOpenFolder">打开</el-button>
    </div>
  </el-dialog>
</template>

<script lang="ts" setup>
import { storeToRefs } from 'pinia'
import { useCommonStore } from '@/pinia/common'
const commonStore = useCommonStore()
let { showExportDirectoryDialog, filePath } = storeToRefs(commonStore)

// filePath.value = '/test'

// 选择输出文件夹路径
const electronGetFolderPath = (e: any) => {
  window.ipcRendererApi?.invoke('select-folder-path').then((res: any) => {
    if (res) {
      const lastCharacter = res.endsWith('/') ? '' : '/'
      localStorage.filePath = filePath.value = res + lastCharacter
      // pictureStore.filePath = filePath.value
    }
  })
}
// 打开文件夹
const electronOpenFolder = (e: any) => commonStore.electronOpenFolder(e)
</script>

<style lang="scss">
.export-directory-dialog {
  width: 401px;
  height: 184px;
  margin: calc(50vh - 92px) auto;
  padding-left: 24px;

  .el-dialog__headerbtn {
    width: 34px;
    height: 38px;
    .el-dialog__close {
      width: 20px;
      height: 20px;
      svg {
        width: 20px;
        height: 20px;
      }
    }
  }
}
</style>
<style lang="scss" scoped>
.export-directory-dialog {
  .tip {
    font-weight: 600; // electron
    font-size: 16px;
    color: rgba(0, 0, 0, 0.85);
    line-height: 24px;
    margin-bottom: 12px;
    user-select: none;
  }

  .dir {
    input {
      margin-bottom: 24px;
      width: 345px;
      height: 32px;
      background: #f7f8fa;
      border-radius: 2px;
      padding: 0 8px;
      border: 1px solid #e0e0e0;

      font-weight: 400;
      font-size: 14px;
      color: #333333;
      line-height: 20px;
    }
  }

  .btns {
    justify-content: right;
    .btn {
      width: 65px;
      height: 32px;
      background: #ffffff;
      border-radius: 2px;
      border: 1px solid rgba(0, 0, 0, 0.15);
      margin-left: 12px;
      cursor: pointer;

      font-weight: 400;
      font-size: 14px;
      color: rgba(0, 0, 0, 0.65);
      line-height: 22px;

      &:hover {
        opacity: 0.7;
      }
    }
    .open {
      margin-right: 14px;
      color: #fff;
      background: #389bfd;
    }
  }
}
</style>
