<template>
  <div
    v-if="isElectron"
    class="export-directory dir flex"
    :style="{
      marginTop: props.imgsMap?.size || imgsMap.size ? '10px' : '0',
    }"
  >
    <span class="tip">导出目录</span>
    <input v-model="filePath" type="text" readonly />
    <span class="btn" @click="electronGetFolderPath">更改</span>
    <span class="btn" @click="electronOpenFolder">打开</span>

    <el-checkbox
      v-if="isPdf && (props.imgsMap?.size || imgsMap.size)"
      v-model="checkAll"
      @change="checkAllChange"
    >
      <span>全选</span>
    </el-checkbox>
  </div>
  <el-checkbox v-else-if="isPdf" v-model="checkAll" @change="checkAllChange">
    <span>全选</span>
  </el-checkbox>
</template>

<script lang="ts" setup>
import { watch } from 'vue'
import { storeToRefs } from 'pinia'
import { usePictureStore } from '@/pinia/picture'
import { usePdfStore } from '@/pinia/pdf'
import { useCommonStore } from '@/pinia/common'
import { useRoute } from 'vue-router'
import { pageType as pageTypeEnum } from '@/utils/enum'
const pictureStore = usePictureStore()
const pdfStore = usePdfStore()
const commonStore = useCommonStore()
let { imgsMap } = storeToRefs(pictureStore)
let { filePath } = storeToRefs(commonStore)
let { checkAll } = storeToRefs(pdfStore)
const isElectron = window.isElectron

const route = useRoute()
const isPdf = route.name === pageTypeEnum.pdf

const props = defineProps({
  imgsMap: {
    type: Object,
    default: null,
  },
  currentStore: {
    type: Object,
    default: {},
  },
})

const checkAllChange = (e: any) => {
  console.log(e)
  ;(props.imgsMap || imgsMap.value).forEach((item: any) => {
    item.checked = checkAll.value
  })
}

if (isPdf) {
  // 监听imgsMap中每个图片的checked变化，以更新checkAll的值
  watch(
    () =>
      Array.from((props.imgsMap || imgsMap.value).values()).map(
        (item: any) => item.checked
      ),
    (newValues) => {
      checkAll.value = newValues.every((checked) => checked)
    },
    { deep: true, immediate: true }
  )
}

// 选择输出文件夹路径
const electronGetFolderPath = (e: any) => {
  window.ipcRendererApi?.invoke('select-folder-path').then((res: any) => {
    if (res) {
      const lastCharacter = res.endsWith('/') ? '' : '/'
      localStorage.filePath = filePath.value = res + lastCharacter
      // pictureStore.filePath = filePath.value
    }
  })
}
// 打开文件夹
const electronOpenFolder = (e: any) => commonStore.electronOpenFolder(e)
</script>

<style lang="scss" scoped>
.export-directory {
  width: 683px;
  height: 28px;

  .el-checkbox {
    margin-left: 50px;
  }

  .tip {
    margin-right: 8px;
    line-height: 28px;
    font-size: 14px;
    color: #999999;
    text-align: left;
  }

  input {
    width: 503px;
    height: 28px;
    border-radius: 2px;
    padding: 0 8px;
    border: 1px solid #e0e0e0;

    font-size: 14px;
    color: #333333;
  }

  .btn {
    width: 46px;
    height: 28px;
    margin-left: 8px;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    cursor: pointer;

    font-size: 14px;
    color: #333333;
    line-height: 26px;

    &:hover {
      opacity: 0.7;
    }
  }
}
</style>
