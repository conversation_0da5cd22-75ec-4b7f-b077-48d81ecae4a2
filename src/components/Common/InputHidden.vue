<template>
  <div v-if="props.keepShow || !isElectron">
    <input
      class="hide"
      type="file"
      name="file"
      :id="props.id"
      :accept="props.accept"
      :multiple="props.multiple"
      @change="chooseWebImages($event)"
    />
    <input
      class="hide"
      type="file"
      name="file"
      :id="`${props.id}Folder`"
      :accept="props.accept"
      @change="chooseWebImages($event)"
      webkitdirectory
      directory
    />
  </div>
</template>

<script lang="ts" setup>
import { usePictureStore } from '@/pinia/picture'
const isElectron = window.isElectron
const props = defineProps({
  id: {
    type: String,
    default: 'imgUploadInput',
  },
  keepShow: {
    type: Boolean,
    default: false,
  },
  accept: {
    type: String,
    default: 'image/*, .pdf',
  },
  multiple: {
    type: Boolean,
    default: true,
  },
  chooseWebImages: {
    type: Function,
    default: null,
  },
})
let chooseWebImages = props.chooseWebImages
if (!chooseWebImages) {
  const pictureStore = usePictureStore()
  chooseWebImages = pictureStore.chooseWebImages
}
</script>
