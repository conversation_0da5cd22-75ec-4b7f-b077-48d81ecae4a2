<template>
  <div class="footer-tool">
    <!-- 图片编辑工具栏 -->
    <div class="image-toolbar">
      <!-- <div class="toolbar-item" @click="resetImage">
        <img src="/img/pic/second2/<EMAIL>" alt="" />
        <span>滤镜</span>
      </div>
      <div class="toolbar-item" @click="resetImage">
        <img src="/img/pic/second2/<EMAIL>" alt="" />
        <span>蒙板</span>
      </div> -->

      <!-- 缩放 -->
      <el-popover
        popper-class="image-tool-popover"
        placement="top"
        :width="300"
        trigger="click"
        :show-arrow="false"
      >
        <template #reference>
          <div class="toolbar-item">
            <img src="/img/pic/second2/<EMAIL>" alt="" />
            <span>缩放</span>
          </div>
        </template>
        <template #default>
          <div class="slider-action zoom flex">
            <div class="tip">缩放</div>
            <el-slider
              :model-value="currentStore.currentImg.record.scaleX || currentStore.currentImg.record.zoom"
              @update:model-value="updateScale"
              :min="0.1"
              :max="2"
              :step="0.01"
              :show-tooltip="true"
              :format-tooltip="(value: number) => `${Math.round(value * 100)}%`"
            />
            <div class="size">
              {{ Math.round(currentStore.currentImg.record.zoom * 100) }}%
            </div>
          </div>
        </template>
      </el-popover>

      <!-- 旋转 -->
      <el-popover
        popper-class="image-tool-popover"
        placement="top"
        :width="300"
        trigger="click"
        :show-arrow="false"
      >
        <template #reference>
          <div class="toolbar-item">
            <img src="/img/pic/second2/<EMAIL>" alt="" />
            <span>旋转</span>
          </div>
        </template>
        <template #default>
          <div class="slider-action rotate flex">
            <div class="tip">旋转</div>
            <el-slider
              v-model="currentStore.currentImg.record.rotate"
              @change="onRotateChange"
              :min="0"
              :max="360"
              :show-tooltip="true"
              :format-tooltip="(value: number) => `${value}°`"
            />
            <div class="size">{{ currentStore.currentImg.record.rotate }}°</div>
          </div>
        </template>
      </el-popover>

      <!-- 透明度 -->
      <el-popover
        popper-class="image-tool-popover"
        placement="top"
        :width="300"
        trigger="click"
        :show-arrow="false"
      >
        <template #reference>
          <div class="toolbar-item">
            <img src="/img/pic/second2/<EMAIL>" alt="" />
            <span>透明度</span>
          </div>
        </template>
        <template #default>
          <div class="slider-action opacity flex">
            <div class="tip">透明度</div>
            <el-slider
              v-model="currentStore.currentImg.record.opacity"
              :min="0"
              :max="1"
              :step="0.01"
              :show-tooltip="true"
              :format-tooltip="(value: number) => `${Math.round(value * 100)}%`"
            />
            <div class="size">
              {{ Math.round(currentStore.currentImg.record.opacity * 100) }}%
            </div>
          </div>
        </template>
      </el-popover>

      <!-- 翻转 -->
      <el-popover
        popper-class="image-tool-popover"
        placement="top"
        :width="300"
        trigger="click"
        :show-arrow="false"
      >
        <template #reference>
          <div class="toolbar-item">
            <img src="/img/pic/second2/<EMAIL>" alt="" />
            <span>翻转</span>
          </div>
        </template>
        <template #default>
          <div class="action btns flex">
            <div class="item btn flex" @click="flipHorizontal">
              <div class="tip">上下翻转</div>
            </div>
            <div class="line"></div>
            <div class="item btn flex" @click="flipVertical">
              <div class="tip">左右翻转</div>
            </div>
          </div>
        </template>
      </el-popover>
      <div class="toolbar-item" @click="resetImage">
        <!-- <img src="/img/pic/second2/<EMAIL>" alt="" /> -->
        <span>重置</span>
      </div>
      <!-- <div class="toolbar-item" @click="resetImage">
        <img src="/img/pic/second2/<EMAIL>" alt="" />
      </div> -->
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRoute } from 'vue-router'
import { useMattingStore } from '@/pinia/matting'
import { usePassportStore } from '@/pinia/passport'
import { pageType as pageTypeEnum } from '@/utils/enum'

const route = useRoute()
// 获取当前应该使用的 store
const getStore = () => {
  switch (route.name) {
    case pageTypeEnum.matting:
      return useMattingStore()
    case pageTypeEnum.passport:
      return usePassportStore()
    default:
      return useMattingStore()
  }
}
const currentStore = getStore()

// 缩放更新函数
const updateScale = (value: number) => {
  // 更新 scaleX 和 scaleY 值
  currentStore.currentImg.record.scaleX = value
  currentStore.currentImg.record.scaleY = value
  // 兼容旧代码
  currentStore.currentImg.record.zoom = value
}

// 旋转更新函数
const onRotateChange = (value: number) => {
  // 更新旋转值
  currentStore.currentImg.record.rotate = value
}

// 翻转相关函数
const flipHorizontal = () => {
  currentStore.currentImg.record.transformY =
    -currentStore.currentImg.record.transformY
}

const flipVertical = () => {
  currentStore.currentImg.record.transformX =
    -currentStore.currentImg.record.transformX
}

// 重置图片
const resetImage = () => {
  // 重置所有变换值
  currentStore.currentImg.record.opacity = 1
  currentStore.currentImg.record.rotate = 0
  currentStore.currentImg.record.zoom = 1
  currentStore.currentImg.record.scaleX = 1
  currentStore.currentImg.record.scaleY = 1
  currentStore.currentImg.record.transformX = 1
  currentStore.currentImg.record.transformY = 1

  // 重置位移
  if (currentStore.currentImg.record.translate) {
    currentStore.currentImg.record.translate.x = 0
    currentStore.currentImg.record.translate.y = 0
  } else {
    currentStore.currentImg.record.translate = { x: 0, y: 0 }
  }

  // 更新元素样式
  const mattingImage = document.querySelector('.matting-image') as HTMLElement
  if (mattingImage) {
    mattingImage.style.transform = 'translate(0px, 0px) rotate(0deg) scale(1, 1) scaleX(1) scaleY(1)'
  }
}
</script>

<style lang="scss">
// 弹出框内容样式
.image-tool-popover {
  height: 40px;
  width: auto !important;
  padding: 0 !important;
  border: 0 !important;
  border-radius: 20px !important;
  margin-bottom: 9px;
  background: #ffffff;
  box-shadow: 0px 2px 8px 0px rgba(180, 180, 180, 0.5);

  font-weight: 400;
  font-size: 14px;
  color: #333333;
  line-height: 20px;

  .el-slider__runway {
    height: 3px;
    border-radius: 3px;
    background-color: #e4e4e6;

    .el-slider__bar {
      height: 3px !important;
      border-radius: 3px;
    }

    .el-slider__button {
      position: relative;
      width: 9px;
      height: 9px;
      top: -1.5px;
      border: #1890ff;
      background: #1890ff;
    }
  }

  .slider-action {
    min-width: 250px;
    height: 20px;
    margin: 10px 20px;
    user-select: none;
    display: flex;
    align-items: center;

    .tip {
      width: 42px;
      margin-right: 12px;
      font-weight: 400;
      font-size: 14px;
      color: #333;
      line-height: 20px;
      flex-shrink: 0;
    }

    .size {
      width: 33px;
      margin-left: 12px;
      font-weight: 400;
      font-size: 12px;
      color: #333;
      line-height: 17px;
      text-align: left;
      flex-shrink: 0;
    }

    .el-slider {
      flex: 1;
    }
  }

  .action {
    margin: 0 20px;
    height: 100%;
    justify-content: center;
    align-items: center;
    color: #333333 !important;

    .item {
      line-height: 20px;
    }

    .line {
      margin: 0 11px;
      width: 1px !important;
      height: 16px !important;
      background-color: #ccc;
    }
  }
}
</style>
<style lang="scss" scoped>
.footer-tool {
  position: absolute;
  bottom: -85px;
  user-select: none;

  // 图片编辑工具栏
  .image-toolbar {
    display: flex;
    padding: 10px 20px;
    height: 40px;
    border-radius: 20px;
    background-color: #bfbfbf;
    z-index: 100;

    .toolbar-item {
      display: flex;
      align-items: center;
      margin-right: 20px;
      font-weight: 400;
      font-size: 14px;
      line-height: 20px;
      color: #ffffff;
      cursor: pointer;

      &:last-child {
        margin-right: 0;
      }
      &:hover {
        opacity: 0.7;
      }

      img {
        width: 20px;
        height: 20px;
        margin-right: 4px;
      }
    }
  }
}
</style>
