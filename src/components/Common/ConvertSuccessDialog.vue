<template>
  <div class="mask" />
  <div class="pdf-dialog">
    <div class="info flex">
      <img
        class="close"
        @click="showConvertSuccessDialog = false"
        src="/img/mind_map/table/<EMAIL>"
        alt=""
      />
      <div class="list">
        <img src="/img/pic/second/<EMAIL>" alt="" />
        <p>导出成功</p>
        <div v-if="isElectron" class="flex">
          <el-button
            class="btn open-file"
            size="small"
            @click="electronOpenFolder"
          >
            打开文件夹
          </el-button>
          <el-button
            v-if="showOpen"
            class="btn open"
            type="primary"
            size="small"
            @click="electronOpenFile($event, props.url)"
          >
            打开
          </el-button>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { storeToRefs } from 'pinia'
import { useCommonStore } from '@/pinia/common'

const commonStore = useCommonStore()
let { showConvertSuccessDialog } = storeToRefs(commonStore)

const isElectron = window.isElectron

const props = defineProps({
  url: {
    type: String,
    default: '',
  },
  showOpen: {
    type: Boolean,
    default: true,
  },
})

const electronOpenFile = commonStore.electronOpenFile
const electronOpenFolder = commonStore.electronOpenFolder
</script>

<style lang="scss" scoped>
.pdf-dialog {
  position: fixed;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  width: 360px;
  border-radius: 4px;
  z-index: 2001;

  .info {
    position: fixed;
    top: 50%;
    left: 50%;
    -webkit-transform: translate(-50%, -50%);
    transform: translate(-50%, -50%);
    text-align: center;

    width: 360px;
    height: 310px;
    padding: 30px 28px 20px;
    border-radius: 4px;
    background: #fff;
    @include common-dialog;

    padding: 42px 28px;

    .close {
      position: absolute;
      right: 14px;
      top: 14px;
      margin-bottom: 25px;
      height: 20px;
      width: 20px;
      cursor: pointer;

      &:hover {
        opacity: 0.7;
      }
    }

    .list {
      width: 360px;

      img {
        margin-top: 20px;
        width: 71px;
        height: 71px;
      }

      p {
        margin: 30px 0;
        font-size: 16px;
        font-weight: 600;
        color: #333333;
      }

      .flex {
        justify-content: center;
        .el-button {
          margin-bottom: 80px;
          width: 145px;
          height: 44px;
          line-height: 28px;
          padding: 0;
          font-size: 20px;
          font-weight: normal;
        }

        .open-file {
          border: 1px solid #e0e0e0;
          color: #333;
          border-color: #e0e0e0;
          background-color: #fff;

          &:hover {
            color: #444;
            opacity: 0.7;
          }
          &:focus {
            color: #444;
            opacity: 1;
          }
        }

        .open {
          border: 1px solid #389bfd;
          color: #fff;
          border-color: #389bfd;
          background-color: #389bfd;

          &:hover {
            opacity: 0.8;
          }
          &:focus {
            opacity: 1;
          }
        }
      }
    }
  }
}
</style>
