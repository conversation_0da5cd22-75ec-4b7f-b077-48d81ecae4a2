<template>
  <div class="ColorBox" :class="moduleType">
    <div class="chooseColor flex">
      <div
        class="currentColor"
        :style="{
          backgroundColor: selectColor,
          border:
            !selectColor || selectColor.toLowerCase().includes('#fff')
              ? '1px solid rgb(236, 236, 236)'
              : 0,
        }"
      >
        <i
          :style="{
            backgroundColor: !selectColor ? '#dadada' : 'transparent',
          }"
        ></i>
      </div>
      <div class="Hex">
        <input
          class="number"
          maxlength="6"
          v-model="selectColorInput"
          @focus="inputFocus"
          @blur="inputBlur"
          :disabled="disabled"
        />
        <p>Hex</p>
      </div>
      <div class="R">
        <input
          class="number"
          maxlength="3"
          v-model="rInput"
          @focus="inputFocus"
          @blur="inputBlur"
          :disabled="disabled || !selectColorInput"
        />
        <p>R</p>
      </div>
      <div class="G">
        <input
          class="number"
          maxlength="3"
          v-model="gInput"
          @focus="inputFocus"
          @blur="inputBlur"
          :disabled="disabled || !selectColorInput"
        />
        <p>G</p>
      </div>
      <div class="B">
        <input
          class="number"
          maxlength="3"
          v-model="bInput"
          @focus="inputFocus"
          @blur="inputBlur"
          :disabled="disabled || !selectColorInput"
        />
        <p>B</p>
      </div>
      <div v-if="hasEyeDrop" class="colorPicker center" @click="nativePick">
        <img
          class="colorPickerImg"
          src="/img/mind_map/btn/<EMAIL>"
          alt=""
        />
      </div>
    </div>

    <div class="line"></div>
    <div class="colorModule">
      <div class="moduleList flex">
        <div
          class="item"
          :class="{
            choose: name === activeModule,
          }"
          v-for="name in Object.keys(colorModule)"
          :key="name"
          @click="activeModule = name"
        >
          {{ name }}
        </div>
      </div>
      <div class="colorList">
        <span
          class="colorItem"
          v-for="item in activeColorList"
          :style="{
            backgroundColor: item,
            border:
              item && item.toLowerCase().includes('#fff')
                ? '1px solid #ECECEC'
                : 0,
          }"
          :key="item"
          @click="clickColorItem(item)"
        ></span>

        <div v-if="activeModule === '更多'" class="moreColor">
          <hex-color-picker :color="selectColor"></hex-color-picker>
        </div>
      </div>
    </div>

    <div
      class="clearColor"
      :style="{
        marginBottom: latestColorList.length ? '12px' : 0,
      }"
    >
      <el-button plain @click="clickColorItem(transparent)">无颜色</el-button>
    </div>

    <div v-if="latestColorList.length" class="line"></div>
    <div v-if="latestColorList.length" class="recently">
      <div class="title">最近使用</div>
      <div class="colorList">
        <span
          class="colorItem"
          v-for="item in latestColorList"
          :style="{
            backgroundColor: item,
            border:
              item && item.toLowerCase().includes('#fff')
                ? '1px solid #ECECEC'
                : 0,
          }"
          :key="item"
          @click="clickColorItem(item)"
        ></span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch, nextTick } from 'vue'
import 'vanilla-colorful'
import { colorModule } from '@/utils/enum/color'

const props = defineProps({
  color: {
    type: String,
    default: '',
  },
  type: {
    // close 或 '' 选择完颜色后，是否关闭颜色面板
    type: String,
    default: '',
  },
  moduleType: {
    // 标志颜色模块类型，各业务自定义
    type: String,
    default: 'noColor',
  },
  disabled: {
    type: Boolean,
    default: false,
  },
})

const transparent = 'transparent'
const latestColorList: any = ref([])
const selectColor = ref('')
const activeModule = ref('常用')
const showR = ref(true)
const showG = ref(true)
const showB = ref(true)

const hasEyeDrop = ref('EyeDropper' in window && !window.isLenovo)

const selectColorInput = computed({
  get() {
    return selectColor.value && selectColor.value.replace('#', '').toUpperCase()
  },
  set(value = '') {
    const color = '#' + value.trim().replace(/[^#0-9a-fA-F]/g, '')
    selectColor.value = color.toUpperCase()
    if ([4, 7].includes(selectColor.value.length)) {
      clickColorItem(color, false)
    } else {
      const temp = selectColor.value
      selectColor.value = ''
      selectColor.value = temp
    }
  },
})

const hexToRgb = computed(() => {
  const defaultRGB = ['', '', '']
  if (selectColor.value === transparent) {
    return defaultRGB
  }
  const val = selectColor.value
  const reg = /^#([0-9a-fA-f]{3}|[0-9a-fA-f]{6})$/
  let color = val.toLowerCase()

  if (reg.test(color)) {
    if (color.length === 4) {
      let colorNew = '#'
      for (let i = 1; i < 4; i += 1) {
        colorNew += color.slice(i, i + 1).concat(color.slice(i, i + 1))
      }
      color = colorNew
    }
    const colorChange: number[] = []
    for (let i = 1; i < 7; i += 2) {
      colorChange.push(parseInt('0x' + color.slice(i, i + 2)))
    }
    return colorChange
  } else {
    return defaultRGB
  }
})

const rInput = computed({
  get() {
    return showR.value ? hexToRgb.value[0] : ''
  },
  set(value: any = '') {
    value = value.trim().replace(/[A-Za-z]/g, '')
    value = value <= 255 ? value : 255
    const rgb = `rgba(${value || 0},${hexToRgb.value[1]},${hexToRgb.value[2]})`
    const color = rgbToHex(rgb).hex
    showR.value = value !== ''
    selectColor.value = color
    clickColorItem(color, false)
  },
})

const gInput = computed({
  get() {
    return showG.value ? hexToRgb.value[1] : ''
  },
  set(value: any = '') {
    value = value.trim().replace(/[A-Za-z]/g, '')
    value = value <= 255 ? value : 255
    const rgb = `rgba(${hexToRgb.value[0]},${value || 0},${hexToRgb.value[2]})`
    const color = rgbToHex(rgb).hex
    showG.value = value !== ''
    selectColor.value = color
    clickColorItem(color, false)
  },
})

const bInput = computed({
  get() {
    return showB.value ? hexToRgb.value[2] : ''
  },
  set(value: any = '') {
    value = value.trim().replace(/[A-Za-z]/g, '')
    value = value <= 255 ? value : 255
    const rgb = `rgba(${hexToRgb.value[0]},${hexToRgb.value[1]},${value || 0})`
    const color = rgbToHex(rgb).hex
    showB.value = value !== ''
    selectColor.value = color
    clickColorItem(color, false)
  },
})

const activeColorList = computed(() => {
  return colorModule[activeModule.value] || []
})

onMounted(() => {
  selectColor.value = expandHexColor(props.color).replace(transparent, '')
  latestColorList.value = JSON.parse(localStorage.latestColorList || '[]')
})

watch(
  () => props.color,
  () => {
    selectColor.value = expandHexColor(props.color).replace(transparent, '')
  }
)

watch(
  () => activeModule.value,
  async (value) => {
    if (value === '更多') {
      await nextTick()
      const hexcolorPicker = document.querySelector('hex-color-picker')
      hexcolorPicker?.addEventListener('color-changed', (e) => {
        selectColor.value = e.detail.value
        clickColorItem(selectColor.value)
      })
    }
  }
)

function clickColorItem(color = '', record = true) {
  if (record && !latestColorList.value.includes(color)) {
    latestColorList.value.unshift(color)
    if (latestColorList.value.length > 10) {
      latestColorList.value = latestColorList.value.slice(-10)
    }
    localStorage.latestColorList = JSON.stringify(latestColorList.value)
  }
  selectColor.value = color
  emitColorChange()
}

function inputFocus() {
  document.querySelectorAll('input.number').forEach((item: any) => {
    item.setSelectionRange(0, 999)
  })
}

function inputBlur() {
  const val = selectColor.value
  const reg = /^#([0-9a-fA-f]{3}|[0-9a-fA-f]{6})$/
  const matchReg = reg.test(val)
  if (val === transparent || matchReg) {
    return
  } else {
    clickColorItem('', false)
  }
}

function nativePick() {
  new window.EyeDropper().open().then((res: any) => {
    clickColorItem(res.sRGBHex)
  })
}

const emits = defineEmits(['change'])
function emitColorChange() {
  if (props.color !== selectColor.value) {
    if (props.type === 'close') {
      document.body.click()
    }
    emits('change', selectColor.value)
  }
}

function expandHexColor(hex: any) {
  hex = hex ? hex.replace('#', '') : ''
  if (!hex || hex === transparent) {
    return transparent
  }
  return '#' + hex.padEnd(6, hex)
}

function rgbToHex(rgb: any) {
  let [r, g, b] = rgb
    .replace(/(?:rgb|rgba)\(/, '')
    .replace(/\)/, '')
    .split(',')
  const hex = (
    (1 << 24) +
    (parseInt(r, 10) << 16) +
    (parseInt(g, 10) << 8) +
    parseInt(b, 10)
  )
    .toString(16)
    .slice(1)
  return { hex: `#${hex}` }
}
</script>

<style lang="scss" scoped>
.ColorBox {
  width: 220px;

  .line {
    margin: 0 12px;
    width: 196px;
    opacity: 0.5;
    border-bottom: 1px solid #d9d9d9;
  }

  .chooseColor {
    padding: 0 12px;
    margin-bottom: 10px;

    .currentColor {
      width: 31px;
      height: 31px;
      border-radius: 1px;

      i {
        position: relative;
        display: inline-block;
        width: 28px;
        height: 1px;
        background-color: #ffffff;
        transform: skewY(-45deg);
      }
    }

    .number {
      width: 20px;
      height: 17px;
      margin-left: 8px;
      border-bottom: 1px solid rgba(217, 217, 217, 0.5);

      font-size: 12px;
      color: #333333;
      line-height: 17px;

      &:focus {
        border-radius: 1px;
        outline: 1px solid #4c91ff;
        caret-color: #4c91ff;
      }
    }

    p {
      margin-left: 8px;

      font-size: 10px;
      color: #333333;
      line-height: 14px;
    }

    .Hex {
      .number {
        width: 49px;
      }
    }

    .Hex,
    .R,
    .G,
    .B {
      position: relative;
      top: -3px;
    }

    .colorPicker {
      margin-left: 8px;

      &:hover {
        margin-top: 6px;
        width: 22px;
        height: 22px;
        background: #ececec;
        border-radius: 2px;
        cursor: pointer;
      }

      .colorPickerImg {
        width: 22px;
        height: 22px;
      }
    }
  }

  .colorModule,
  .recently {
    .colorList {
      .colorItem {
        float: left;
        width: 16px;
        height: 16px;
        border-radius: 1px;
        margin: 0 4px 4px 0;
        cursor: pointer;

        &:nth-child(10n) {
          margin: 0;
        }
      }

      .moreColor {
        display: flex;

        hex-color-picker {
          display: flex;
          flex-direction: column;
          position: relative;
          width: 196px;
          height: 118px;

          &::part(saturation) {
            border-radius: 1px;
            border-bottom: 0;
            overflow: hidden;
          }

          &::part(saturation-pointer) {
            width: 12px;
            height: 12px;
          }

          &::part(hue) {
            flex: 0 0 8px;
            margin: 8px 0 4px;
            border-radius: 12px;
          }

          &::part(hue-pointer) {
            width: 12px;
            height: 12px;
          }
        }
      }
    }
  }

  .colorModule {
    overflow: hidden;

    .colorList {
      padding: 0 12px;
      overflow: hidden;
      min-height: 120px;
    }

    .moduleList {
      padding: 0 12px;
      margin: 11px 0 5.5px;

      .item {
        font-size: 12px;
        color: #999999;
        line-height: 17px;
        margin-right: 16px;
        cursor: pointer;
      }

      .choose {
        color: #333333;
      }
    }
  }

  .clearColor {
    padding: 0 12px;
    margin: 4px 0 12px;

    .el-button {
      width: 196px;
      height: 25px;
      background: #ffffff;
      border-radius: 1px;
      border: 1px solid rgba(217, 217, 217, 0.5);

      font-size: 12px;
      color: #333333;
      line-height: 0;
      text-align: center;
    }
  }

  .recently {
    padding: 0 12px;
    .title {
      margin-top: 11px;
      font-size: 12px;
      color: rgba(0, 0, 0, 0.85);
      line-height: 17px;
    }

    .colorList {
      margin-top: 8px;
    }
  }
}
</style>
