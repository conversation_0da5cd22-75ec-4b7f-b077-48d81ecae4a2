<template>
  <div class="picture-header flex">
    <div class="picture-btns flex flex1">
      <el-button
        class="btn add-pic"
        type="primary"
        @click="(e: any) => isElectron ? electronUpload(e) : webUpload(e)"
      >
        <img src="/img/pic/index/<EMAIL>" />
        <span>添加图片</span>
      </el-button>
      <el-button
        v-if="isElectron"
        class="btn add-folder"
        @click="(e: any) => isElectron ? electronUploadFolder(e) : webUploadFolder(e)"
        ><img
          class="templateIcon"
          src="/img/pic/index/<EMAIL>"
        />添加文件夹
      </el-button>
      <div v-if="isPDF" class="pdf-tip">*按住图片拖动，可调整位置</div>
    </div>
    <div class="picture-btns">
      <el-button class="btn remove" @click="clearList"
        ><img
          class="templateIcon"
          src="/img/pic/index/<EMAIL>"
        />清空列表
      </el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { storeToRefs } from 'pinia'
import { usePictureStore } from '@/pinia/picture'
import { useRoute } from 'vue-router'
import { pageType as pageTypeEnum } from '@/utils/enum'
import { messageSimple } from '@/utils/message'
const { proxy } = getCurrentInstance()
const pictureStore = usePictureStore()
let { imgsMap } = storeToRefs(pictureStore)
const isElectron = window.isElectron

const route = useRoute()
const isPDF = route.name === pageTypeEnum.pdf

const electronUpload = pictureStore.electronUpload
const electronUploadFolder = pictureStore.electronUploadFolder
const webUpload = pictureStore.webUpload
const webUploadFolder = pictureStore.webUploadFolder

const clearList = () => {
  if (pictureStore.hasLoading()) return messageSimple.warning('当前页面图片处理中，请稍后')

  proxy?.$messageBox
    .confirm('只清空列表记录，已处理好的文件不会删除', '确定清空列表？', {
      distinguishCancelAndClose: true,
      confirmButtonText: '确定', // 保留
      cancelButtonText: '取消', // 不保留
      customClass: 'customStyleByMessageBox',
      type: 'warning',
      // center: true
    })
    .then((e: any) => {
      if (e === 'confirm') {
        imgsMap.value.clear()
      }
    })
    .catch((e: any) => {})
}
</script>

<style lang="scss" scoped>
@import '@/assets/css/picture-btns.scss';
.picture-header {
  min-width: 901px;
}
</style>
