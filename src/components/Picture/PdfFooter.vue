<template>
  <div v-if="isElectron || imgsMap.size" class="pdf-footer flex">
    <div class="flex1">
      <!-- common 导出目录 -->
      <ExportDirectory></ExportDirectory>
    </div>
    <button
      v-show="imgsMap.size"
      class="start-convert btn"
      :class="{
        'start-convert-web': !isElectron,
      }"
      @click="startHandler"
    >
      <div>开始处理</div>
    </button>
  </div>
</template>

<script lang="ts" setup>
import { storeToRefs } from 'pinia'
import { usePictureStore } from '@/pinia/picture'
import { usePdfStore } from '@/pinia/pdf'
import { messageSimple } from '@/utils/message'
const pictureStore = usePictureStore()
const pdfStore = usePdfStore()
let { imgsMap } = storeToRefs(pictureStore)
let { showPdfConvertEdit } = storeToRefs(pdfStore)
// const beforeConvert = pictureStore.beforeConvert
const isElectron = window.isElectron

const hasChecked = (imgsMap: any) => {
  let checked = false
  imgsMap.forEach((img: any) => {
    if (img.checked) checked = true
  })
  return checked
}
const startHandler = () => {
  if (!hasChecked(imgsMap.value)) {
    return messageSimple.warning('请先选择图片')
  }
  showPdfConvertEdit.value = true
}
</script>

<style lang="scss" scoped>
.pdf-footer {
  position: absolute;
  bottom: 0;
  background-color: #fff;
  width: 100%;
  padding: 13.5px 20px;
  border-top: 1px solid #e0e0e0;

  .el-checkbox__inner {
    border-radius: 50%;
  }

  :deep(.el-checkbox__label) {
    span {
      color: #333;
    }
  }

  .export-directory {
    width: 778px;
    margin-top: 3px !important;
  }

  .start-convert {
    position: absolute;
    right: 20px;
    bottom: 14px;

    width: 100px;
    height: 32px;
    line-height: 22px;
    border-radius: 4px;
    background: #389bfd;
    color: #fff;
    font-size: 15px;
    border-radius: 4px;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    user-select: none;
    z-index: 1;
    cursor: pointer;

    &:hover {
      opacity: 0.7;
    }
  }

  .start-convert-web {
    bottom: 14px;
    width: 100px;

    height: 32px;
    margin: 0;
    font-size: 15px;

    .el-icon {
      margin-right: 3px;
      animation: rotating 2s linear infinite;
    }
  }
}
</style>
