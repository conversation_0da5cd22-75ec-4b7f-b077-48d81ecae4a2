<template>
  <div
    class="flex slider"
    :class="{
      'slider-compress': isSimpleCompress,
      'slider-all': !isSimpleCompress,
    }"
  >
    <div>
      <p class="desc">体积较小</p>
      <p class="desc-tip">画质模糊</p>
    </div>
    <el-slider
      class="flex1"
      v-model="clearVal"
      :marks="marks"
      :min="min"
      :max="max"
      :show-tooltip="false"
      @input="changeCompressRadio"
    />
    <div>
      <p class="desc text-left">体积较大</p>
      <p class="desc-tip text-left">画质清晰</p>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted } from 'vue'
import { storeToRefs } from 'pinia'
import { useCompressStore } from '@/pinia/compress'
const compressStore = useCompressStore()
let { compressRadio } = storeToRefs(compressStore)

const props = defineProps({
  type: {
    type: String,
    default: '',
  },
  updateCurrentSize: {
    type: Function,
    default: () => {},
  },
  currentCompressRadio: {
    type: Object,
    default: {},
  },
})

const compressType = '自定义'
const isSimpleCompress = props.type === 'simpleCompress'
let clearVal = ref(
  isSimpleCompress
    ? props.currentCompressRadio[compressType]
    : compressRadio.value[compressType]
)

const changeCompressRadio = (value?: any) => {
  // console.log(value)

  if (isSimpleCompress) {
    props.currentCompressRadio[compressType] = clearVal.value // 可以用emit
  } else {
    compressRadio.value[compressType] = clearVal.value
    compressStore.compressChange(compressRadio.value[compressType])
  }

  // 自定义 tooltip
  let button: any = document.querySelector(
    `.${isSimpleCompress ? 'slider-compress' : 'slider-all'} .el-slider__button`
  )
  if (button) {
    button.innerHTML = `<span class="tool-tip">${clearVal.value}%</span>`
  }

  props.updateCurrentSize(clearVal.value)
}

// slider marks
const min = 10
const max = 90
const marks = reactive<any>({
  10: '10%',
  20: '20%',
  30: '30%',
  40: '40%',
  50: '50%',
  60: '60%',
  70: '70%',
  80: '80%',
  90: '90%',
})

// watch(
//   isSimpleCompress
//     ? () => props.currentCompressRadio[compressType]
//     : () => compressRadio.value[compressType],
//   (newValue, oldValue) => {
//     console.log('watch currentCompressRadio', newValue, oldValue)
//     clearVal.value = newValue
//   }
// )

onMounted(() => {
  changeCompressRadio()
})
</script>

<style lang="scss" scoped>
.slider {
  margin: 16px 0 2px;
  width: 635px;
  height: 64px;
  margin-left: 64px;

  :deep(.el-slider) {
    margin: 0 20px;

    .el-slider__marks-text {
      margin-top: 19px;
      font-size: 10px;
      color: #999999;
      line-height: 14px;
      text-align: center;
    }

    .el-slider__runway {
      height: 8px;
      background-color: #e7f4fd;
      border-radius: 4px;

      .el-slider__bar {
        display: none;
      }

      .el-slider__button {
        position: relative;
        width: 10px;
        height: 20px;
        // 原始图标
        // background-color: #389bfd;
        // border-radius: 2px;
        // border-bottom-left-radius: 50%;
        // border-bottom-right-radius: 50%;
        // 自定义图标
        border: 0;
        border-radius: 0;
        background-color: transparent;
        background-image: url(/img/pic/second/<EMAIL>);
        background-size: contain;

        .tool-tip {
          position: relative;
          top: -21.5px;
          left: -5px;
          height: 17px;
          line-height: 17px;
          font-size: 12px;
          font-weight: 600; // electron
          color: #333;
          user-select: none;
        }
      }

      .el-slider__stop {
        top: 2px;
        width: 1px;
        height: 6px;
        background-color: #6e8ca9;
        border-radius: 1px 1px 0px 0px;

        &:first-child {
          width: 0;
        }
        &:last-child {
          width: 0;
        }
      }
    }
  }

  .desc {
    margin: -3px 0 4px;
    font-size: 12px;
    color: #333333;
    line-height: 17px;
    text-align: right;
  }

  .desc-tip {
    font-size: 10px;
    color: #999999;
    line-height: 14px;
    text-align: right;
  }

  .text-left {
    text-align: left;
  }
}
</style>
