<template>
  <div class="mask" />
  <div class="compress-dialog">
    <div class="compress-info">
      <!-- 压缩模式 -->
      <div class="compress-model flex">
        <span class="tip">压缩模式</span>
        <el-radio-group
          v-model="currentCompressType"
          @change="updateCurrentSize(currentCompressRadio[currentCompressType])"
        >
          <el-radio
            v-for="comKey in Object.keys(currentCompressRadio)"
            :key="comKey"
            :label="comKey"
          ></el-radio>
        </el-radio-group>
      </div>
      <!-- 压缩模式-滑动条 -->
      <CompressSlider
        v-if="currentCompressType === '自定义'"
        type="simpleCompress"
        :currentCompressRadio="currentCompressRadio"
        :updateCurrentSize="updateCurrentSize"
      ></CompressSlider>
      <!-- 压缩大小 -->
      <div
        class="compress-size flex"
        :style="{
          marginTop: currentCompressType !== '自定义' ? '20px' : '-5px',
        }"
      >
        <span class="tip">大小</span>
        <span class="desc">{{ currentSize }}</span>
        <span class="size">
          {{ `（${minSize}~${maxSize}）` }}
        </span>
      </div>
      <div
        class="compress-btns"
        :style="{
          marginTop: currentCompressType !== '自定义' ? '40px' : '40px',
        }"
      >
        <!-- <el-button class="btn reset" @click="reset" link>重置 </el-button> -->
        <el-button class="btn cancel" @click="showCompressDialog = false" plain
          >取消
        </el-button>
        <el-button class="btn confirm" type="primary" @click="confirm">
          <span>确定</span>
        </el-button>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { ref, reactive } from 'vue'
import { storeToRefs } from 'pinia'
import { useCompressStore } from '@/pinia/compress'
import CompressSlider from '@/components/Picture/CompressSlider.vue'
// import { tools } from '@haluo/util'
// const { proxy } = getCurrentInstance()
// console.log(proxy.$filter.tools) // test
// const compressRadioEnum = tools.convertKeyValueEnum(compressRadio.value as any)

const compressStore = useCompressStore()
let { showCompressDialog } = storeToRefs(compressStore)

let currentCompressType: any = ref(compressStore.currentCompress.compressType)
let currentCompressRadio: any = reactive({
  ...compressStore.compressRadio,
  自定义: compressStore.currentCompress.clearVal,
})

// size 相关
const getCurrentSize = (customClearVal?: number) => {
  return compressStore.getCompressSize(
    compressStore.currentCompress,
    customClearVal || compressStore.currentCompress.clearVal
  )
}
const updateCurrentSize = (customClearVal?: number) => {
  currentSize.value = getCurrentSize(customClearVal)
}
let currentSize = ref(getCurrentSize())
const minSize = compressStore.getCompressSize(
  compressStore.currentCompress,
  10
)
const maxSize = compressStore.getCompressSize(
  compressStore.currentCompress,
  90
)

// console.log(
//   'dialog',
//   compressStore.currentCompress.key,
//   compressStore.currentCompress,
//   currentCompressType.value,
//   currentCompressRadio
// )
// const reset = () => {
//   currentCompressType.value = '标准压缩'
//   currentCompressRadio['自定义'] = 75
// }

const confirm = () => {
  compressStore.compressSimpleChange(
    currentCompressRadio[currentCompressType.value],
    currentCompressType.value
  )
  showCompressDialog.value = false
}
</script>

<style lang="scss" scoped>
@import './compress-model.scss';
.compress-dialog {
  position: fixed;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  width: 754px;
  z-index: 2001;

  .compress-info {
    position: fixed;
    top: 50%;
    left: 50%;
    -webkit-transform: translate(-50%, -50%);
    transform: translate(-50%, -50%);
    text-align: center;

    // height: 266px;
    padding: 30px 28px 20px;
    border-radius: 4px;
    background: #fff;
    @include common-dialog;

    // :deep(.compress-model) {
    //   .el-radio__input {
    //     top: 0.5px;
    //   }

    //   .el-radio__input.is-checked + .el-radio__label {
    //     color: #333 !important;
    //   }
    // }

    .compress-size {
      .tip,
      .size,
      .desc {
        margin-right: 8px;
        line-height: 22px;
        font-size: 14px;
        color: #999999;
        text-align: left;
      }
      .desc {
        margin-right: 0;
        color: #333;
      }
      .tip {
        width: 56px;
      }
    }

    .compress-btns {
      text-align: right;

      .btn {
        width: 83px;
        height: 32px;
        margin-left: 20px;
        border-radius: 4px;

        font-size: 14px;
      }
      .reset {
        color: #999999;
      }
      .cancel {
        color: #389bfd;
        border: 1px solid #389bfd;
      }
      .confirm {
        color: #fff;
        background: #389bfd;
        border: 1px solid #389bfd;
      }
    }
  }
}
</style>
