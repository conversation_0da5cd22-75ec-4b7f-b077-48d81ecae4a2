<template>
  <div class="mask" />
  <div class="pdf-dialog">
    <div class="info flex">
      <img
        class="close"
        @click="showPdfConvertEdit = false"
        src="/img/mind_map/table/<EMAIL>"
        alt=""
      />
      <div class="list">
        <div class="item flex">
          <p>纸张大小</p>
          <el-button
            class="btn"
            :class="{ select: paperSize === 'A4' }"
            @click="paperSize = 'A4'"
            >A4</el-button
          >
          <el-button
            class="btn"
            :class="{ select: paperSize === 'A3' }"
            @click="paperSize = 'A3'"
            >A3</el-button
          >
          <el-button
            class="btn"
            :class="{ select: paperSize === '' }"
            @click="paperSize = ''"
            >原图</el-button
          >
        </div>
        <div class="item flex">
          <p>纸张方向</p>
          <el-button
            class="btn"
            :class="{ select: orientation === 'vertical' }"
            @click="orientation = 'vertical'"
            >纵向</el-button
          >
          <el-button
            class="btn"
            :class="{ select: orientation === 'horizontal' }"
            @click="orientation = 'horizontal'"
            >横向</el-button
          >
        </div>
        <div class="item flex">
          <p>页面间距</p>
          <el-button
            class="btn"
            :class="{ select: spacing === 50 }"
            @click="spacing = 50"
            >有</el-button
          >
          <el-button
            class="btn"
            :class="{ select: spacing === 0 }"
            @click="spacing = 0"
            >无</el-button
          >
        </div>
        <div class="line"></div>
        <div class="item output-type flex">
          <p>输出方式</p>
          <el-radio-group
            class="flex"
            v-model="outputType"
            @change="changeOutputType"
          >
            <el-radio
              v-for="type in outputs"
              :key="type.key"
              :label="type.key"
              >{{ type.value }}</el-radio
            >
          </el-radio-group>
        </div>
        <div class="item output-name flex">
          <p>输出名称</p>
          <el-input
            type="text"
            v-model="outputName"
            placeholder="请输入文件输出名称"
            disabled
          />
        </div>
        <el-button class="btn convert" type="primary" @click="convert">
          开始转换
        </el-button>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { onMounted } from 'vue'
import { storeToRefs } from 'pinia'
import { usePdfStore } from '@/pinia/pdf'
import { usePictureStore } from '@/pinia/picture'
import { mapToArray } from '@/utils/util'
import dayjs from 'dayjs'

const pdfStore = usePdfStore()
const pictureStore = usePictureStore()
let {
  showPdfConvertEdit,
  paperSize,
  orientation,
  spacing,
  outputType,
  outputName,
  checkedNums,
  callbackNums,
} = storeToRefs(pdfStore)
let { imgsMap } = storeToRefs(pictureStore)

const outputs = [
  {
    key: 'multiple',
    value: '一张图片一个PDF',
  },
  {
    key: 'single',
    value: '合并为一个PDF',
  },
]

const convert = () => {
  showPdfConvertEdit.value = false
  const options = {
    paperSize: paperSize.value,
    orientation: orientation.value,
    spacing: spacing.value,
    outputType: outputType.value,
    outputName: outputName.value,
  }
  // 更新选中数量和回调数量
  checkedNums.value = mapToArray(imgsMap.value).filter(
    (item: any) => item.checked
  ).length
  callbackNums.value = 0
  pictureStore.beforeConvertPdf(imgsMap.value, options)
}
const changeOutputType = (e: any) => {
  console.log(e)
}

onMounted(() => {
  outputName.value = '图片转PDF' + dayjs(new Date()).format('YYYYMMDDHHmmss')
})
</script>

<style lang="scss" scoped>
.pdf-dialog {
  position: fixed;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  width: 498px;
  border-radius: 4px;
  z-index: 2001;

  .info {
    position: fixed;
    top: 50%;
    left: 50%;
    -webkit-transform: translate(-50%, -50%);
    transform: translate(-50%, -50%);
    text-align: center;

    width: 498px;
    height: 383px;
    padding: 30px 28px 20px;
    border-radius: 4px;
    background: #fff;
    @include common-dialog;

    padding: 42px 28px;

    .close {
      position: absolute;
      right: 14px;
      top: 14px;
      margin-bottom: 25px;
      height: 20px;
      width: 20px;
      cursor: pointer;

      &:hover {
        opacity: 0.7;
      }
    }

    .list {
      width: 442px;
      .line {
        margin-bottom: 20px;
      }
      .item {
        margin-bottom: 20px;

        p {
          font-weight: 400;
          font-size: 14px;
          color: #999999;
          line-height: 28px;
          margin-right: 16px;
        }

        .el-button {
          width: 60px;
          height: 28px;
          border-radius: 4px;
          border: 1px solid #eaeaea;

          font-weight: 400;
          font-size: 14px;
          color: #000000;
          line-height: 22px;

          &:hover,
          &:focus {
            background-color: #fff;
          }
        }

        .select {
          width: 60px;
          height: 28px;
          color: #006eff;
          border-radius: 4px;
          border: 1px solid #006eff;
        }
      }

      .output-type {
        height: 24px;
        p {
          font-weight: 400;
          font-size: 14px;
          color: #999999;
          line-height: 24px;
        }

        :deep(.el-radio-group) {
          height: 24px;

          .el-radio {
            height: 24px;
            line-height: 24px;
            margin-right: 30px;

            .el-radio__input {
              top: 1px;
            }

            .el-radio__input.is-checked + .el-radio__label {
              color: rgba(0, 0, 0, 0.65) !important;
            }

            .el-radio__label {
              padding-left: 8px;
              line-height: 24px;
              font-weight: 400;
              font-size: 14px;
              color: rgba(0, 0, 0, 0.65);
            }

            .el-radio__inner {
              border-color: #d4d4d4;
            }

            .el-radio__input.is-checked .el-radio__inner {
              border-color: #0089fa;
              background: #fff;
            }

            .el-radio__inner::after {
              width: 8px;
              height: 8px;
              background-color: #0089fa;
            }
          }
        }
      }

      .output-name {
        p {
          line-height: 32px;
        }
        :deep(.el-input) {
          width: 368px;
          height: 32px;
          .el-input__wrapper {
            background-color: #fff;
          }

          .el-input__inner {
            background: #ffffff;
            border-radius: 4px;

            font-weight: 400;
            font-size: 14px;
            color: #000000;
            line-height: 22px;
          }
        }
      }

      :deep(.convert) {
        position: absolute;
        right: 28px;
        bottom: 20px;
        width: 83px;
        height: 32px;
        background: #389bfd;
        border: 0;
        border-radius: 4px;

        font-weight: 400;
        font-size: 14px;
        color: #ffffff;

        span {
          position: relative;
          top: -1px;
        }
      }
    }
  }
}
</style>
