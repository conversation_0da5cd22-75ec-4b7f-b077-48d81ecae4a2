<template>
  <div class="content">
    <!-- 图片相关信息 -->
    <div class="item-content flex">
      <div v-if="props.imgMap[1].record.status === 'fail'" class="fail"></div>
      <div
        class="img-wrap chessboard-bg"
        :class="{
          'img-wrap-web': !isElectron,
        }"
      >
        <img
          :src="
            props.imgMap[1].record.imgUrl ||
            '/img/pic/index/<EMAIL>'
          "
        />
        <div
          v-if="isElectron"
          class="btn open-img"
          @click="electronOpenFile($event, props.imgMap[1].originFile)"
        >
          打开
        </div>
      </div>
      <div class="origin-info flex flex1">
        <div class="flex1">
          <p class="name flex">
            <span
              class="dotdotdot1"
              style="width: 200px"
              :class="{ 'show-tooltip': isOverflow }"
              @mouseover="checkOverflow"
              @mouseleave="isOverflow = false"
              :data-fullname="props.imgMap[1].filename"
              >{{ props.imgMap[1].filename }}</span
            >
          </p>
          <p>
            <span>格式</span>
            <span>{{ props.imgMap[1].record.originFormat }}</span>
          </p>
          <p>
            <span>分辨率</span>
            <span>{{ props.imgMap[1].record.originPixel }}</span>
          </p>
          <p>
            <span>大小</span> <span>{{ props.imgMap[1].record.size }}</span>
          </p>
        </div>
        <img
          v-if="isWatermark || isCompress"
          class="modify"
          style="width: 15px; height: 9px; top: 37.5px"
          src="/img/pic/second/<EMAIL>"
          alt=""
        />
        <img
          v-else
          class="modify"
          src="/img/pic/index/<EMAIL>"
          alt=""
        />
      </div>
      <div class="format-info flex1">
        <input
          v-if="props.imgMap[1].record.edit"
          :ref="(el) => (inputRefs[index] = el)"
          type="text"
          maxlength="50"
          v-model="props.imgMap[1].newFileName"
          @change="newFileNameChange(props.imgMap[0])"
          @blur="props.imgMap[1].record.edit = false"
        />
        <p class="name flex">
          <span
            class="dotdotdot1"
            :class="{ 'show-tooltip': isOverflow }"
            @mouseover="checkOverflow"
            @mouseleave="isOverflow = false"
            :style="{ width: getWidth(props.imgMap[1].newFileName) + 'px' }"
            :data-fullname="props.imgMap[1].newFileName"
            >{{ props.imgMap[1].newFileName }}</span
          >
          <img
            class="modify-btn"
            src="/img/pic/index/<EMAIL>"
            alt=""
            @click="inputFocus(props.imgMap[1], index)"
          />
        </p>
        <p>
          <span>格式</span>
          <span>{{
            props.imgMap[1].record[isWatermark ? 'originFormat' : 'newFormat']
          }}</span>
        </p>
        <p>
          <span>分辨率</span>
          <span>{{ props.imgMap[1].record.newFormat.includes('ico') ? props.imgMap[1].record.icoSizeValue : props.imgMap[1].record.originPixel }}</span>
        </p>
        <p v-if="isCompress">
          <span>大小</span>
          <span>{{ props.imgMap[1].record.targetSize }}（预估）</span>
        </p>
      </div>
      <div class="status flex">
        <!-- <p v-if="isElectron && props.imgMap[1].record.status > 0">
          <el-icon color="#389bfd" size="27"><Loading /></el-icon>
        </p> -->
        <!-- || true -->
        <template
          v-if="
            props.imgMap[1].record.status === 'wait' &&
            !props.imgMap[1].record.loading
          "
        >
          <p
            v-if="isCompress"
            class="wait btn"
            @click="showCompressSetting(props.imgMap[1])"
          >
            <img src="/img/pic/second/<EMAIL>" alt="" />
            <span>压缩设置</span>
          </p>
          <p
            v-else-if="isWatermark"
            class="wait btn wartermark"
            @click="showWatermarkSetting(props.imgMap[1])"
          >
            <img src="/img/pic/second/<EMAIL>" alt="" />
            <span>加水印设置</span>
          </p>
          <ConvertNewFormat
            v-else
            :type="'simple'"
            :imgMap="props.imgMap"
          ></ConvertNewFormat>
        </template>
        <p
          v-else-if="props.imgMap[1].record.status === 'success'"
          class="success"
        >
          <img src="/img/pic/index/<EMAIL>" alt="" />
          成功
        </p>
        <p v-else-if="props.imgMap[1].record.status === 'fail'" class="fail">
          <img src="/img/pic/index/<EMAIL>" alt="" />
          失败
        </p>
        <p v-else-if="props.imgMap[1].record.status > 0">
          <span class="sub-progress">{{ props.imgMap[1].record.status }}%</span>
          <!-- <p v-if="props.imgMap[1].record.status === 'pause'" class="pause">已暂停</p> -->
        </p>
      </div>
      <div class="action flex">
        <button
          v-if="isElectron && props.imgMap[1].record.status === 'success'"
          class="blue btn"
          @click="electronOpenFile($event, props.imgMap[1].newFile)"
        >
          打开
        </button>
        <button
          v-if="isElectron && props.imgMap[1].record.status === 'success'"
          class="white btn"
          @click="electronOpenFolder"
        >
          打开文件夹
        </button>
        <button
          v-if="!isElectron && props.imgMap[1].record.aliImgUrl"
          class="white btn"
          @click="download(props.imgMap[1])"
        >
          保存
        </button>
        <button
          v-if="
            props.imgMap[1].record.status === 'wait' &&
            !props.imgMap[1].record.aliImgUrl
          "
          class="blue btn flex"
          @click="beforeConvert([props.imgMap[1]])"
        >
          <el-icon
            v-if="props.imgMap[1].record.loading"
            class="is-loading"
            color="#fff"
          >
            <Loading />
          </el-icon>
          <div>
            {{ props.imgMap[1].record.loading ? '处理中' : '立即处理' }}
          </div>
        </button>
        <button
          v-if="
            props.imgMap[1].record.status === 'fail' ||
            props.imgMap[1].record.status === 'cancel'
          "
          class="blue btn flex"
          @click="
            () => {
              props.imgMap[1].record.status = 'wait'
              convert([props.imgMap[1]])
            }
          "
        >
          <el-icon
            v-if="props.imgMap[1].record.loading"
            class="is-loading"
            color="#fff"
          >
            <Loading />
          </el-icon>
          <div>重试</div>
        </button>
        <!-- 水印不支持取消 -->
        <button
          v-if="isElectron && !isWatermark && props.imgMap[1].record.status > 0"
          class="cancel btn red"
          @click="magickCancel(props.imgMap)"
        >
          取消
        </button>
        <button
          v-if="!isElectron && props.imgMap[1].record.status === 'success'"
          style="z-index: -1"
        ></button>
      </div>
      <div
        v-if="!props.imgMap[1].record.loading"
        class="delete btn"
        @click="deleteImg(props.imgMap[1].key)"
      ></div>
    </div>
    <!-- 进度条 -->
    <div
      v-if="isElectron"
      class="progress"
      :style="{
        width:
          props.imgMap[1].record.status > 0 ||
          props.imgMap[1].record.status < 100
            ? props.imgMap[1].record.status + '%'
            : 0,
      }"
    ></div>
  </div>
</template>

<script lang="ts" setup>
import { ref, nextTick } from 'vue'
import { storeToRefs } from 'pinia'
import { usePictureStore } from '@/pinia/picture'
import { useCompressStore } from '@/pinia/compress'
import { useWatermarkStore } from '@/pinia/watermark'
import { useRoute } from 'vue-router'
import { pageType as pageTypeEnum } from '@/utils/enum'
import ConvertNewFormat from '@/components/Picture/ConvertNewFormat.vue'

const isElectron = window.isElectron
const pictureStore = usePictureStore()
const compressStore = useCompressStore()
const watermarkStore = useWatermarkStore()
let { inputRefs } = storeToRefs(pictureStore)
let { showCompressDialog } = storeToRefs(compressStore)
let { isGlobal, showWatermarkPictureEditDialog } = storeToRefs(watermarkStore)

const route = useRoute()
const isCompress = route.name === pageTypeEnum.compress
const isWatermark = route.name === pageTypeEnum.watermark

const props = defineProps({
  index: {
    type: Number,
    default: 1,
  },
  imgMap: {
    type: Object,
    default: {},
  },
})

// electron、web 图片转换
const beforeConvert = pictureStore.beforeConvert
const convert = pictureStore.convert

// web
const download = pictureStore.download
const newFileNameChange = pictureStore.newFileNameChange
const deleteImg = pictureStore.deleteImg
const getWidth = (text: string) => {
  // 创建一个临时的 span 元素来计算文本的宽度
  const span = document.createElement('span')
  span.style.visibility = 'hidden'
  span.style.position = 'absolute'
  span.style.whiteSpace = 'nowrap' // 防止文本换行
  span.style.font = '14.5px Arial' // 根据你的字体和大小设置，这里用16px而不是原始宽度14px
  span.innerText = text
  document.body.appendChild(span)
  const width = span.offsetWidth
  document.body.removeChild(span)
  return width > 200 ? 200 : width // 如果宽度大于200px，则返回200，否则返回实际宽度
}
// 修改文件名的输入框（非通用，可不提取）
const inputFocus = async (key: any, index: number) => {
  key.record.edit = true
  await nextTick() // 等待DOM更新
  const inputEl: any = inputRefs.value[index]
  if (inputEl) {
    inputEl.focus() // 聚焦输入框
  }
}
const showCompressSetting = (key: any) => {
  showCompressDialog.value = true
  compressStore.setCurrent(key)
}
const showWatermarkSetting = (key: any) => {
  isGlobal.value = false
  showWatermarkPictureEditDialog.value = true
  watermarkStore.setCurrent(key)
}
// 显示toolTip
const isOverflow = ref(false)
const checkOverflow = (event: any) => {
  const span = event.target
  isOverflow.value = span.scrollWidth > span.clientWidth
}

// electron
const electronOpenFile = pictureStore.electronOpenFile
const electronOpenFolder = pictureStore.electronOpenFolder
const magickCancel = isCompress
  ? compressStore.magickCancel
  : pictureStore.magickCancel
</script>

<style lang="scss" scoped>
.content {
  position: relative;

  .item-content {
    height: 129px;
    padding: 20px 0;
    border-bottom: 1px solid #e0e0e0;

    .img-wrap {
      position: relative;
      width: 80px;
      height: 80px;
      margin-right: 16px;
      border-radius: 4px;
      background-color: #ededed;
      // @include colorBtn(#000);

      img {
        position: relative;
        width: 100%;
        height: 100%;
        object-fit: contain;
      }

      .open-img {
        position: absolute;
        top: 27.5px;
        left: 16px;
        visibility: hidden;

        width: 48px;
        height: 25px;
        background: #389bfd;
        border-radius: 4px;

        font-weight: normal;
        font-size: 12px;
        color: #ffffff;
        line-height: 25px;
        cursor: pointer;
      }

      &:hover {
        &::after {
          position: absolute;
          content: '';
          left: 0;
          top: 0;
          width: 100%;
          height: 100%;
          border-radius: 4px;
          background: #000000;
          opacity: 0.1;
          z-index: 1;
        }
        .open-img {
          visibility: visible;
          z-index: 2;
        }
      }
    }

    .img-wrap-web {
      &:hover {
        &::after {
          opacity: 0;
        }
      }
    }

    p {
      line-height: 20px;
    }

    .origin-info,
    .format-info {
      position: relative;
      margin-right: 20px;

      // hover显示全部名称
      .name {
        .dotdotdot1::after {
          content: attr(data-fullname);
          position: absolute;
          white-space: nowrap;
          top: 0;
          left: 0;
          visibility: hidden;
          opacity: 0;
          background-color: #fbfbfb;
          transition: opacity 0.3s, visibility 0.3s;
          z-index: 1;

          padding: 2px 8px;
          top: 25px;
          // left: -7px;
          @include common-dialog; // #e3e3e3

          // top: -28px;
          // background: #f0f0f0;
          // box-shadow: 0px 1px 4px 0px rgba(194, 194, 194, 0.62);
          // border-radius: 1px;
          // border: 0px solid #c3c3c3;
        }

        .show-tooltip:hover::after {
          visibility: visible;
          opacity: 1;
        }
      }

      input {
        position: absolute;
        left: 0;
        top: -2px;
        z-index: 1;
        width: 195px;
        height: 24px;
        padding: 2px 4px;
        margin-bottom: 8px;
        border-radius: 2px;
        border: 1px solid #389bfd;

        font-size: 14px;
        color: #333333;
        line-height: 20px;
      }

      p {
        line-height: 17px;
        margin-bottom: 4px;
        font-size: 12px;
        color: #999;

        span:first-child {
          float: left;
          width: 52px;
          color: #999;
        }
        span:last-child {
          clear: both;
          color: #666;
        }
      }

      .name {
        line-height: 20px;
        margin-bottom: 8px;
        font-size: 14px;
        color: #333333;

        span:first-child {
          color: #333333;
        }
      }

      .modify {
        position: relative;
        width: 16px;
        height: 14px;
        top: 34px;
      }
    }

    .format-info {
      .name {
        img {
          margin: 3px 0 0 5px;
          width: 14px;
          height: 14px;
          cursor: pointer;
        }
      }
    }

    .status {
      min-width: 75px;
      line-height: 20px;
      margin-right: 44px;
      font-size: 14px;
      flex-flow: column;
      justify-content: center;
      text-align: center;
      user-select: none;

      .wait {
        color: #389bfd;
        img {
          position: relative;
          top: -2px;
          width: 16px;
          height: 16px;
          line-height: 16px;
        }
        span {
          display: inline-block;
          line-height: 20px;
          margin-left: 3px;
        }
      }

      .wartermark {
        min-width: 89px;
        img {
          position: relative;
          top: 2px;
          width: 24px;
          height: 24px;
        }
        span {
          margin-left: 0;
        }
      }

      .sub-progress {
        color: #389bfd;
      }

      .sub-progress {
        display: block;
      }
      .success {
        color: #04c400;

        img {
          position: relative;
          top: -2px;
          width: 16px;
          height: 16px;
          line-height: 16px;
        }
      }
      .fail {
        color: #ff0000;

        img {
          position: relative;
          top: -2px;
          width: 16px;
          height: 16px;
          line-height: 16px;
        }
      }
      .pause {
        color: #333333;
      }

      .btn {
        cursor: pointer;
      }
    }

    .action {
      margin-right: 8px;
      flex-flow: column;
      justify-content: center;

      button {
        display: block;
        width: 98px;
        height: 28px;
        line-height: 28px;
        margin-bottom: 8px;
        background: #ffffff;
        border-radius: 4px;
        cursor: pointer;

        &:last-child {
          margin-bottom: 0;
        }
      }

      .blue {
        justify-content: center;
        align-items: center;
        color: #fff;
        background: #389bfd;
      }
      .white {
        line-height: 26px;
        color: #333;
        border: 1px solid #e0e0e0;
      }
      .red {
        line-height: 26px;
        color: #ff3630;
        border: 1px solid #ff3630;
      }

      .el-icon {
        margin-right: 2px;
        animation: rotating 2s linear infinite;
      }
    }

    .delete {
      display: none;
      position: absolute;
      right: 0;
      top: 0;
      width: 26px;
      height: 26px;
      cursor: pointer;
      background-image: url('/img/pic/index/<EMAIL>');
      background-size: contain;

      &:hover {
        background-image: url('/img/pic/index/<EMAIL>');
      }
    }

    &:hover {
      background-color: #fbfbfb;
      .delete {
        display: block;
      }
    }
  }

  .progress {
    position: absolute;
    height: 129px;
    top: 0;
    border-bottom: 3px solid #389bfd;
    z-index: 1;

    background-color: rgba(56, 155, 253, 0.3);
  }
}
</style>
