<template>
  <section class="category-box">
    <div class="tabs flex">
      <a
        v-for="water in categoryWatermarkList"
        class="tab"
        :class="{
          selected: currentTypeId == water.id,
        }"
        :data-id="water.id"
        :data-name="water.name"
        :href="`#${water.id}${water.name}`"
        @click="tabClick(water)"
      >
        {{ water.name }}
      </a>
    </div>
    <div class="category auto-scrollbar" v-scroll-effect>
      <div class="module" v-for="(water, windex) in categoryWatermarkList">
        <div
          class="title"
          :name="`${water.id}${water.name}`"
          :id="`${water.id}${water.name}`"
          :data-id="water.id"
        >
          {{ water.name }}
        </div>
        <div v-if="water.name === '自定义'" class="add-box-new flex">
          <div
            class="text btn flex"
            @click="
              () => {
                // watermarkStore.addTextWatermark()
                addWatermark(watermarkStore.getCustomTextWatermark())
              }
            "
          >
            <img src="/img/pic/second/<EMAIL>" alt="" />
            <p>添加文字水印</p>
          </div>
          <div
            class="img btn flex"
            @click="
              () => {
                // watermarkStore.addImageWatermark()
                addWatermark(watermarkStore.getCustomImageWatermark())
              }
            "
          >
            <img src="/img/pic/second/<EMAIL>" alt="" />
            <p>添加图片水印</p>
          </div>
          <!-- <div class="add flex btn" @click.stop="showAddBox = !showAddBox">
            <img src="/img/pic/second/<EMAIL>" alt="" />
            <p>添加</p>
          </div>
          <div v-if="showAddBox" class="action" @click.stop>
            <div
              class="item btn"
              @click="
                () => {
                  // showAddBox = false
                  watermarkStore.addTextWatermark()
                }
              "
            >
              文字水印
            </div>
            <div class="line"></div>
            <div
              class="item btn"
              @click="
                () => {
                  // showAddBox = false
                  watermarkStore.addImageWatermark()
                }
              "
            >
              图片水印
            </div>
          </div> -->
        </div>
        <div class="list flex">
          <template v-for="content in water.list">
            <div class="item flex" @click="addWatermark(content, windex)">
              <img class="watermark" :src="content.thumbnail" alt="" />
            </div>
          </template>
        </div>
      </div>
    </div>
  </section>
</template>
<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { storeToRefs } from 'pinia'
import { useWatermarkStore } from '@/pinia/watermark'

const watermarkStore = useWatermarkStore()
let {
  // canvasImgWidth,
  // canvasImgHeight,
  categoryWatermarkList,
  currentWatermark,
  globalWatermark,
  currentIndex,
  isGlobal,
} = storeToRefs(watermarkStore)

// let showAddBox = ref(false)
// const getRoom = (content: any) => {
//   const itemWidth = 96
//   const maxSize = Math.max(
//     content.canvas.style.width,
//     content.canvas.style.height
//   )

//   return itemWidth / maxSize
// }

// 添加水印
const addWatermark = (watermark: any, index?: number) => {
  // console.log('addWatermark', watermark)
  const _watermark = JSON.parse(JSON.stringify(watermark))

  // // 默认居中
  // _watermark.canvas.style.left =
  //   (canvasImgWidth.value - _watermark.canvas.style.width) / 2
  // _watermark.canvas.style.top =
  //   (canvasImgHeight.value - _watermark.canvas.style.height) / 2

  if (isGlobal.value) {
    globalWatermark.value.customWatermark.push(_watermark)
    currentIndex.value = globalWatermark.value.customWatermark.length - 1 // 选中最新添加的水印
  } else {
    currentWatermark.value.customWatermark.push(_watermark)
    currentIndex.value = currentWatermark.value.customWatermark.length - 1 // 选中最新添加的水印
  }

  watermarkStore.updateView('categoryBox')
}

// ==================== tabs级联滚动 ====================
let enableComputedScroll: any = true // 是否可以启动滚动计算，锚点点击时禁用
let timer: any = null // 定时器
let currentTypeId = ref(0)
let tabs: any = null
let category: any = null
let threshold = ''
const tabClick = (water: any) => {
  enableComputedScroll = false
  clearTimeout(timer)
  timer = setTimeout(() => (enableComputedScroll = true), 1000)

  currentTypeId.value = water.id
  // console.log(111, currentTypeId.value, water.id)
}
const scrollHander = (e: any) => {
  if (!enableComputedScroll) return
  const titles = category.querySelectorAll('.title')
  titles.forEach((title: any) => {
    if (title.getBoundingClientRect().top < threshold) {
      currentTypeId.value = title.getAttribute('data-id')
      // console.log(222, currentTypeId.value, title)
    }
  })
}
const typeClickHanlder = () => {
  tabs = document.querySelector('.category-box .tabs')
  category = document.querySelector('.category-box .category')
  // console.log(tabs, tabsBtns, category)

  // 1、点击：监听锚点链接的点击事件，触发滚动；改成了锚链接
  // 2、滚动：滚动事件监听，同步更新锚点链接的选中状态
  const paddingTop = 42
  const tabsHeight = tabs?.offsetHeight // 32或64
  const titleHeight = 20
  const gap = 155
  threshold = tabsHeight + paddingTop + titleHeight + gap // 距离窗口顶部的阈值

  category?.addEventListener('scroll', scrollHander)
}

onMounted(() => typeClickHanlder())
onUnmounted(() => category?.removeEventListener('scroll', scrollHander))
// ==================== tabs级联滚动end ====================
</script>

<style lang="scss" scoped>
.category-box {
  height: 100%;
  width: calc(336px + 10px);

  .tabs {
    flex-wrap: wrap;
    margin-bottom: 12px;

    .tab {
      margin: 0 12px 8px 0;
      font-weight: 400;
      font-size: 14px;
      color: #666666;
      line-height: 20px;
      cursor: pointer;

      padding: 2px 14px;
      background: #f6f6f6;
      border-radius: 12px;
    }
    .selected {
      color: #fff;
      background-color: #006eff;
    }
  }

  .category {
    position: relative;
    // 32px 菜单栏高度，非固定，需要动态调整
    height: calc(100% - 32px);
    // height: 200px; // test
    padding-bottom: 10px;
    overflow-x: hidden;
    overflow-y: auto;

    .add-box-new {
      margin-bottom: 20px;
      .btn {
        width: 127px;
        height: 32px;
        border: 1px solid #dcdcdc;
        border-radius: 4px;

        font-weight: 400;
        font-size: 14px;
        color: #333333;
        align-items: center;
        cursor: pointer;

        &:hover {
          opacity: 0.7;
        }
      }

      .text {
        padding: 6px 0 6px 11px;

        img {
          width: 16px;
          height: 14px;
          margin-right: 4px;
        }
      }
      .img {
        padding: 6px 0 6px 7px;
        margin-left: 12px;

        img {
          width: 18px;
          height: 16px;
          margin-right: 6px;
        }
      }
    }

    .module {
      .title {
        margin-bottom: 12px;
        font-weight: 600; // electron
        font-size: 14px;
        color: #333333;
        line-height: 20px;
      }

      .list {
        width: 354px;
        flex-wrap: wrap;

        .item {
          position: relative;
          width: 96px;
          height: 96px;
          margin: 0 19px 20px 1px;
          // box-shadow: 0 0 0 1px #eaeaea;
          // background: #eaeaea;
          border-radius: 4px;
          flex-shrink: 0;
          justify-content: center;
          align-items: center;
          z-index: 0;
          overflow: hidden;
          cursor: pointer;

          &:hover {
            opacity: 0.7;
          }

          :nth-child(2n + 1) {
            margin-right: 0;
          }

          .watermark {
            width: 100%;

            img {
              -webkit-user-drag: none;
            }
          }
        }
      }
    }
  }
}
</style>
