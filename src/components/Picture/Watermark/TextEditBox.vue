<template>
  <section class="text-edit-box">
    <div class="header flex">
      <img
        class="back"
        src="/img/pic/second/<EMAIL>"
        alt=""
        @click="currentIndex = -1"
      />
      <img
        class="split"
        src="/img/pic/second/<EMAIL>"
        alt=""
        @click="currentIndex = -1"
      />
      <p class="title">水印详情</p>
      <div class="delete flex btn" @click="removeWatermark(currentIndex)">
        <img
          src="/img/pic/second/<EMAIL>"
          alt=""
          @click="currentIndex = -1"
        />
        <p>删除</p>
      </div>
    </div>
    <div class="content">
      <p class="title">内容</p>
      <el-input
        v-model="item.text"
        v-for="item in textList"
        maxlength="20"
        placeholder="请输入水印文字"
        show-word-limit
      ></el-input>
    </div>

    <div class="style">
      <p class="title">样式</p>
      <!-- 多行文字颜色 -->
      <div v-if="textList.length > 1" class="multi-color flex">
        <p class="tip">颜色</p>
        <el-popover
          ref="popover"
          placement="bottom"
          trigger="click"
          :offset="0"
          :show-arrow="false"
          popper-class="textEditBoxPopover customerPopover"
        >
          <template #reference>
            <div class="box flex">
              <div
                class="color"
                :style="{
                  backgroundColor: textList[0].style.color,
                }"
              ></div>
              <img
                class="down"
                src="/img/pic/second/<EMAIL>"
                alt=""
              />
            </div>
          </template>
          <template #default>
            <Color
              :color="textList[0].style.color"
              moduleType="multiColor"
              type="close"
              @change="changeColor"
            ></Color>
          </template>
        </el-popover>
      </div>
      <!-- 单行文字颜色等 -->
      <div v-else class="simple-color flex">
        <el-select
          popper-class="font-family-select"
          size="mini"
          v-model="textList[0].style.fontFamily"
          placeholder=""
        >
          <el-option
            v-for="item in fontFamilyList"
            :key="item.value"
            :label="item.name"
            :value="item.value"
            :style="{ fontFamily: item.value }"
          >
          </el-option>
        </el-select>
        <el-popover
          ref="popover"
          placement="bottom"
          trigger="click"
          :offset="0"
          :show-arrow="false"
          popper-class="textEditBoxPopover customerPopover"
        >
          <template #reference>
            <div class="box flex">
              <div
                class="color"
                :style="{
                  backgroundColor: textList[0].style.color,
                }"
              ></div>
              <img
                class="down"
                src="/img/pic/second/<EMAIL>"
                alt=""
              />
            </div>
          </template>
          <template #default>
            <Color
              :color="textList[0].style.color"
              moduleType="multiColor"
              type="close"
              @change="changeColor"
            ></Color>
          </template>
        </el-popover>
        <div
          class="styleBtn"
          :class="{ actived: textList[0].style.fontWeight === 'bold' }"
          @click="
            textList[0].style.fontWeight = textList[0].style.fontWeight
              ? ''
              : 'bold'
          "
        >
          <img src=" /img/mind_map/icon/<EMAIL>" alt="" />
        </div>
        <div
          class="styleBtn i"
          :class="{ actived: textList[0].style.fontStyle === 'italic' }"
          @click="
            textList[0].style.fontStyle = textList[0].style.fontStyle
              ? ''
              : 'italic'
          "
        >
          <img src=" /img/mind_map/icon/<EMAIL>" alt="" />
        </div>
        <div
          class="styleBtn u"
          :class="{ actived: textList[0].style.textDecoration === 'underline' }"
          :style="{
            textDecoration: 'underline',
          }"
          @click="
            textList[0].style.textDecoration =
              textList[0].style.textDecoration === 'underline'
                ? ''
                : 'underline'
          "
        >
          <img src=" /img/mind_map/icon/<EMAIL>" alt="" />
        </div>
        <div
          class="styleBtn s"
          :class="{
            actived: textList[0].style.textDecoration === 'line-through',
          }"
          :style="{
            textDecoration: 'line-through',
          }"
          @click="
            textList[0].style.textDecoration =
              textList[0].style.textDecoration === 'line-through'
                ? ''
                : 'line-through'
          "
        >
          <img src=" /img/mind_map/icon/<EMAIL>" alt="" />
        </div>
      </div>
      <div class="slider-action zoom flex">
        <div class="tip">大小</div>
        <!-- 20-400 -->
        <el-slider
          v-model="zoom"
          step="0.01"
          :show-tooltip="false"
          :min="minZoom"
          :max="2"
        />
        <div class="size">{{ (zoom * 100).toFixed(0) }}%</div>
      </div>
      <div class="slider-action opacity flex">
        <div class="tip">透明度</div>
        <el-slider
          v-model="opacity"
          step="0.2"
          :show-tooltip="false"
          :max="1"
        />
        <div class="size">{{ opacity * 100 }}%</div>
      </div>
      <div class="slider-action rotate flex">
        <div class="tip">旋转</div>
        <el-slider v-model="rotate" :show-tooltip="false" :max="360" />
        <div class="size">{{ Number(rotate).toFixed(0) }}°</div>
      </div>
    </div>
    <div class="position">
      <p class="title">位置</p>
      <el-radio-group
        v-model="watermarkPositionType"
        @change="changewatermarkPositionType"
      >
        <el-radio
          v-for="comKey in Object.keys(watermarkpositionRadio)"
          :key="comKey"
          :label="comKey"
        ></el-radio>
      </el-radio-group>

      <div
        v-if="watermarkPositionType === watermarkpositionRadio['九宫格']"
        class="sudoku"
      >
        <div class="wrap flex">
          <span
            v-for="(data, index) in sudokuData"
            :class="{
              currentSudoku: currentSudoku === index,
            }"
            @click="sudokuClick(data.x, data.y, index)"
          ></span>
        </div>
      </div>
      <div
        v-if="watermarkPositionType === watermarkpositionRadio['平铺']"
        class="tile"
      >
        <div class="slider-action opacity flex">
          <div class="tip">间距</div>
          <el-slider v-model="gap" :show-tooltip="false" :max="300" />
          <div class="size">{{ gap || 0 }}px</div>
        </div>
        <div class="slider-action rotate flex">
          <div class="tip">交错</div>
          <el-slider v-model="intersect" :show-tooltip="false" :max="100" />
          <div class="size">{{ intersect || 0 }}px</div>
        </div>
      </div>
    </div>
  </section>
</template>
<script setup lang="ts">
import { ref, computed } from 'vue'
import { storeToRefs } from 'pinia'
import { useWatermarkStore } from '@/pinia/watermark'
import { fontFamilyList } from '@/utils/enum'
import Color from '@/components/Picture/Color.vue'

const watermarkStore = useWatermarkStore()
let {
  minZoom,
  currentIndex,
  isGlobal,
  globalWatermark,
  currentWatermark,
  dialogWatermark,
  watermarkpositionRadio,
} = storeToRefs(watermarkStore)

// let dialogWatermark = computed(() =>
//   isGlobal.value
//     ? globalWatermark.value.customWatermark
//     : currentWatermark.value.customWatermark
// )
// let dialogWatermark = computed({
//   get() {
//     return watermarkStore.currentWatermark.customWatermark
//   },
//   set(value) {
//     watermarkStore.setCustomWatermark(value)
//   },
// })
const getCanvasStyleProperty = (property: string) => {
  return computed({
    get() {
      return (
        dialogWatermark.value[currentIndex.value]?.canvas?.style[property] || ''
      )
    },
    set(value) {
      const newCustomWatermark = [...dialogWatermark.value]
      if (newCustomWatermark[currentIndex.value]?.canvas?.style) {
        newCustomWatermark[currentIndex.value].canvas.style[property] = value
        watermarkStore.setDialogWatermark(newCustomWatermark)
      }
    },
  })
}
let watermarkPositionType = getCanvasStyleProperty('watermarkPositionType')
let zoom = getCanvasStyleProperty('zoom')
let opacity = getCanvasStyleProperty('opacity')
let rotate = getCanvasStyleProperty('rotate')
let gap = getCanvasStyleProperty('gap')
let intersect = getCanvasStyleProperty('intersect')

watermarkPositionType.value =
  watermarkPositionType.value || watermarkpositionRadio.value['自定义']

const textList: any = computed({
  get() {
    return (
      dialogWatermark.value[currentIndex.value].list.filter(
        (item: any, index: number) => {
          item.id = index
          return item.type === 'text'
        }
      ) || []
    )
  },
  set() {},
})
// textList.value[0].text = 'test'
// // textList 等亲测修改都同步了
// setInterval(() => {
//   console.log(
//     JSON.stringify(textList.value),
//     '====\r\n',
//     JSON.stringify(dialogWatermark.value[currentIndex.value]),
//     '====\r\n',
//     JSON.stringify(currentWatermark.value.customWatermark[currentIndex.value])
//   )
// }, 5000)

const sudokuData = [
  { x: 1, y: 1 },
  { x: 2, y: 1 },
  { x: 3, y: 1 },
  { x: 1, y: 2 },
  { x: 2, y: 2 },
  { x: 3, y: 2 },
  { x: 1, y: 3 },
  { x: 2, y: 3 },
  { x: 3, y: 3 },
]
let currentSudoku: any = ref(null)
const sudokuClick = (x: number, y: number, position: number) => {
  currentSudoku.value = position
  watermarkStore.sudoku(x, y)
}
const changewatermarkPositionType = (type: any) => {
  watermarkPositionType.value = type

  if (type !== watermarkpositionRadio.value['平铺']) {
    watermarkStore.updateView('textEditBox').then(() => {
      if (type === watermarkpositionRadio.value['九宫格']) {
        currentSudoku.value = currentSudoku.value || 8

        const { x, y } = sudokuData[currentSudoku.value]
        watermarkStore.sudoku(x, y)
      }
    })
  }
}
const changeColor = (color: any) => {
  dialogWatermark.value[currentIndex.value].list.forEach((item: any) => {
    if (item.type === 'text') {
      item.style.color = color
    }
  })
}
const removeWatermark = (index: number) => {
  console.log('removeWatermark', index)

  if (isGlobal.value) {
    globalWatermark.value.customWatermark.splice(index, 1)
  } else {
    currentWatermark.value.customWatermark.splice(index, 1)
  }
}
</script>

<style lang="scss">
.textEditBoxPopover {
  width: auto !important;
  padding: 12px 0 !important;
}
</style>
<style lang="scss" scoped>
.text-edit-box {
  margin-top: -10px;
  width: 339px;
  :deep(.el-slider) {
    height: 20px;
    width: 251px;

    .el-slider__runway {
      height: 3px;
      border-radius: 3px;
      background-color: #e4e4e6;

      .el-slider__bar {
        height: 3px !important;
        border-radius: 3px;
      }

      .el-slider__button {
        position: relative;
        width: 9px;
        height: 9px;
        top: -2px;
        border: #1890ff;
        background: #1890ff;
      }
    }
  }
  .header {
    margin-bottom: 4px;
    .back {
      width: 20px;
      height: 21px;
    }
    .split {
      width: 2px;
      height: 21px;
      margin: 0 12px;
    }
    .title {
      font-weight: 400;
      font-size: 14px;
      color: #333333;
      line-height: 21px;
    }
    .delete {
      margin-left: 24px;
      cursor: pointer;
      img {
        position: relative;
        top: 0.5px;
        width: 20px;
        height: 20px;
        margin-right: 4px;
      }
      font-weight: 400;
      font-size: 12px;
      color: #ff4c48;
      line-height: 21px;
    }
  }
  .title {
    font-weight: 600;
    font-size: 14px;
    color: #333333;
    line-height: 20px;
    margin-bottom: 12px;
  }
  .content {
    :deep(.el-input) {
      width: 339px;
      height: 32px;
      margin-bottom: 12px;

      input::placeholder {
        color: #999;
      }

      .el-input__wrapper {
        border-radius: 2px;
        background-color: #fff;
      }

      .el-input__inner {
        font-weight: 400;
        font-size: 14px;
        color: #000000;
        line-height: 22px;
      }
    }
  }

  // slider相关
  .tip {
    width: 42px;
    margin-right: 12px;
    font-weight: 400;
    font-size: 14px;
    color: #999999;
    line-height: 20px;
  }
  .slider-action {
    min-width: 336px;
    height: 20px;
    margin-bottom: 16px;
    user-select: none;

    .tip {
      height: 20px;
      flex-shrink: 0;

      font-weight: 400;
      font-size: 14px;
      color: #999999;
      line-height: 20px;
    }

    .size {
      width: 33px;
      margin-left: 12px;
      font-weight: 400;
      font-size: 12px;
      color: #333333;
      line-height: 17px;
      text-align: left;
      flex-shrink: 0;
      overflow: hidden;
    }
  }

  .style {
    .title {
      margin-top: 8px;
    }

    .simple-color,
    .multi-color {
      height: 32px;
      margin-bottom: 16px;

      .tip {
        line-height: 32px;
      }

      .box {
        width: 62px;
        height: 32px;
        padding: 5px 7px;
        background: #ffffff;
        border-radius: 2px;
        border: 1px solid rgba(0, 0, 0, 0.15);
        cursor: pointer;

        .color {
          width: 30px;
          height: 20px;
          border-radius: 2px;
        }

        .down {
          position: relative;
          left: 4px;
          top: 4px;
          width: 12px;
          height: 13px;
        }
      }
    }

    .simple-color {
      height: 32px;
      margin-bottom: 16px;

      :deep(.el-select) {
        width: 119px;
        height: 32px;
        border-radius: 2px;
        margin-right: 6px;

        .el-input__wrapper {
          border-radius: 2px;

          .el-input__inner {
            font-weight: 400;
            font-size: 14px;
            color: rgba(0, 0, 0, 0.65);
          }
        }
      }

      .box {
        margin-right: 6px;
      }

      .styleBtn {
        position: relative;
        width: 32px;
        height: 32px;
        margin-right: 6px;
        border-radius: 2px;
        background-color: #fff;
        border: 1px solid #e0e0e0;
        display: flex;
        justify-content: center;
        align-items: center;
        font-weight: bold;
        flex-shrink: 0;
        cursor: pointer;

        &:last-child {
          margin-right: 0;
        }

        img {
          width: 12px;
          height: 14px;
        }

        &.actived {
          background: rgba(220, 226, 237, 0.5);
        }

        &.i {
          font-style: italic;
        }
      }
    }
  }
  .position {
    padding-top: 4px;
    .title {
      margin-bottom: 16px;
    }
    .el-radio-group {
      margin-bottom: 16px;
    }

    .sudoku {
      .wrap {
        flex-wrap: wrap;
        width: 184px;
        margin-left: 44px;
        border-right: 1px solid #eaeaea;
        border-bottom: 1px solid #eaeaea;
        span {
          display: inline-block;
          width: 61px;
          height: 34px;
          border-left: 1px solid #eaeaea;
          border-top: 1px solid #eaeaea;
          cursor: pointer;
        }
        .currentSudoku {
          background: url(/img/pic/second/<EMAIL>)
            center center/contain no-repeat;
          background-size: 24px 17px;
        }
      }
    }
  }
}
</style>
