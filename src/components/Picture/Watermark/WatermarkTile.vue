<template>
  <div
    v-if="watermark"
    class="grid-container"
    :style="{
      gridTemplateColumns: `repeat(auto-fill, minmax(${
        watermark.canvas.style?.width * watermark.canvas.style?.zoom || 0
      }px, 1fr))`,
      gap: `${watermark.canvas.style?.gap || 0}px`,
    }"
  >
    <div
      v-for="(_, index) in list"
      :key="index"
      class="watermark-box"
      :style="{
        transform:
          Math.floor(index / columns) % 2 !== 0
            ? `translateX(${watermark.canvas.style?.intersect || 0}px)`
            : 'none',
        rotate: `${watermark.canvas.style?.rotate || 0}deg`,
        zIndex: currentIndex + 1,
      }"
    >
      <div
        class="watermark"
        :style="{
          width: `${
            watermark.canvas.style?.width * watermark.canvas.style?.zoom || 0
          }px`,
          height: `${
            watermark.canvas.style?.height * watermark.canvas.style?.zoom || 0
          }px`,
          opacity: watermark.canvas.style?.opacity,
          overflow: 'hidden',
        }"
      >
        <template v-for="(item, index) in watermark.list">
          <p
            v-if="item.type === 'text'"
            :style="{
              position: 'absolute',
              left: `${item.style.left}px`,
              top: `${item.style.top}px`,
              width: 'max-content',
              fontFamily: item.style.fontFamily,
              fontSize: `${item.style.fontSize}px`,
              color: item.style.color,
              fontWeight: item.style.fontWeight,
              fontStyle: item.style.fontStyle,
              textDecoration: item.style.textDecoration,
              rotate: `${item.style.rotate || 0}deg`,
              zoom: watermark.canvas.style?.zoom,
              zIndex: index,
            }"
          >
            {{ item.text?.slice(0, 20) }}
          </p>
          <img
            v-else-if="item.type === 'img'"
            :src="item.img"
            :style="{
              position: 'absolute',
              left: `${item.style.left}px`,
              top: `${item.style.top}px`,
              width: `${item.style.width * (item.style.zoom || 1)}px`,
              height: `${item.style.height * (item.style.zoom || 1)}px`,
              zIndex: index,
              zoom: watermark.canvas.style?.zoom,
              userSelect: 'none',
            }"
            alt=""
          />
        </template>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { storeToRefs } from 'pinia'
import { useWatermarkStore } from '@/pinia/watermark'

const watermarkStore = useWatermarkStore()
let { dialogWatermark, currentIndex } = storeToRefs(watermarkStore)

let watermark = dialogWatermark.value[currentIndex.value]

// 计算列数
const columns = computed(() => {
  const container: any = document.querySelector('.grid-container')
  const containerWidth = container?.offsetWidth || 0
  const itemWidth =
    watermark.canvas.style?.width * watermark.canvas.style?.zoom || 0
  const gap = watermark.canvas.style?.gap || 0
  return Math.floor(containerWidth / (itemWidth + gap))
})

const list = Array.from({ length: 50 }, (_, i) => i + 1)
</script>

<style lang="scss" scoped>
.grid-container {
  display: grid;
  position: absolute;
  left: -0%;
  top: -0%;
  width: 100%;
  z-index: 0;
}

.watermark-box {
  .watermark {
    position: relative;

    img {
      -webkit-user-drag: none;
    }
  }
}
</style>
