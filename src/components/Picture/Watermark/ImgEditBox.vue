<template>
  <section class="img-edit-box">
    <div class="header flex">
      <img
        class="back"
        src="/img/pic/second/<EMAIL>"
        alt=""
        @click="currentIndex = -1"
      />
      <img
        class="split"
        src="/img/pic/second/<EMAIL>"
        alt=""
        @click="currentIndex = -1"
      />
      <p class="title">水印详情</p>
      <div class="delete flex btn" @click="removeWatermark(currentIndex)">
        <img
          src="/img/pic/second/<EMAIL>"
          alt=""
          @click="currentIndex = -1"
        />
        <p>删除</p>
      </div>
    </div>
    <div class="img">
      <p class="title">图片</p>
      <el-button class="btn add-pic" type="primary" @click="triggerChoose">
        <img src="/img/mind_map/icon/<EMAIL>" />
        <span>重新选择</span>
      </el-button>
    </div>
    <div class="style">
      <p class="title">样式</p>
      <div class="slider-action zoom flex">
        <div class="tip">大小</div>
        <!-- 20-400 -->
        <el-slider
          v-model="zoom"
          step="0.01"
          :show-tooltip="false"
          :min="minZoom"
          :max="2"
        />
        <div class="size">{{ (zoom * 100).toFixed(0) }}%</div>
      </div>
      <div class="slider-action opacity flex">
        <div class="tip">透明度</div>
        <el-slider
          v-model="opacity"
          step="0.2"
          :show-tooltip="false"
          :max="1"
        />
        <div class="size">{{ opacity * 100 }}%</div>
      </div>
      <div class="slider-action rotate flex">
        <div class="tip">旋转</div>
        <el-slider v-model="rotate" :show-tooltip="false" :max="360" />
        <div class="size">{{ Number(rotate).toFixed(0) }}°</div>
      </div>
    </div>
    <div class="position">
      <p class="title">位置</p>
      <el-radio-group
        v-model="watermarkPositionType"
        @change="changewatermarkPositionType"
      >
        <el-radio
          v-for="comKey in Object.keys(watermarkpositionRadio)"
          :key="comKey"
          :label="comKey"
        ></el-radio>
      </el-radio-group>

      <div
        v-if="watermarkPositionType === watermarkpositionRadio['九宫格']"
        class="sudoku"
      >
        <div class="wrap flex">
          <span
            v-for="(data, index) in sudokuData"
            :class="{
              currentSudoku: currentSudoku === index,
            }"
            @click="sudokuClick(data.x, data.y, index)"
          ></span>
        </div>
      </div>
      <div
        v-if="watermarkPositionType === watermarkpositionRadio['平铺']"
        class="tile"
      >
        <div class="slider-action opacity flex">
          <div class="tip">间距</div>
          <el-slider v-model="gap" :show-tooltip="false" :max="300" />
          <div class="size">{{ gap || 0 }}px</div>
        </div>
        <div class="slider-action rotate flex">
          <div class="tip">交错</div>
          <el-slider v-model="intersect" :show-tooltip="false" :max="100" />
          <div class="size">{{ intersect || 0 }}px</div>
        </div>
      </div>
    </div>

    <input
      class="hide"
      type="file"
      name="file"
      id="imageEditBoxImgUpload"
      accept="image/*"
      @change="chooseImage($event)"
    />
  </section>
</template>
<script setup lang="ts">
import { ref, computed } from 'vue'
import { storeToRefs } from 'pinia'
import { useWatermarkStore } from '@/pinia/watermark'
import {
  // getImageSize,
  compressFile,
} from '@/utils'

const watermarkStore = useWatermarkStore()
let {
  minZoom,
  currentIndex,
  isGlobal,
  globalWatermark,
  currentWatermark,
  dialogWatermark,
  watermarkpositionRadio,
} = storeToRefs(watermarkStore)

const getCanvasStyleProperty = (property: string) => {
  return computed({
    get() {
      return (
        dialogWatermark.value[currentIndex.value]?.canvas?.style[property] || ''
      )
    },
    set(value) {
      const newCustomWatermark = [...dialogWatermark.value]
      if (newCustomWatermark[currentIndex.value]?.canvas?.style) {
        newCustomWatermark[currentIndex.value].canvas.style[property] = value
        watermarkStore.setDialogWatermark(newCustomWatermark)
      }
    },
  })
}
let watermarkPositionType = getCanvasStyleProperty('watermarkPositionType')
let zoom = getCanvasStyleProperty('zoom')
let opacity = getCanvasStyleProperty('opacity')
let rotate = getCanvasStyleProperty('rotate')
let gap = getCanvasStyleProperty('gap')
let intersect = getCanvasStyleProperty('intersect')
let canvasWidth = getCanvasStyleProperty('width')
let canvasHeight = getCanvasStyleProperty('height')

watermarkPositionType.value =
  watermarkPositionType.value || watermarkpositionRadio.value['自定义']

const imgList: any = computed({
  get() {
    return (
      dialogWatermark.value[currentIndex.value].list.filter(
        (item: any, index: number) => {
          item.id = index
          return item.type === 'img'
        }
      ) || []
    )
  },
  set() {},
})
const triggerChoose = (e: any) => {
  const choose = document.getElementById('imageEditBoxImgUpload')
  if (choose) {
    // 解决文件不能重复上传问题
    choose.setAttribute('type', 'text')
    choose.setAttribute('type', 'file')
    choose.click()
  }
}
const chooseImage = (e: any) => {
  upload(e)
}
const upload = (e: any) => {
  const file = e.target.files[0]
  // const blob: any = new Blob([file], {
  //   type: file.type,
  // })
  // const url = window.URL.createObjectURL(file)

  const maxWidth = 200
  const maxHeight = 200
  compressFile(
    {
      file,
      limitSize: 500,
    },
    (_: any) => {
      const { rate } = _
      const img = imgList.value[0]
      const style = img.style
      style.width = rate > 1 ? maxWidth : maxHeight * rate
      style.height = rate > 1 ? maxWidth / rate : maxHeight
      style.left = (maxWidth - style.width) / 2
      style.top = (maxHeight - style.height) / 2
      img.img = _.base64

      if (style.width > 120 || style.height > 120) {
        canvasWidth.value = maxWidth
        canvasHeight.value = maxHeight
      }
    }
  )
  // console.log(url, style)
}

const sudokuData = [
  { x: 1, y: 1 },
  { x: 2, y: 1 },
  { x: 3, y: 1 },
  { x: 1, y: 2 },
  { x: 2, y: 2 },
  { x: 3, y: 2 },
  { x: 1, y: 3 },
  { x: 2, y: 3 },
  { x: 3, y: 3 },
]
let currentSudoku: any = ref(null)
const sudokuClick = (x: number, y: number, position: number) => {
  currentSudoku.value = position
  watermarkStore.sudoku(x, y)
}
const changewatermarkPositionType = (type: any) => {
  watermarkPositionType.value = type

  if (type !== watermarkpositionRadio.value['平铺']) {
    watermarkStore.updateView('textEditBox').then(() => {
      if (type === watermarkpositionRadio.value['九宫格']) {
        currentSudoku.value = currentSudoku.value || 8

        const { x, y } = sudokuData[currentSudoku.value]
        watermarkStore.sudoku(x, y)
      }
    })
  }
}
const removeWatermark = (index: number) => {
  console.log('removeWatermark', index)

  if (isGlobal.value) {
    globalWatermark.value.customWatermark.splice(index, 1)
  } else {
    currentWatermark.value.customWatermark.splice(index, 1)
  }
}
</script>

<style lang="scss" scoped>
@import '@/assets/css/picture-btns.scss';
.img-edit-box {
  margin-top: -10px;
  width: 339px;
  :deep(.el-slider) {
    height: 20px;
    width: 251px;

    .el-slider__runway {
      height: 3px;
      border-radius: 3px;
      background-color: #e4e4e6;

      .el-slider__bar {
        height: 3px !important;
        border-radius: 3px;
      }

      .el-slider__button {
        position: relative;
        width: 9px;
        height: 9px;
        top: -2px;
        border: #1890ff;
        background: #1890ff;
      }
    }
  }
  .header {
    margin-bottom: 4px;
    .back {
      width: 20px;
      height: 21px;
    }
    .split {
      width: 2px;
      height: 21px;
      margin: 0 12px;
    }
    .title {
      font-weight: 400;
      font-size: 14px;
      color: #333333;
      line-height: 21px;
    }
    .delete {
      margin-left: 24px;
      cursor: pointer;
      img {
        position: relative;
        top: 0.5px;
        width: 20px;
        height: 20px;
        margin-right: 4px;
      }
      font-weight: 400;
      font-size: 12px;
      color: #ff4c48;
      line-height: 21px;
    }
  }
  .title {
    font-weight: 600;
    font-size: 14px;
    color: #333333;
    line-height: 20px;
    margin-bottom: 12px;
  }
  .img {
    .add-pic {
      height: 32px;
      font-size: 14px;
      padding: 6px 10px;
      border-radius: 4px;
      color: #fff;
      margin-right: 4px;
      border: 0;
      background: #389bfd;

      img {
        position: relative;
        width: 15px;
        height: 15px;
        right: 4px;
      }

      span {
        line-height: 20px;
      }
    }
  }

  // slider相关
  .tip {
    width: 42px;
    margin-right: 12px;
    font-weight: 400;
    font-size: 14px;
    color: #999999;
    line-height: 20px;
  }
  .slider-action {
    min-width: 336px;
    height: 20px;
    margin-bottom: 16px;
    user-select: none;

    .tip {
      height: 20px;
      flex-shrink: 0;

      font-weight: 400;
      font-size: 14px;
      color: #999999;
      line-height: 20px;
    }

    .size {
      width: 33px;
      margin-left: 12px;
      font-weight: 400;
      font-size: 12px;
      color: #333333;
      line-height: 17px;
      text-align: left;
      flex-shrink: 0;
      overflow: hidden;
    }
  }

  .style {
    .title {
      margin-top: 8px;
    }
  }

  .position {
    padding-top: 4px;
    .title {
      margin-bottom: 16px;
    }
    .el-radio-group {
      margin-bottom: 16px;
    }

    .sudoku {
      .wrap {
        flex-wrap: wrap;
        width: 184px;
        margin-left: 44px;
        border-right: 1px solid #eaeaea;
        border-bottom: 1px solid #eaeaea;
        span {
          display: inline-block;
          width: 61px;
          height: 34px;
          border-left: 1px solid #eaeaea;
          border-top: 1px solid #eaeaea;
          cursor: pointer;
        }
        .currentSudoku {
          background: url(/img/pic/second/<EMAIL>)
            center center/contain no-repeat;
          background-size: 24px 17px;
        }
      }
    }
  }
}
</style>
