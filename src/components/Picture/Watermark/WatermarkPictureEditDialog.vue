<template>
  <div
    class="mask"
    :style="{
      zIndex: 2000,
    }"
  />
  <div
    v-loading="loading"
    class="watermark-dialog"
    :style="{
      zIndex: 2001,
    }"
  >
    <div class="info flex">
      <div class="left flex chessboard-bg">
        <img
          v-if="isGlobal"
          class="demo-img"
          src="/img/pic/second/<EMAIL>"
          alt=""
        />
        <div class="img-box">
          <!-- 图片画布 -->
          <img
            class="canvas-img"
            :style="{
              width: canvasImgWidth + 'px',
              height: canvasImgHeight + 'px',
            }"
            :src="url"
            alt=""
            @click="currentIndex = -1"
          />
          <!-- 图片水印 -->
          <Watermark></Watermark>
        </div>
      </div>

      <div class="right">
        <img
          class="close"
          @click="closeHandler"
          src="/img/mind_map/table/<EMAIL>"
          alt=""
        />
        <TextEditBox v-if="currentIndex > -1 && hasText()"></TextEditBox>
        <ImgEditBox v-else-if="currentIndex > -1 && isCustom()"></ImgEditBox>
        <CategoryBox v-else></CategoryBox>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { onMounted, onUnmounted } from 'vue'
import { storeToRefs } from 'pinia'
import { useWatermarkStore } from '@/pinia/watermark'
import { usePictureStore } from '@/pinia/picture'
import Watermark from '@/components/Picture/Watermark/Watermark.vue'
import TextEditBox from '@/components/Picture/Watermark/TextEditBox.vue'
import ImgEditBox from '@/components/Picture/Watermark/ImgEditBox.vue'
import CategoryBox from '@/components/Picture/Watermark/CategoryBox.vue'

const watermarkStore = useWatermarkStore()
const pictureStore = usePictureStore()
let {
  canvasImg,
  canvasImgWidth,
  canvasImgHeight,
  currentWatermark,
  dialogWatermark,
  currentIndex,
  loading,
  isGlobal,
  showWatermarkPictureEditDialog,
} = storeToRefs(watermarkStore)
const { imgsMap } = storeToRefs(pictureStore)

// 重置
canvasImg.value = null
watermarkStore.updateCanvasImgSize()

const hasText = () => {
  return dialogWatermark.value[currentIndex.value]?.list?.some(
    (item: any) => item.type === 'text'
  )
}
const isCustom = () => {
  return dialogWatermark.value[currentIndex.value]?.canvas?.isCustom
}

const closeHandler = () => {
  window.location.hash = ''
  showWatermarkPictureEditDialog.value = false
  currentIndex.value = -1
  watermarkStore.updateWatermarkAll(imgsMap.value)
}

const resizeHandler = (e: UIEvent) => {
  watermarkStore.updateView('watermarkPictureEditDialog resizeHandler')
}

// ==== init ====
const url = isGlobal.value
  ? '/img/pic/second/<EMAIL>' // 默认图
  : URL.createObjectURL(currentWatermark.value?.file) // 当前选中图

onMounted(() => {
  watermarkStore.updateView('watermarkPictureEditDialog onMounted')
  window.addEventListener('resize', resizeHandler)
})
onUnmounted(() => {
  !isGlobal.value && URL.revokeObjectURL(url)
  window.removeEventListener('resize', resizeHandler)
})
</script>

<style lang="scss" scoped>
.watermark-dialog {
  position: fixed;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  width: 984px;
  border-radius: 4px;
  z-index: 2001;
  user-select: none;

  .info {
    position: fixed;
    top: 50%;
    left: 50%;
    -webkit-transform: translate(-50%, -50%);
    transform: translate(-50%, -50%);
    text-align: center;

    width: 984px;
    height: 646px;
    border-radius: 4px;
    background: #fff;
    @include common-dialog;

    .left {
      position: relative;
      width: 592px;
      height: 646px;
      // background: url(/img/pic/second/<EMAIL>);
      // background-size: contain;
      justify-content: center;
      align-items: center;
      flex-shrink: 0;

      .demo-img {
        position: absolute;
        width: 116px;
        height: 40px;
        top: 20px;
        left: 0;
        z-index: 1;
        -webkit-user-drag: none;
      }

      .img-box {
        position: relative;
        overflow: hidden;

        // 图片画布
        .canvas-img {
          -webkit-user-drag: none;
        }
      }
    }
    .right {
      position: relative;
      width: 392px;
      height: 646px;
      padding: 42px 28px 32px;
      text-align: left;
      background: #ffffff;
      overflow: hidden;

      .close {
        position: absolute;
        right: 14px;
        top: 14px;
        margin-bottom: 25px;
        height: 20px;
        width: 20px;
        cursor: pointer;

        &:hover {
          opacity: 0.7;
        }
      }
    }
  }
}
</style>
