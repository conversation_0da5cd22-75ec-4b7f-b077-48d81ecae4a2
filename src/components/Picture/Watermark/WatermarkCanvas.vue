<template>
  <template v-for="(watermark, index) in dialogWatermark">
    <WatermarkTile
      v-if="
        watermark.canvas.style?.watermarkPositionType ===
        watermarkpositionRadio['平铺']
      "
      @click.stop="currentIndex = index"
    ></WatermarkTile>
    <div
      v-else
      class="watermark-box"
      :style="{
        position: 'absolute',
        left: `${draggables[index]?.position?.x - canvasImgRect?.left || 0}px`,
        top: `${draggables[index]?.position?.y - canvasImgRect?.top || 0}px`,
        transform: `rotate(${watermark.canvas.style?.rotate || 0}deg)`,
        zIndex: index + 1,
      }"
    >
      <!-- 操作按钮 -->
      <div
        class="dot left-top"
        :style="{
          left: `-3px`,
          top: `-3px`,
          cursor: 'nwse-resize',
          visibility: getVisible(index),
        }"
        @click.stop="currentIndex = index"
        @mousedown="startResize($event, index)"
      ></div>
      <div
        class="dot left-bottom"
        :style="{
          left: `-3px`,
          top: `${-3 + Number(getWatermarkHeight(watermark))}px`,
          cursor: 'nesw-resize',
          visibility: getVisible(index),
        }"
        @click.stop="currentIndex = index"
        @mousedown="startResize($event, index)"
      ></div>
      <div
        class="dot right-bottom"
        :style="{
          left: `${-3 + getWatermarkWidth(watermark, index)}px`,
          top: `${-3 + Number(getWatermarkHeight(watermark))}px`,
          cursor: 'nwse-resize',
          visibility: getVisible(index),
        }"
        @click.stop="currentIndex = index"
        @mousedown="startResize($event, index)"
      ></div>
      <div
        v-if="currentIndex === index"
        class="rotate"
        :style="{
          left: `${(getWatermarkWidth(watermark, index) - 24) / 2}px`,
          top: `-36px`,
          visibility: getVisible(index),
        }"
        @click.stop="currentIndex = index"
        @mousedown="startRotate($event, index)"
      >
        <img src="/img/pic/second/<EMAIL>" alt="" />
      </div>
      <img
        class="delete"
        :style="{
          visibility: getVisible(index),
        }"
        src="/img/mind_map/table/<EMAIL>"
        alt=""
        @click.stop="removeWatermark(index)"
      />
      <!-- 水印内容 -->
      <canvas
        class="watermark"
        :class="{ selected: currentIndex === index }"
        :ref="(el: any) => (watermarkRefs[index] = el)"
        :width="`${getWatermarkWidth(watermark, index)}`"
        :height="`${getWatermarkHeight(watermark)}`"
        @click.stop="currentIndex = index"
      >
      </canvas>
    </div>
  </template>
</template>

<script setup lang="ts">
import { computed, onMounted, onUnmounted, watch } from 'vue'
import { storeToRefs } from 'pinia'
// import fabric from 'fabric'
import { useWatermarkStore } from '@/pinia/watermark'
import WatermarkTile from '@/components/Picture/Watermark/WatermarkTile.vue'

const watermarkStore = useWatermarkStore()
let {
  draggables,
  watermarkRefs,
  canvasImgRect,
  currentWatermark,
  globalWatermark,
  dialogWatermark,
  currentIndex,
  minZoom,
  isGlobal,
  showWatermarkPictureEditDialog,
  watermarkpositionRadio,
} = storeToRefs(watermarkStore)

const getCanvasStyleProperty = (property: string) => {
  return computed({
    get() {
      return (
        dialogWatermark.value[currentIndex.value]?.canvas?.style[property] || ''
      )
    },
    set(value) {
      const newCustomWatermark = [...dialogWatermark.value]
      if (newCustomWatermark[currentIndex.value]?.canvas?.style) {
        newCustomWatermark[currentIndex.value].canvas.style[property] = value
        watermarkStore.setDialogWatermark(newCustomWatermark)
      }
    },
  })
}
let widthCanvas = getCanvasStyleProperty('width')
let heightCanvas = getCanvasStyleProperty('height')
let zoomCanvas = getCanvasStyleProperty('zoom')

const getVisible = (index: number) =>
  currentIndex.value === index ? 'visible' : 'hidden'

const removeWatermark = (index: number) => {
  console.log('removeWatermark', index)

  if (isGlobal.value) {
    globalWatermark.value.customWatermark.splice(index, 1)
  } else {
    currentWatermark.value.customWatermark.splice(index, 1)
  }

  watermarkStore.updateView('watermark removeWatermark')
}
const getWatermarkWidth = (watermark: any, index: number) => {
  if (watermark.canvas?.isCustom) {
    const padding = 10
    if (watermark.canvas?.isText) {
      const width =
        watermarkRefs.value[index]?.querySelector('p')?.offsetWidth *
        watermark.canvas.style?.zoom
      // console.log('isText', width)
      return width + padding
    } else {
      // console.log('isImg')
      // nextTick(() => {
      //   const width =
      //     watermarkRefs.value[0]?.querySelector('img')?.offsetWidth *
      //     watermark.canvas.style?.zoom
      //   console.log(width + padding)
      //   // watermark.canvas.style.width = width + padding
      //   // watermark.canvas.style.height = width + padding
      //   return width + padding
      // })
    }
  }
  return watermark.canvas.style?.width * watermark.canvas.style?.zoom
}
const getWatermarkHeight = (watermark: any, index?: number) => {
  return watermark.canvas.style?.height * watermark.canvas.style?.zoom || 0
}

// watch(
//   () => dialogWatermark.value,
//   (newValue, oldValue) => {
//     console.log('dialogWatermark change', newValue, oldValue)
//     renderWatermark()
//   }
// )

// 1、动态生成canvas，挂载到 dialogWatermark？
// 2、改成单个水印，每次添加、修该都处理一个
// 3、rotate、zoom通过canvas处理
const renderWatermark = () => {
  dialogWatermark.value.forEach((watermark: any, index: any) => {
    const canvas: any = watermarkRefs.value[index]
    const ctx = canvas.getContext('2d')

    // 清除画布内容
    // ctx.clearRect(0, 0, canvas.width, canvas.height);

    // 处理旋转
    const angle = watermark.canvas.style.rotate || 0
    ctx.rotate((angle * Math.PI) / 180)

    watermark.list.forEach((item: any, index: any) => {
      ctx.save() // 保存初始状态
      if (item.type === 'text') {
        // 设置文本样式
        ctx.font = `${item.style.fontWeight || 'normal'} ${
          item.style.fontStyle || 'normal'
        } ${item.style.fontSize || 20}px ${item.style.fontFamily || 'Arial'}`
        ctx.fillStyle = item.style.color || '#000'
        ctx.textDecoration = item.style.textDecoration || 'none'

        // 处理旋转
        const angle = item.style.rotate || 0
        // ctx.translate(item.style.left, item.style.top);
        ctx.rotate((angle * Math.PI) / 180)

        // 绘制文本
        ctx.fillText(
          item.text?.slice(0, 20) || '',
          item.style.left,
          item.style.top + item.style.fontSize
        )
        ctx.restore()
      } else {
        var img = new Image()
        img.crossOrigin = 'Anonymous'
        img.onload = function () {
          ctx.drawImage(
            img,
            item.style.left,
            item.style.top,
            item.style.width * (item.style.zoom || 1),
            item.style.height * (item.style.zoom || 1)
          )
          ctx.restore()
        }
        img.src = item.img
      }
    })
  })
}
setTimeout(() => renderWatermark(), 3000)

// ================== 缩放 ====================
// 定义初始尺寸和位置
let initialSize = { width: 0, height: 0 }
let initialPosition = { x: 0, y: 0 }
let className = ''
let initialZoom = 1 // 初始化局部缩放变量，基于当前的 zoomCanvas.value
let localZoom = 1 // 初始化局部缩放变量，基于当前的 zoomCanvas.value

const startResize = (event: any, index: number) => {
  event.preventDefault()
  event.stopPropagation()
  initialSize.width = widthCanvas.value * zoomCanvas.value || 0
  initialSize.height = heightCanvas.value * zoomCanvas.value || 0
  initialPosition.x = event.clientX
  initialPosition.y = event.clientY
  className = event.target?.className || ''
  initialZoom = zoomCanvas.value // 记录开始缩放时的 zoom 值

  window.addEventListener('mousemove', resizeWatermark)
  window.addEventListener('mouseup', stopResize)
}
const resizeWatermark = (event: MouseEvent) => {
  const deltaX = event.clientX - initialPosition.x
  const deltaY = event.clientY - initialPosition.y

  // 根据不同的类名，计算新的缩放比例
  let zoomChange = 0
  if (className.includes('right-bottom')) {
    zoomChange = deltaX / initialSize.width
  } else if (className.includes('left-top')) {
    zoomChange = -deltaX / initialSize.width
  } else if (className.includes('left-bottom')) {
    // 这里需要根据实际情况计算高度的变化
    zoomChange = deltaY / initialSize.height
  }

  localZoom = initialZoom * (1 + zoomChange)
  localZoom = Math.max(minZoom.value, Math.min(localZoom, 2.0))
  zoomCanvas.value = localZoom
}
const stopResize = () => {
  window.removeEventListener('mousemove', resizeWatermark)
  window.removeEventListener('mouseup', stopResize)

  // renderWatermark()

  showWatermarkPictureEditDialog.value &&
    watermarkStore.updateView('watermark stopResize')
}

// ============ 旋转 =================
// 定义初始角度和位置
let initialAngle = 0
let initialMouseAngle = 0
let rotateOrigin = { x: 0, y: 0 }
let newAngle = 0 // 存储旋转角度

const normalizeAngle = (angle: any) => {
  return (angle + 360) % 360
}

const startRotate = (event: MouseEvent, index: number) => {
  event.preventDefault()
  event.stopPropagation()

  const zoom = dialogWatermark.value[index].canvas?.style.zoom || 1
  const rect = watermarkRefs.value[index].getBoundingClientRect()
  rotateOrigin = {
    x: rect.left + (rect.width * zoom) / 2,
    y: rect.top + (rect.height * zoom) / 2,
  }
  initialAngle = normalizeAngle(
    dialogWatermark.value[index].canvas?.style.rotate || 0
  )
  newAngle = initialAngle

  const deltaX = event.clientX - rotateOrigin.x
  const deltaY = event.clientY - rotateOrigin.y
  initialMouseAngle = Math.atan2(deltaY, deltaX) * (180 / Math.PI)

  window.addEventListener('mousemove', rotateWatermark)
  window.addEventListener('mouseup', stopRotate)
}

const rotateWatermark = (event: MouseEvent) => {
  const deltaX = event.clientX - rotateOrigin.x
  const deltaY = event.clientY - rotateOrigin.y

  const currentMouseAngle = Math.atan2(deltaY, deltaX) * (180 / Math.PI)
  const angleDifference = currentMouseAngle - initialMouseAngle
  newAngle = normalizeAngle(initialAngle + angleDifference)

  dialogWatermark.value[currentIndex.value].canvas.style.rotate = newAngle
}

const stopRotate = () => {
  window.removeEventListener('mousemove', rotateWatermark)
  window.removeEventListener('mouseup', stopRotate)

  // renderWatermark()

  showWatermarkPictureEditDialog.value &&
    watermarkStore.updateView('watermark stopRotate')
}

onMounted(() => {})
onUnmounted(() => {
  stopResize()
  stopRotate()
})
</script>

<style lang="scss" scoped>
.watermark-box {
  .dot {
    position: absolute;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #1890ff;
    border: 1px solid #ffffff;
    z-index: 10;
    visibility: hidden;
  }

  .rotate {
    position: absolute;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background: #ffffff;
    visibility: hidden;
    cursor: pointer;
    @include common-shadow;

    img {
      width: 12px;
      height: 12px;
      position: relative;
      top: -0.5px;
      -webkit-user-drag: none;
    }
  }

  .delete {
    position: absolute;
    right: -11px;
    top: -11px;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background-color: #fff;
    z-index: 10;
    visibility: hidden;
    cursor: pointer;
  }

  .watermark {
    position: relative;
    border: 1px dashed transparent;
    box-sizing: content-box;
    cursor: move;

    img {
      -webkit-user-drag: none;
    }
  }
  .selected {
    border-color: #fff;
  }
}
</style>
