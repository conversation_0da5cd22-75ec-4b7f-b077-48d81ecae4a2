<template>
  <el-popover
    popper-class="newFormatPopover"
    effect="light"
    placement="bottom"
    trigger="click"
    :show-arrow="false"
    :hide-after="0"
  >
    <template #reference>
      <p v-if="isAll" class="format btn">
        <span>{{ newFormatAll }}</span>
        <img class="arrow" src="/img/pic/public/<EMAIL>" alt="" />
      </p>
      <p v-else class="wait btn">
        <img src="/img/pic/index/<EMAIL>" alt="" />
        <span>格式设置</span>
      </p>
    </template>
    <template #default>
      <el-input
        class="search"
        v-model="newFormatBySearch"
        style="width: 357px"
        size="large"
        placeholder="输入搜索关键词"
        :prefix-icon="Search"
        :maxlength="9"
        clearable
      />
      <div v-if="imgExtsFilter.length" class="exts" v-scroll-effect>
        <span
          class="ext"
          :class="{
            current:
              (isAll ? newFormatAll : props.imgMap[1].record.newFormat) === ext,
          }"
          v-for="ext in imgExtsFilter"
          @click="
            isAll
              ? newFormatAllChange(ext)
              : newFormatChange(props.imgMap[1], ext)
          "
          >{{ ext }}</span
        >
      </div>
      <div v-else class="no-result">
        <img src="/img/pic/icon/icon_search_ <EMAIL>" alt="" />
        <p>暂无数据</p>
      </div>
      <div class="shade"></div>
    </template>
  </el-popover>
</template>

<script lang="ts" setup>
import { computed } from 'vue'
import { storeToRefs } from 'pinia'
import { usePictureStore } from '@/pinia/picture'
import { Search } from '@element-plus/icons-vue'
import { imgExts } from '@/utils/enum/configData'
const pictureStore = usePictureStore()
let { newFormatAll, newFormatBySearch } = storeToRefs(pictureStore)
const newFormatChange = pictureStore.newFormatChange
const newFormatAllChange = pictureStore.newFormatAllChange

const props = defineProps({
  type: {
    type: String,
    default: 'all', // all、simple
  },
  imgMap: {
    type: Object,
    default: {},
  },
})
const isAll = props.type === 'all'

const imgExtsUpperCase = imgExts.map((_) => _.toLowerCase())
// 根据搜索筛选
const imgExtsFilter = computed(() => {
  // heic格式转换目前只支持jpg、jpeg、png
  if (props.imgMap[1]?.record?.originFormat?.toLowerCase() === 'heic') {
    return ['jpg', 'jpeg', 'png']
  }
  if (newFormatBySearch.value) {
    return imgExtsUpperCase.filter((ext) =>
      ext.includes(newFormatBySearch.value.toLowerCase())
    )
  }
  return imgExtsUpperCase
})
</script>

<style lang="scss">
.newFormatPopover {
  position: relative;
  width: 405px !important;
  height: 216px !important;
  padding: 16px 24px !important;
  background: #ffffff;
  @include common-dialog;
  // box-shadow: 0px 12px 48px 16px rgba(0, 0, 0, 0.03), 0px 9px 28px 0px rgba(0, 0, 0, 0.05), 0px 6px 16px -8px rgba(0, 0, 0, 0.08);
  border-radius: 2px;
  z-index: 4000 !important;
  user-select: none;

  .search {
    width: 357px;
    height: 32px;
    margin-bottom: 12px;
    font-size: 14px;
    border-radius: 2px;
    color: #999999;
    background: #ffffff;
    border: 1px solid rgba(0, 0, 0, 0.15);
  }
  .el-input__wrapper {
    box-shadow: 0 0 0 0 !important;
  }
  .exts {
    height: 140px !important;
    text-align: left;
    overflow-y: auto;
    .ext {
      display: inline-block;
      padding: 0 12px;
      margin: 0 8px 12px 0;
      height: 26px;
      line-height: 24px;
      border-radius: 2px;
      border: 1px solid #dcdcdc;

      font-size: 14px;
      color: #333;
      cursor: pointer;
    }
    .current {
      color: #fff;
      background-color: #389bfd;
      border-color: #389bfd;
    }
  }
  .no-result {
    text-align: center;
    img {
      width: 33px;
      height: 33px;
      margin: 34px 0 8px;
    }

    font-size: 14px;
    color: #333333;
  }
  .shade {
    position: absolute;
    bottom: 14px;
    height: 44px;
    width: calc(100% - 48px);
    background: linear-gradient(
      180deg,
      rgba(255, 255, 255, 0) 0%,
      #ffffff 100%
    );
    border-radius: 2px;
    background: linear-gradient(
      180deg,
      rgba(255, 255, 255, 0) 0%,
      #ffffff 100%
    );
    pointer-events: none;
  }
}
</style>
<style lang="scss" scoped>
.picture-footer {
  .format {
    position: relative;
    padding: 3px 8px;
    margin-left: 8px;
    width: 140px;
    height: 28px;
    border-radius: 2px;
    border: 1px solid #e0e0e0;
    user-select: none;
    cursor: pointer;

    font-size: 14px;
    color: #333333;
    line-height: 20px;
    text-align: left;

    .arrow {
      position: absolute;
      top: 10px;
      right: 11px;
      width: 8px;
      height: 5px;
    }
  }
}

.pictureConvert {
  .status {
    .wait {
      color: #389bfd;
      img {
        position: relative;
        top: -2px;
        width: 16px;
        height: 16px;
        line-height: 16px;
      }
      span {
        display: inline-block;
        line-height: 20px;
        margin-left: 3px;
      }
      cursor: pointer;
    }
  }
}
</style>
