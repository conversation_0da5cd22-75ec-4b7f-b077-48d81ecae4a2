<template>
  <section v-show="!imgsMap.size" class="picture-add">
    <div class="drag-area">
      <div class="drag-area-main">
        <!-- <el-upload class="upload-demo" action="https://run.mocky.io/v3/9d059bf9-4660-45f2-925d-ce80ad6c4d15" drag
            multiple :auto-upload="false" :show-file-list="false" :accept="isElectron ? '' : 'image/*, .pdf'"
            @change="onDragChange"></el-upload> -->
        <div class="add-icon center">
          <img src="/img/pic/index/<EMAIL>" alt="" />
        </div>
        <div class="desc">
          <span>将图片拖拽到此处，或点击下方按钮</span>
        </div>
        <div class="picture-btns center">
          <el-button
            class="btn add-pic"
            type="primary"
            @click="(e: any) => isElectron ? electronUpload(e) : webUpload(e)"
          >
            <img src="/img/mind_map/icon/<EMAIL>" />
            <span>添加图片</span>
          </el-button>
          <el-button
            v-if="isElectron"
            class="btn add-folder"
            @click="(e: any) => isElectron ? electronUploadFolder(e) : webUploadFolder(e)"
            ><img
              class="templateIcon"
              src="/img/pic/index/<EMAIL>"
            />添加文件夹
          </el-button>
        </div>
        <div class="desc-info">
          {{
            props.imgExts.length
              ? `支持${props.imgExts.join('、')}格式的图片`
              : '支持格式：jpg、jpeg、png、bmp、tif、tiff、webp、pdf、gif、psd、svg、art、aai、avif、avs、bmp2、bmp3、cgm、cin、dcx、dds、dib、dpx、epdf、epi等百余种'
          }}
        </div>
      </div>
    </div>
  </section>
</template>

<script lang="ts" setup>
// pictureStore 专用
import { storeToRefs } from 'pinia'
import { usePictureStore } from '@/pinia/picture'
const pictureStore = usePictureStore()
let { imgsMap } = storeToRefs(pictureStore)

const props = defineProps({
  imgExts: {
    type: Array,
    default: [],
  },
})

const electronUpload = pictureStore.electronUpload
const electronUploadFolder = pictureStore.electronUploadFolder
const webUpload = pictureStore.webUpload
const webUploadFolder = pictureStore.webUploadFolder

const isElectron = window.isElectron
</script>

<style lang="scss" scoped>
@import '@/assets/css/picture-btns.scss';
.picture-add {
  .picture-btns {
    .add-pic {
      padding: 6px 19.5px;
    }
    .add-folder {
      padding: 6px 11.5px;
    }
  }
  .drag-area {
    width: 100%;
    // height: 100%;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    justify-content: center;
    // align-items: center;
    margin-top: 20.28vh;

    .drag-area-main {
      width: calc(100% - 165px - 30px);

      .add-icon {
        img {
          width: 196px;
          height: 153px;
        }
      }

      .desc {
        margin-top: 20px;
        width: 100%;
        border: 1px solid transparent;
        color: #282a43;
        font-weight: 600;
        font-size: 16px;
        text-align: center;

        span {
          display: block;
          font-weight: 600; // electron
          font-size: 20px;
          color: #333333;
          line-height: 28px;
        }
      }

      .desc-info {
        line-height: 20px;
        color: #999999;
        text-align: center;
        font-size: 14px;
        margin-top: 5px;
        padding: 0 20px;
        white-space: pre-wrap;
      }
    }
  }
}
</style>
