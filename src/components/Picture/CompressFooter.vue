<template>
  <div v-if="isElectron || imgsMap.size" class="picture-footer flex">
    <div class="flex1">
      <!-- 压缩模式 -->
      <div v-show="imgsMap.size" class="compress-model flex">
        <span class="tip">压缩模式</span>
        <el-radio-group v-model="compressType" @change="changeCompressMode">
          <el-radio
            v-for="comKey in Object.keys(compressRadio)"
            :key="comKey"
            :label="comKey"
          ></el-radio>
        </el-radio-group>
      </div>
      <!-- 压缩模式-滑动条 -->
      <CompressSlider v-if="compressType === '自定义'"></CompressSlider>
      <!-- common 导出目录 -->
      <ExportDirectory></ExportDirectory>
    </div>
    <button
      v-show="imgsMap.size"
      class="start-convert btn"
      :class="{
        'start-convert-web': !isElectron,
      }"
      @click="beforeConvert(imgsMap, true)"
    >
      <!-- <el-icon v-if="loading" class="is-loading" color="#fff"><Loading /></el-icon> -->
      <div>全部压缩</div>
    </button>
  </div>
</template>

<script lang="ts" setup>
import { storeToRefs } from 'pinia'
import { usePictureStore } from '@/pinia/picture'
import { useCompressStore } from '@/pinia/compress'
import CompressSlider from '@/components/Picture/CompressSlider.vue'
const pictureStore = usePictureStore()
const compressStore = useCompressStore()
let { imgsMap } = storeToRefs(pictureStore)
let { compressType, compressRadio } = storeToRefs(compressStore)
const beforeConvert = pictureStore.beforeConvert
const isElectron = window.isElectron

const changeCompressMode = (key: any) => {
  // console.log(key, compressRadio.value[key])
  compressStore.compressChange(compressRadio.value[compressType.value])
}
</script>
<style lang="scss">
.picture-footer {
  .el-radio-group {
    flex-wrap: nowrap;
    height: 22px;
    line-height: 22px;

    .el-radio {
      margin-right: 36px;
      color: rgba(0, 0, 0, 0.65) !important;

      // .el-radio__input {
      //   top: 0.5px;
      // }

      // .el-radio__input.is-checked + .el-radio__label {
      //   color: #333 !important;
      // }

      .el-radio__label {
        padding-left: 8px;
      }

      .el-radio__inner {
        border-color: #d4d4d4;
      }

      .el-radio__input.is-checked .el-radio__inner {
        border-color: #0089fa;
        background: #fff;
      }

      .el-radio__inner::after {
        width: 8px;
        height: 8px;
        background-color: #0089fa;
      }
    }
    .el-radio.el-radio--large .el-radio__inner {
      width: 16px;
      height: 16px;
    }
  }
}
</style>

<style lang="scss" scoped>
@import './compress-model.scss';
.picture-footer {
  position: absolute;
  bottom: 0;
  background-color: #fff;
  width: 100%;
  padding: 13.5px 20px;
  border-top: 1px solid #e0e0e0;

  .compress-model {
    :deep(.el-radio__label) {
      color: #333;
    }
  }

  .start-convert {
    position: absolute;
    right: 20px;
    bottom: 25px;

    width: 120px;
    height: 49px;
    line-height: 49px;
    border-radius: 4px;
    background: #389bfd;
    color: #fff;
    font-size: 18px;
    border-radius: 4px;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    user-select: none;
    z-index: 1;
    cursor: pointer;

    &:hover {
      opacity: 0.7;
    }
  }

  .start-convert-web {
    bottom: 11px;
    width: 112px;

    height: 38px;
    margin: 0;
    font-size: 16px;

    .el-icon {
      margin-right: 3px;
      animation: rotating 2s linear infinite;
    }
  }
}
</style>
