<template>
  <div
    class="detail center"
    :style="{
      width: `${detailWidth}px`,
      height: `${detailHeight}px`,
      marginLeft: `${detailMarginLeft}px`,
    }"
  >
    <div class="flex info">
      <div
        class="origin-info center"
        :class="{
          selected: pageInfo[pageType].currentImg.select === 'origin',
        }"
        @click="pageInfo[pageType].currentImg.select = 'origin'"
      >
        原图{{ originImgSize }}
      </div>
      <div
        class="result-info center"
        :class="{
          selected: pageInfo[pageType].currentImg.select === 'new',
        }"
        @click="pageInfo[pageType].currentImg.select = 'new'"
      >
        效果图{{ resultImgSize }}
      </div>
    </div>
    <!-- 效果图 -->
    <div
      v-if="pageInfo[pageType].currentImg.select === 'new'"
      class="result-img-box center"
    >
      <img
        class="result-img"
        :style="{
          width: imageWidth,
          height: imageHeight,
        }"
        :src="pageInfo[pageType].currentImg.new"
        @load="onResultImgLoaded"
      />
    </div>
    <!-- 原图 -->
    <div v-else class="origin-img-box center">
      <img
        class="origin-img"
        :style="{
          width: imageWidth,
          height: imageHeight,
        }"
        :src="pageInfo[pageType].currentImg.origin"
        @load="onOriginImgLoaded"
      />
    </div>
    <div v-if="loading" class="loading">
      <span class="loading-span"></span>
    </div>

    <Menus v-if="isCartoon"></Menus>
  </div>
</template>

<script setup lang="ts">
import {
  ref,
  // onMounted
} from 'vue'
import { storeToRefs } from 'pinia'
import { usePictureAliStore } from '@/pinia/pictureAli'
const pictureAliStore = usePictureAliStore()
const { pageInfo, pageType, loading } = storeToRefs(pictureAliStore)

const isCartoon = ref(pageType.value === 'cartoon')
const detailWidth = isCartoon.value ? 777 : 800
const detailHeight = 570
const detailMarginLeft = isCartoon.value ? 6 : 50

// 获取图片信息
let resultImgSize = ref('')
const onResultImgLoaded = (e: any) => {
  if (pageType.value !== 'enlarge') return // 无损放大才显示图片大小

  const img = e.target
  const width = img.naturalWidth
  const height = img.naturalHeight

  if (width && height) {
    resultImgSize.value = `（尺寸:${width}*${height}）`
  }
}

let rate = 1
let imageWidth = ref('')
let imageHeight = ref('')
let originImgSize = ref('')
const onOriginImgLoaded = (e: any) => {
  const img = e.target
  const width = img.naturalWidth
  const height = img.naturalHeight
  rate = width / height
  if (rate > 1) {
    imageWidth.value = `${detailWidth}px`
    imageHeight.value = 'auto'
  } else {
    imageWidth.value = 'auto'
    imageHeight.value = `${detailHeight}px`
  }

  if (pageType.value !== 'enlarge') return // 无损放大才显示图片大小
  if (width && height) {
    originImgSize.value = `（尺寸:${width}*${height}）`
  }
}

// onMounted(() => {
//   loading.value = true
//   setTimeout(() => {
//     loading.value = false
//   }, 1000)
// })
</script>

<style lang="scss" scoped>
@keyframes debugLoading {
  0% {
    height: 0;
  }
  100% {
    height: 100%;
    opacity: 0;
  }
}
.detail {
  position: relative;
  background: #ffffff;
  box-shadow: 0px 0px 20px 0px rgba(0, 0, 0, 0.17);

  .loading {
    width: 100%;
    height: 100%;
    position: absolute;
    z-index: 20;
    left: 0;
    top: 0;
    span {
      width: 100%;
      position: absolute;
      left: 0;
      top: 0;
      background: linear-gradient(#fff, #389bfd);
      animation: debugLoading 1s linear infinite;
    }
  }

  .info {
    position: absolute;
    top: -50px;
    height: 36px;
    font-weight: 500; // electron
    font-size: 14px;
    color: #333;
    line-height: 20px;
    border-radius: 8px;
    background-color: #fff;
    z-index: 2;

    .origin-info {
      padding: 0px 30px;
      text-wrap: nowrap;
      z-index: 1;
      cursor: pointer;
    }

    .result-info {
      padding: 0px 30px;
      text-wrap: nowrap;
      z-index: 1;
      cursor: pointer;
    }

    .selected {
      border-radius: 8px;
      color: #fff;
      background: #389bfd;
      transition: all 0.5s;
    }
  }

  .result-img-box,
  .origin-img-box {
    position: absolute;
    width: 100%;
    height: 100%;
    background: #fff;

    img {
      max-width: 100%;
      max-height: 100%;
      left: 0px;
      top: 0px;
    }
  }
}
</style>
