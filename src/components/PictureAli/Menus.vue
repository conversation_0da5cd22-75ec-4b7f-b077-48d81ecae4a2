<template>
  <div
    class="menus"
    :style="{
      top: isCartoon ? '87px' : '0',
    }"
  >
    <p
      class="menu center"
      :class="{ selected: pageInfo[pageType].currentMenuIndex === index }"
      v-for="(menu, index) in menus"
      :key="index"
      @click="menuClick($event, index)"
    >
      {{ menu }}
    </p>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { storeToRefs } from 'pinia'
import { usePictureAliStore } from '@/pinia/pictureAli'
const pictureAliStore = usePictureAliStore()
const { pageInfo, pageType, menus } = storeToRefs(pictureAliStore)

let currentPageInfo = pageInfo.value[pageType.value]

const hasOrigin = !!currentPageInfo.currentImg?.origin
const isCartoon = ref(pageType.value === 'cartoon' && hasOrigin)

const menuClick = (e: any, menuIndex: number) => {
  if (currentPageInfo.currentMenuIndex === menuIndex) return

  currentPageInfo.currentMenuIndex = menuIndex
  if (hasOrigin) {
    currentPageInfo.currentDemoResultImage = pictureAliStore.getResultImage(
      currentPageInfo.currentImg?.origin
    )
  } else {
    currentPageInfo.currentDemoResultImage =
      pictureAliStore.getResultImageBySelect()
  }
}
</script>

<style lang="scss" scoped>
.menus {
  position: absolute;
  right: -122px;

  .menu {
    width: 90px;
    height: 32px;
    margin-bottom: 12px;
    background: #ffffff;
    border-radius: 4px;
    border: 1px solid #666666;

    font-weight: 400;
    font-size: 14px;
    color: #666666;
    line-height: 20px;

    cursor: pointer;
  }
  .selected {
    color: #fff;
    background: #389bfd;
    border: 1px solid #389bfd;
  }
}
</style>
