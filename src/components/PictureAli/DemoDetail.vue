<template>
  <p class="title">示例：</p>
  <div
    class="demo-detail chessboard-bg"
    :style="{
      padding: `0 ${paddingWidth}px`,
      width: `${detailWidth}px`,
    }"
  >
    <div class="origin-info center">原图{{ originImgSize }}</div>
    <!-- 效果图 -->
    <div class="result-img-box">
      <img
        class="result-img"
        :style="{
          width: `${resultImgWidth}`,
        }"
        :src="pageInfo[pageType].currentDemoResultImage"
        @load="onResultImgLoaded"
      />
    </div>
    <!-- 原图 -->
    <div
      class="origin-img-box"
      :style="{
        left: `${paddingWidth}px`,
        width: `${leftValue - paddingWidth + barWidth / 2}px`,
      }"
    >
      <img
        class="origin-img"
        :src="pageInfo[pageType].currentDemoImage"
        @load="onOriginImgLoaded"
      />
    </div>
    <!-- <div v-if="loading" class="loading">
      <span class="loading-span"></span>
    </div> -->
    <!-- 控制条 -->
    <div
      class="control"
      :style="{
        left: `${leftValue}px`,
      }"
    >
      <div class="line"></div>
      <div class="bar" @mousedown="startDrag" @mouseup="stopDrag">
        <!-- <i class="left-icon arrow-left"></i
        ><i class="right-icon arrow-right"></i> -->
      </div>
      <div class="tip center result-info">效果图{{ resultImgSize }}</div>
    </div>

    <Menus v-if="isCartoon"></Menus>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { storeToRefs } from 'pinia'
import { usePictureAliStore } from '@/pinia/pictureAli'
const pictureAliStore = usePictureAliStore()
const { pageInfo, pageType } = storeToRefs(pictureAliStore)

const isCartoon = pageType.value === 'cartoon'
const isSketch = pageType.value === 'sketch'

// 获取图片信息
let resultImgSize = ref('')
const onResultImgLoaded = (e: any) => {
  if (pageType.value !== 'enlarge') return // 无损放大才显示图片大小

  const img = e.target
  const width = img.naturalWidth
  const height = img.naturalHeight

  if (width && height) {
    resultImgSize.value = `（尺寸:${width}*${height}）`
  }
}
let originImgSize = ref('')
const onOriginImgLoaded = (e: any) => {
  if (pageType.value !== 'enlarge') return // 无损放大才显示图片大小

  const img = e.target
  const width = img.naturalWidth
  const height = img.naturalHeight

  if (width && height) {
    originImgSize.value = `（尺寸:${width}*${height}）`
  }
}

// 拖拽条
const resultImgWidth = isSketch ? 'auto' : '688px'
const paddingWidth = isCartoon ? 40 : 95
const detailWidth = isCartoon ? 769 : 879
const minLeft = isCartoon ? 23 : 78
const maxLeft = isCartoon ? 711 : 766
const barWidth = 35
const gap = 190
let leftValue = ref(detailWidth / 2)
let isDragging = ref(false)
const handleMousemove = (e: MouseEvent) => {
  if (isDragging.value) {
    leftValue.value = e.clientX - gap

    if (leftValue.value < minLeft) {
      leftValue.value = minLeft
    }
    if (leftValue.value > maxLeft) {
      leftValue.value = maxLeft
    }
  }
}
const startDrag = (e: MouseEvent) => {
  isDragging.value = true
  document.addEventListener('mousemove', handleMousemove)
  document.addEventListener('mouseup', stopDrag)
}
const stopDrag = () => {
  isDragging.value = false
  document.removeEventListener('mousemove', handleMousemove)
  document.removeEventListener('mouseup', stopDrag)
}
</script>

<style lang="scss" scoped>
// @keyframes debugLoading {
//   0% {
//     height: 0;
//   }
//   100% {
//     height: 100%;
//     opacity: 0;
//   }
// }
.title {
  margin: 26px 0 13px;
  font-weight: 600; // electron
  font-size: 15px;
  color: #333333;
}
.demo-detail {
  position: relative;
  height: 386px;
  // background: #f7f7f7;
  // background: url(/img/pic/second/<EMAIL>);
  // background-size: contain;
  user-select: none;

  // .loading {
  //   width: 100%;
  //   height: 100%;
  //   position: absolute;
  //   z-index: 20;
  //   left: 0;
  //   top: 0;
  //   span {
  //     width: 100%;
  //     position: absolute;
  //     left: 0;
  //     top: 0;
  //     background: linear-gradient(#fff, #389bfd);
  //     animation: debugLoading 1s linear infinite;
  //   }
  // }

  .origin-info {
    position: absolute;
    top: 0;
    left: 0;
    padding: 10px;
    color: #fff;
    background: rgba(0, 0, 0, 0.5);
    opacity: 0.6;
    min-width: 88px;
    text-align: center;
    text-wrap: nowrap;
    width: max-content;
    z-index: 1;
  }

  .result-img-box {
    position: absolute;
    width: 688px;
    height: 386px;

    img {
      width: 688px;
      height: 386px;
      position: absolute;
      left: 50%;
      top: 50%;
      transform: translate(-50%, -50%);
      -webkit-user-drag: none;
    }
  }
  .origin-img-box {
    position: absolute;
    width: 688px;
    height: 386px;
    background: #d8d8d8;
    overflow: hidden; // 必须

    img {
      width: 688px;
      height: 386px;
      left: 0px;
      top: 0px;
      -webkit-user-drag: none;
    }
  }
  .control {
    position: absolute;
    top: 0;
    width: 35px;
    height: 386px;

    .line {
      width: 2px;
      background: #0074E8;
      height: 100%;
      position: absolute;
      left: 16.5px;
      top: 0;
    }

    .bar {
      position: absolute;
      width: 34px;
      height: 35px;
      // background: url(/img/pic/second/<EMAIL>) left top/contain;
      background: url(/img/pic/second/<EMAIL>) left top/contain;
      border-radius: 35px;
      left: 0;
      top: 175.5px;
      // cursor: move;

      // background: #389bfd;
      // .left-icon {
      //   position: absolute;
      //   left: 8px;
      //   top: 13.5px;
      // }
      // .right-icon {
      //   position: absolute;
      //   right: 8px;
      //   top: 13.5px;
      // }
      // .arrow-left {
      //   width: 0px;
      //   height: 0px;
      //   border-bottom: 4px solid transparent; /* left arrow slant */
      //   border-top: 4px solid transparent; /* right arrow slant */
      //   border-right: 6px solid #fff; /* bottom, add background color here */
      //   border-radius: 2px;
      //   font-size: 0px;
      //   line-height: 0px;
      // }
      // .arrow-right {
      //   width: 0px;
      //   height: 0px;
      //   border-bottom: 4px solid transparent; /* left arrow slant */
      //   border-top: 4px solid transparent; /* right arrow slant */
      //   border-left: 6px solid #fff; /* bottom, add background color here */
      //   border-radius: 2px;
      //   font-size: 0px;
      //   line-height: 0px;
      // }
    }

    .result-info {
      position: absolute;
      padding: 10px;
      left: 2px;
      top: 0;
      left: 18.5px;
      color: #fff;
      background: #0074E8;
      opacity: 0.6;
      min-width: 88px;
      text-align: center;
      text-wrap: nowrap;
      width: max-content;
      z-index: 1;
    }
  }
}
</style>
