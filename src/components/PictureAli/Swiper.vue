<template>
  <swiper
    class="swiper"
    :modules="modules"
    :space-between="20"
    :slides-per-view="5"
    :slides-per-group="5"
    :loop="true"
    :loop-fill-group-with-blank="true"
    :navigation="true"
    :pagination="{ clickable: true }"
  >
    <swiper-slide
      :class="{
        selected: pageInfo[pageType].currentDemoSwipeIndex === index,
      }"
      v-for="(item, index) in imgsArr"
      :key="index"
      @click="selectImg(item, index)"
    >
      <img :src="item" alt="" />
    </swiper-slide>
  </swiper>
</template>

<script setup lang="ts">
import { storeToRefs } from 'pinia'
import { usePictureAliStore } from '@/pinia/pictureAli'
import { Navigation } from 'swiper/modules'
import { Swiper, SwiperSlide } from 'swiper/vue'
import 'swiper/css'
import 'swiper/css/pagination'
import 'swiper/css/navigation'

const modules = [Navigation]
const pictureAliStore = usePictureAliStore()
const { pageInfo, pageType } = storeToRefs(pictureAliStore)

let currentPageInfo = pageInfo.value[pageType.value]

const imgsArr = Array(10)
  .fill(0)
  .map((_, i) => {
    const index = i + 1

    return currentPageInfo.currentDemoTemplate.replace('$', `${index}`)
  })

// let timer: any = null
const selectImg = (img: string, swipeIndex: number) => {
  if (swipeIndex && currentPageInfo.currentDemoSwipeIndex === swipeIndex) return
  // loading.value = true
  currentPageInfo.currentDemoSwipeIndex = swipeIndex
  currentPageInfo.currentDemoImage = img
  currentPageInfo.currentDemoResultImage =
    pictureAliStore.getResultImageBySelect()
  // clearTimeout(timer)
  // timer = setTimeout(() => {
  //   loading.value = false
  // }, 1000)
}

const currentDemoSwipeIndex = currentPageInfo.currentDemoSwipeIndex
selectImg(imgsArr[currentDemoSwipeIndex], currentDemoSwipeIndex)
</script>

<style lang="scss" scoped>
.swiper {
  position: absolute;
  left: 81px;
  margin-top: 21px;
  padding: 0 2px;
  // padding: 0 49px;
  // width: 878px;
  // width: 790px;
  width: 784px;

  :deep(.swiper-wrapper) {
    padding: 2px 0;
    .swiper-slide {
      width: 140px !important;
      img {
        width: 140px !important;
        height: 78px !important;
      }
      cursor: pointer;
    }
    .selected {
      box-shadow: 0 0 0 2px #389bfd;
    }
  }

  :deep(.swiper-button-prev) {
    // old
    // top: 49px;
    // left: 10px;

    // new
    position: fixed;
    top: 650px;
    left: 192px;
    width: 28px;
    height: 28px;
    background: url(/img/pic/second/<EMAIL>);
    background-size: 28px 28px;
    &::after {
      content: '' !important;
    }
  }
  :deep(.swiper-button-next) {
    // old
    // top: 49px;
    // right: 10px;

    // new
    position: fixed;
    top: 650px;
    left: calc(192px + 20px + 28px + 784px - 4px + 20px);
    width: 28px;
    height: 28px;
    background: url(/img/pic/second/<EMAIL>);
    background-size: 28px 28px;
    &::after {
      content: '' !important;
    }
  }
}
</style>
