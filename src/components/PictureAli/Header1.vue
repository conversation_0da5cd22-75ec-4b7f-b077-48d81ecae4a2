<template>
  <div class="picture-header flex">
    <div class="picture-btns flex flex1">
      <el-button
        class="btn add-pic"
        type="primary"
        @click="(e: any) => upload(e)"
      >
        <img src="/img/pic/index/<EMAIL>" />
        <span>添加图片</span>
      </el-button>
      <div class="tip">{{ pageInfo[pageType]?.currentDemoTip }}</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { goLogin, goPay } from '@/utils/user'
import { storeToRefs } from 'pinia'
import { usePictureAliStore } from '@/pinia/pictureAli'
const pictureAliStore = usePictureAliStore()
const { pageInfo, pageType } = storeToRefs(pictureAliStore)
const isElectron = window.isElectron

const upload = (e: any) => {
  if (goLogin()) return
  if (goPay()) return

  if (isElectron) {
    pictureAliStore.electronUpload(e)
  } else {
    pictureAliStore.webUpload(e)
  }
}
</script>

<style lang="scss" scoped>
@import '@/assets/css/picture-btns.scss';
// 重置
.picture-btns {
  margin-top: 32px;

  .add-pic {
    width: 95px;
    height: 32px;
  }
  .tip {
    margin-left: 20px;
    font-weight: 400;
    font-size: 14px;
    color: #999999;
    line-height: 32px;
  }
}
.picture-header {
  min-width: 901px;
}
</style>
