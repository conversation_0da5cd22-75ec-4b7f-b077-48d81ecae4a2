<template>
  <el-dialog class="PaySuccess" width="360px" height="386px" v-model="paySuccessDialogVisible" @close="close">
    <div class="box">
      <img src="/img/mind_map/icon/<EMAIL>" alt="" />
      <p>支付成功！</p>
      <el-button @click="paySuccessDialogVisible = false">立即体验</el-button>
    </div>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
const router = useRouter()
const route = useRoute()

const paySuccessDialogVisible = ref(true)
const close = () => {
  route.name === 'pay' ? router.back() : location.reload()
}
</script>

<style lang="scss">
.PaySuccess {
  padding: 0;
  border-radius: 8px;
  .el-dialog__headerbtn {
    top: 0;
    right: 0;
  }

  // 自定义close大小
  .el-dialog__close {
    font-size: 23.4px;
    svg {
      stroke: #777879;
      stroke-width: 23.4px;
    }
  }
}
</style>
<style lang="scss" scoped>
.PaySuccess {
  .box {
    text-align: center;
  }

  .el-dialog__header {
    height: 0;
  }

  .el-dialog__body {
    text-align: center;
  }

  img {
    margin-top: 20px;
    width: 71px;
    height: 71px;
  }

  p {
    margin: 30px 0;
    font-size: 16px;
    font-weight: 600;
    color: #333333;
  }

  .el-button {
    margin-bottom: 80px;
    width: 176px;
    height: 44px;
    line-height: 28px;
    padding: 0;
    font-size: 20px;
    font-weight: normal;
    border-radius: 8px;
    color: #389bfd;
    border: 1px solid #389bfd;
  }
}
</style>
