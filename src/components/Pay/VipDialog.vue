<template>
  <el-dialog
    class="VipDialog"
    width="480px"
    height="383px"
    v-model="showVipDialog"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
  >
    <p class="tip">
      <img src="/img/mind_map/icon/<EMAIL>" alt="" />
      开通VIP，解锁更多功能
    </p>
    <div class="tableHeader">
      <div class="flex">
        <p class="flex1">功能</p>
        <p class="flex1">试用</p>
        <p class="flex1">VIP</p>
      </div>
    </div>
    <div class="tableBody">
      <div class="flex text-left" v-for="(item, index) in [{
        name: '思维导图',
        nums: 2
      }, {
        name: '主题',
        nums: 100
      }, {
        name: '文件夹',
        nums: 1
      }]" :key="index">
        <p class="flex1" style="padding-left: 36px;">{{item.name}}</p>
        <p class="flex1" style="padding-left: 70px;">{{item.nums}}个</p>
        <div class="flex1" style="padding-left: 57px;">
          <img src="/img/mind_map/icon/<EMAIL>" />
        </div>
      </div>
    </div>
    <span slot="footer" class="dialog-footer flex">
      <el-button
        v-if="showVipDialogType === 'mind' && userStore.mindNums < 2 || showVipDialogType === 'folder' && userStore.folderNums < 1"
        class="try"
        @click="
          () => {
            showVipDialog = false;
            showVipDialogCallback()
          }
        "
        >继续试用</el-button
      >
      <el-button
        class="vip"
        @click="
          () => {
            showVipDialog = false;
            goNext('pay')
          }
        "
        >开通VIP</el-button
      >
    </span>
  </el-dialog>
</template>

<script setup lang="ts">
import { watch } from 'vue'
import { useRouter } from 'vue-router'
import { storeToRefs } from 'pinia'
import { usePayStore } from '@/pinia/pay'
import { useUserStore } from '@/pinia/user'
import { useEventStore } from '@/pinia/event'
const payStore = usePayStore()
const eventStore = useEventStore()
const userStore = useUserStore()
let { showVipDialog, showVipDialogType, showVipDialogCallback } = storeToRefs(eventStore)
watch(showVipDialog, (newValue: boolean, oldValue: boolean) => {
  console.log('showVipDialog watch 已触发', newValue, oldValue)
})

const router = useRouter()

const goNext = (type: string) => {
  if (type === 'pay') {
    showVipDialog.value = false
    return payStore.setShowPayDialog(true)
  }
  router.push({
    name: type
  })
}
</script>

<style lang="scss">
.VipDialog {
  .el-dialog__header {
    padding: 5px 0;
  }

  .el-dialog__body {
    text-align: center;
  }
}
</style>
<style lang="scss" scoped>
.VipDialog {
  text-align: center;
  margin: 0 auto;

  .tip {
    margin: 10px 0 30px;
    font-size: 20px;
    font-weight: 600;
    color: #e7b15c;

    img {
      width: 28px;
      height: 25px;
      margin-right: 5px;
    }
  }

  .tableHeader {
    font-size: 14px;
    color: #999999;
  }

  .tableBody {
    width: 396px;
    height: 144px;
    margin: 12px auto 28px;
    font-size: 14px;
    border-radius: 9px;
    color: #333333;
    background: #fafafa;
    border: 1px solid #e0e0e0;

    .flex {
      width: 394px;
      height: 48px;
      line-height: 48px;

      &:nth-child(2) {
        border-top: 1px dotted #e0e0e0;
        border-bottom: 1px dotted #e0e0e0;
      }
    }

    img {
      position: relative;
      bottom: 14px;
      left: 14px;
      width: 24px;
      height: 18px;
    }
  }

  .dialog-footer {
    justify-content: right;
    margin-right: 22px;

    .try {
      width: 96px;
      height: 36px;
      padding: 0;
      font-style: normal;
      border-radius: 4px;
      font-size: 14px;
      font-weight: 600;
      color: #999999;
      border: 1px solid #e0e0e0;
    }

    .vip {
      width: 90px;
      height: 36px;
      border-radius: 4px;
      padding: 0;
      font-size: 14px;
      font-weight: 600;
      color: #662b04;
      border: 0;
      background: linear-gradient(90deg, #f6deb5 0%, #eabc6c 100%);
      &:hover {
        opacity: 0.7;
      }
    }
  }
}
</style>
