

<template>
  <!-- 收银台挽留 -->
  <div class="payStayDialog">
    <el-container>
      <el-main width="500px">
        <img class="close" src="/img/mind_map/table/<EMAIL>" alt="" @click="close" />
        <!-- 优惠券 -->
        <div class="payCoupon">
          <p class="price"><span>¥ </span>10</p>
          <p class="tip1">幸运用户</p>
          <p class="tip2">优惠券</p>
          <p class="tip3">后失效</p>
          <p class="remain">{{ remainStr }}{{ millisecond > 9 ? millisecond : `0${millisecond}` }}</p>
          <div class="pop">
            <img class="vip-pop" src="/img/mind_map/table/<EMAIL>" />
            <p class="vip-pop-tip">快过期</p>
          </div>
        </div>
        <!-- 扫码支付提示信息 -->
        <div class="payType flex">
          <img class="weixin" src="/img/mind_map/icon/<EMAIL>" alt="" />
          <img class="zhifubao" src="/img/mind_map/icon/<EMAIL>" alt="" />
          <p>扫码支付</p>
        </div>
        <!-- 选择后的支付信息 -->
        <div class="payBox flex">
          <!-- 扫码支付二维码 -->
          <div class="qrcodeBox"></div>
          <img v-if="payUrl" class="qrcode" :src="'data:image/*;base64,' + payUrl" alt="" />
          <img
            v-else
            class="qrcode"
            style="background-color: #fff;"
            src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAZAAAAGQCAMAAAC3Ycb+AAAAHlBMVEVHcExBQUFTU1NmZmZ5eXmVlZWGhoapqanx8fHR0dGJBN9XAAAAAXRSTlMAQObYZgAAAAlwSFlzAAALEwAACxMBAJqcGAAAEkRJREFUeJztnYl2qzgQRGMEWv7/hx+bUGsBtD0QUDeZxJkz9mDK1a1uCfH3BwAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPRI7cfQxAM6khBDRphVGMFQVFGkAqAUUawvhjBorcDQRpDAjSGBCkMSBIY0CQxoAgjQFBGgOCNAYEaQwI0hgQpDEgSHUGIVT+s2sKggmVid/EkP30ioJIMRR8Mt7C71emSD1BRj2Gjysiu58h81TUEUQqpcQwKjKGzw/Pckmix6/LU6SKIFINhO8Kon4WWa9RRRA1RatBfF0QR48bBRHUIMNXY5bqmhFkiVhalq+ulXD1yEvrNQSR1CHfHWu5euQpUkEQaUcsCFKkSLkgchnwEo8gZBUoUi6I8vzxTT3+hoAg6YrUEMQJWR/V40/2NRT5Dw75qiBhRS4XRLkp/cMtfNmAIK4eRXMBTyckSJ94SgsFGZ/uCPJhPYKCpCpSKghKEIrbzZrhSS9RLohtke8mkJmgIkmvUCaIxBDLQdwqiJxTOrEIVkmEFEl6fpEgGPIGKFSkRBAdr4T+BT0mfEG6hGcXCOKXINBjomyglS8IGfEKxCtCQJA+/tlFgmDEG8Kbyv39WPyz8wWhI95pARAE0QSKkXiLZAvizxNCEE1goBWtSK4gUxNLWA756rxUgFC9LiKfmyuI31SEHhvBBkqkItmCkAIEQ16X4GxunCIFglgR69tdXo+QInFpJFMQf2VD/sG/koAgcfV6niAKEeuE0PR61BOzBFFC2FUh9HAJteGjYlaeIE68+vS8bZhAuR6nSB1Byg7+lQQViThROYK4RbqAIAEyq8MMQZx5W4x5dxh8j/wXQSTmCSMJFCPniiQL4sUr6LGLP/btThVJFWSOV9ayBjSxdgnV62dnK1UQL15Bj31CgrCT85UqiLPuB/HqEBZQ5GRpabJDXINUO/hXElLkOI0kCiJth2B3kzMCghxvg5ImiDdvi6bJCaEscviEJEGWiw/oGAsp/YTkBkqSIE4CERhinRLq+h4qkiiI03aHHmfI4GzuwXlLEaT+xQcf2MspqEgdQWT1eULFG69jpFJCjZRsXph4JWi8IN7FIGUnU8rRcHyq9NvdqnHaIa6bv87q66MXCcWsfUWiBfHjVZke+lV4u6lo0WOhyz7GoCD7LxcriNd0L94wcxaDtzw4WMTQkmTPwgUV2V3xECtI3UmQSY9FDb78blGRKVQtciyRq64iu/9xrCCV4xXf4tVMgwX/Fqy2MJOvSKA83P1vcwXJPbg/K17x9as9QdhvM8j6YE7vmbuE+oLsncBIQaSzJ1b2GZz+f4OJV+06xDNIUXL3FdnbRZbeV3J/9s8dYmXvaKLNsf7UBuHtCUIc0pHvMXCpnIMNZJEIRfb1sEoQkTlPON1zd5VjjVcNO0TPh9fZJy4kSIQiuwHLSSB5eiitxSMcwokcnfXd5QyBQzNVZ4rsn2U5OE2TTD02GVyHNNhAEascIYf8OsYTNQkNtHZ7WosiB596r+2evEeNUooEKtchDephLTzsvO9f8v0PgorsBQY5N9D2j23bInn9kbxHjfEWD+WQBvWYLWJKQ9ch07/laYcdmhnJi9TFm8bpupwHc0iL8WpGkPMfcEhqpVhNEEmyx2KRLD0sh3BqkUb1mD+JS683SJeoSGjxdeZm5O4QK/n5ZDxFC8Kp+d74FJUIDbNICZ+y8j+gSOYtRlT+yp9phmdwDbLp84A1K+LIITPxmzT4iuSW1yrXIcoZTg2WQxosB30k05NUvkOW3/EbmQhH24Q9aZyD2pb/iKR5EOU1rahDHqHHosiRQ0atuj7yrdiJPVsPK4/E6rF2EQcrRulEzp/ijwkpOPv9fJNYY+IMRVK2NQsc1OyP+BwsB6KG75DWk7lLcHmVNsgsS+SaQRO1CvwxMWf2yI+BHLHU8BzyFHMYhMkZoZJk0ST6hcr1+Eu4beQa34gapGs1//U8PaYlZMHraq1+V5QiU59KZbXwHWJX68gtlYfaumMOanfZzwmqC423tgHXRINvbakDSTFuT3y02ieJItyytQZczb09aSZoXYdMXbBH6zEjutCAy/xu7A1ufSs+WBn9MXXgOW51ZzlkJv9W0ZVRuvQwKYQTg7xDDz1Ocrtam1O61Psh/De2KUHu/nroUHcPt3bvvAf97VcyzUunfWvwLYW8SI9VkS7okO1B6p11Kh+h2EZSnOhAJHqTHvPnrz9yyPSgYMF8+fGJwZQadv9qGeo+tvQ4gFtTvP6DguXZpUhjCNciSyl414H9XwbmOoM6ZGoC36SIHuqGLPK4LmIacpviDTnk110fG+S8GpGEJ6sEecKUYBmy23fI8mfi2pTiA6I9Q+qQtw1199BD4KBD5ofXDrekm8AtXd6vx59pzDtWMcJcmkkkbetSg0x/vTh5UAT7ETk8h1xqEbMEzqoJ+dxH/IQ/FtTPnhixZ6+uE4QsgaPtkq8EK4I6cMiFgghv5mP962t6TI28vgGHKGdUtQSrKvOTT4RZtbtR5tqQ5TrkrXV5DOwXcsiVc1amqdj2pQVXsayEoDNYo2Uu7TJaw6ym9y25CqVHW/qfi7u+25rGl0155KNoiVi0bU0e69D3W3XHIVJfLzpFr+L1cBmIj6dyj1WRSZM79JgUgR4W01W0A//x1Et16x0A9AAAAAAAAAAAAAAAAAAAAAB3Icu4+/BfhxS8L+DWRaiFn6UGP1TjwQhexry26573pLpKxO/5958pFWOVZPy6wyhyWbpT4bsr2iuuHmKoIsjC5Yqobr6AswK/RhSpqge/ersQZU5n6VcjUUtVleNyQX6/rpZF5rh1uyKqrj/uEORXyyHzC925s8ZffX9cL4iO/rW4eSuzxwuyhqtyfxCf3KnIGwSpy6XXFvo8XhBdRNQzyL0OkbVz+i05pC73ZvXqilx8/L+q/vjdPsqqr8jFh1/bIbfXIbUVufpSl6VSrzbAakCPuopcf3X1fD1zNa4++jDz3QpLlZg2grhlFzr1+5WbY7XI9Ue/y6hIX/J144aAspY9mohXG2rbwSzn69YNGt83YzjzqjcDAAAAAAAAAAAAAAAAAACwgImMtlCi77+953tbqOVyTShCEUIwMW33ff1pUVxfQPvV+4YEGPR6h66/+qQIck3z5f/zVuGsW75GLr5LsaBreO69zLwRFOsWJTSX3u9ecXufgM/cK28XtRiD6e+LLaKWUEW+P70vv1SKLanDrJq79roENatgeeSdN+uOQmhzdDqFzH/f4BCTRO7fseQ+hs7I0YZDuP75PUWUEoyqcbNDNnPw9Rd/933UPRTrbDnudQi3HKJ/fSi966Fu0CFXC9JvzuCbWxZNPqOIcM1B6xB2o0Nsi4wiDZ+4dZsIxCvikEu7fOPAW3BukseWRDafvFwR6WdzyyHs6tbJ39xdtMSwHfPuAZdk5syHHXK9HlN/kdNoRVLJktyvP6KrkFqBXYfck0eVm863QfDkmtvu5vefEQMnfcSwQ+5668okcpJG9N/8lfdlFcYKLOgQxm9810pMud0t2s3ft2xr+V8RnS49WMghbeytYeoRq3Kf+1xcvarn2JNKMCAHu1+PSRF7iOU0gkcHvSeZcNOp6piRRqePoY3G0bSTA7dtYj1+yYBLiaG3pgS9ke+lc4QnKE69wa3H49cLFFFbzmD6h2OXlvQwZaI91tqCV1sHm4HaVi8EaSB1uMypxHOIDlz8yYpITsJT8OtaPWTcuHpUxFSI1vB3kmTa3P2hQ2BphacSf9QpUCRnLCoHzB1HJ2rRsPXI/pZev3DgkLi6XE3tSCZKx2Hj8cyj2tjejAjlkC1uTUVJe8H2CLUNpPZzSJwebKPkY2nWJ8Z290UoYNHa5FGLgVXnyOD5I7LuJXqUKKJ6Q+xSuNED3JslMT55lCKDJ4NL5LnVenTzd3Zrnuoxnsjo50laFJJu46rRY5ZnD74KJL+PX7HTcGoRo9MWyRVEWIIk1NtyGAZOy3Y3jD1iQnEgHSvXIeOj+A8oiVeLRXLfvSNIWgdEWTnErUxaV4TW5bZD9L9L2GF40EJ0hUlkEYQZRZJeSHFvioSGr6bXpihmZXM3h0zX5CS8GqcZvY5DWE6gof0tV46+6eXZ26UF4RySekKZcUh37BB5vFmuG7JS44z2SCCHzFO8bTSrfXQpuJNDUvUQLNYha7LZHYV5gqSW2moIDbV447ldMdLG9XNI6qBVMOKQxSI7b3tL/nuVgSDRimUpIqdbTgbildalxba8ZF65QXNI6phVMZPMD4e9kjopfJ6FldGX85j+/kRQjmYdImmUcpMIH5IznyLDXe2Q0GtMevRM/xM+M1thSGTJeIdkJQStD6cf7QkiafJwLBJbl1sEHBJ6GelkmhNFSgSZX2iwUkfDDhF2Ere67rl6MLsOCb6MXGTYfuw02Kkii00yo/42BOY0iTTYkB9cc6wWYXn9HtUzyyHd7pl20gzbmdHz7sqdOz2uJ0vs+fb2krrjkO1x3gBd9qscp0MsyUwKmX/sdAL0fdJZsSJ/22QJmStpU5BA/sjUY3A/97tFiOeQveQgvWKkIPBvflsDWIMO+eOkYZVVlxsWPewB1t50ltpyyOqR3V6ZdLNIiSJWf4u3ufKBOwYpeLe05b6y9xFUOplvuuy9KhFk0yW/46GoQ5rU4++P1iAleqw9E8shu6dZES0OQ9Y829TbFWLJopf5flVLvGpUD3P1R0ZdbuFlkIOPsrLUOBLETGwYChdOLIuBG8wfK9v1UWXrrYSXQA5OnEnq/UnIChXshdPi8wqudvVYb9+kCncwdMdNx/KqTY1Th9hd30WUws1/vrBZo/T8cTheU0SNPkWQlVbjfztI3yARDiGqHLy2N5WbsgTlq0jfIEdR2g5w/bEgfgMFgpwhvYn04/W4iqTzU4eYoMU2pzTXFWyLWQ/bIcef4ejWyYqXRNrr0zYFd8/v2YJ1O4echazxf+DOHUKRQ5hjkN22u4aOsmIcMvgDLQhygG+Qk2qa1iGnw94/pzhEFjlDef44q/mdHNKfCRKYzf38zrD7yJ65nJ0s08vqo0IWFElAzxPSIuTsOZZDTnpZ+inebC4UCeKNYEdNTus2ZfsjRpDVI2z78aHN/FIILcTaW7LgPCspZP2FhlpQxMe6NGd1yHksSWudrPiCtDgpfjc6ShGHRMT2pPa7xur6Fi3TejOrHOTjHtP4S2q/ayS3xIAgIYSzECsioU+ktk4WZO3Z3PfhLNGdiHqeWQaUELJCS1AedJHzFbjz6F3smSVJPab9vmFnECji4i80SRDETIYkCsIsVSAIYfAqkMiFXRmV+ow/dwhBCM5Eeheth70MKMEhgaW+KA43Ak2s2J44mcJNEkQrQqarWr6++VomPdy1vLHPzarUl2cSMdYrQVGMLHhdrC5VEJLW4wVx53J7zFWt+G3elNNqz+DGP1Na7oAiBOENseJHPHT1e3TrZMEqDssvGXkPjkHSNsZKWP3uIV01xkcQZLm+03ZIQnK1h70pSf0veIECBAnNEyaclaz2u3m2v7T060Nf5QywZhIFSW6/k6d7Uevj8+vKr0DSroZLWv3uI71x1sdXPLhb/iQ0TRbyC8MZOtLSunxdkNjrbcPQ1klGyJIcTUaL3nHIqEfa+Uhd/e6yXrhJo9aXB1rrxQeE1AhuLQNKaL9v+LO5H14OP+g0br7zttXKd0hgh8zvKqKHvJQMQag/igQxHkl9jZegvIUmmQ4x87cZgihqj9Ukqa/xEgJd3uRgUR6y6m0393iUV6OnB2/TOkmdDyGvAUEW7E93F7G0Ovwa1nZAOSfT29ws4zXegOeQjCnUwtbJCkns7NuC2OQLklupa+yh1rdHWWSxe95rWAtJMwWx0shnW/COQ7L2gcldl+W+DPT4m7dt2MrCyMXuHtZi65zWiT4UU6d/Vg/7qtvM85C/6sQ7FOhBt5rJPQ/xey6eHYrgM5/WY0aWbA5KQ1afXYcAi6obAkKQm6HXhxTUIaAWcEhj2NcYIofcTp1eFqhG4UI5UBtnKWluLwvUAkm9MSRj9iqHr7bOWwEOaQz7Lm0Y9t6OdLu9CFk3M/RW7+TTK3PbwNomBXo0wNDrCcNvr1xvBzFHrfHHZ1dJt4ZAvGqM6Qa1SmH2tSWgBgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAHH8A3h8DotVhMk2AAAAAElFTkSuQmCC"
            alt=""
          />
          <!-- 挽留支付信息 -->
          <div class="payCouponInfo">
            <p class="tip">
              <img src="/img/mind_map/icon/<EMAIL>" alt="" />
              终身VIP
            </p>
            <p class="price"><span>¥</span>{{ productPrice[1].price - couponAmount }}<span>{{ productPrice[1].tip.replace('¥', '') }}</span></p>
            <p class="tip2">{{ `「共优惠${210 + couponAmount}，活动${200 + couponAmount}+优惠券10」` }}</p>
            <div v-if="isLenovo" class="declaration flex">
              <img src="/img/mind_map/table/<EMAIL>" alt="" />
              <el-popover
                popper-class="declarationPopover"
                effect="dark"
                placement="bottom"
                trigger="hover"
                width="395px"
              >
                <template #reference>
                  <img
                    class="warning"
                    src="/img/mind_map/table/<EMAIL>"
                    alt=""
                  />
                </template>
                <template #default>
                  满足7天无理由退款的前提下，可在联想应用商店，我的订单处申请退款，也可联系联想软件商店客服申请退款
                </template>
              </el-popover>
            </div>
          </div>
        </div>
      </el-main>
    </el-container>
  </div>
</template>

<script setup lang="ts">
import type { Ref } from 'vue'
import { ref, onUnmounted } from 'vue'
import dayjs from 'dayjs'
import { APPID } from '@/utils/configData/config'
import { GetSQBQRCode, GetLXQRCode } from '@/api/pay'
const { proxy } = getCurrentInstance()
const isLenovo = window.isLenovo
let timer: any = null
let millisecondTimer: any = null

const emits = defineEmits(['close'])
// 清除倒计时
const clearPayStayTimer = () => {
  clearInterval(timer)
  clearInterval(millisecondTimer)
}
// 关闭弹框
const close = () => {
  clearPayStayTimer()
  emits('close')
}

let couponAmount = ref(0)
if(sessionStorage.isActivityTime) {
  couponAmount.value = 49 // 活动优惠金额
}

// 获取支付二维码
const payUrl = ref('')
const productPrice = {
  1: {
    productId: 1,
    productPayId: APPID + (76 - couponAmount.value),
    price: 76,
    tip: '原价¥286',
    desc: '终身VIP'
  }
}
const getQRCode = () => {
  const getQRCodeMethod = isLenovo ? GetLXQRCode : GetSQBQRCode
  getQRCodeMethod({
    product_id: productPrice[1].productPayId,
    lenovo_token: localStorage.lenovoToken || undefined
  })
    .then((_: any) => {
      if (_.data.code === 0) {
        payUrl.value = _.data.data
      } else {
        proxy.$message.error(_.data.message || '支付二维码获取失败')
      }
    })
    .catch(err => {
      console.log(err.message)
      proxy.$message.error(err.message || '支付二维码获取失败')
    })
}
getQRCode()

// 倒计时参数
let remainStr: Ref<string> = ref('')
let remainStore = JSON.parse(localStorage.payRemain || '{}')
let payRemain: number = 10 * 60 // 默认支付倒计时10分钟
const today = dayjs(new Date()).format('YYYY-MM-DD')
const isSameDay = today === remainStore.day // 是否同一天
isSameDay && (payRemain = remainStore.payRemain) // 同一天取缓存中的倒计时
// 倒计时分、秒 00:00.
const remainHandler = () => {
  --payRemain
  const remainMinute: number = payRemain / 60
  const remainSecond = payRemain % 60
  const remainSecondStr = remainSecond > 9 ? remainSecond : `0${remainSecond}`
  remainStr.value = `0${parseInt(String(remainMinute))}:${remainSecondStr}.` // 倒计时字符串，不包含毫秒（因为毫秒需要10ms更新一次）
  localStorage.payRemain = JSON.stringify({ // 缓存当天及倒计时
    day: today,
    payRemain,
  })
}
// 倒计时毫秒 00
let millisecond = ref(99) // 每10ms更新一次，分成100份，只取99
const remainMillisecondHandler = () => {
  if (payRemain <= 0) { // 毫秒归0，并清除定时器
    millisecond.value = 0
    clearPayStayTimer()
    return
  }
  --millisecond.value
  if (millisecond.value <= 0) {
    millisecond.value = 99
  }
}
// 倒计时启动或关闭弹框
if (payRemain > 0) {
  remainHandler() // 获取首次倒计时信息
  timer = setInterval(remainHandler, 1000)
  millisecondTimer = setInterval(remainMillisecondHandler, 10)
} else {
  close() // 关闭弹框和倒计时
}

// 组件卸载
onUnmounted(() => {
  console.log('payStay onUnmounted')
  clearPayStayTimer()
})
</script>

<style lang="scss">
.declarationPopover {
  text-align: center !important;
  min-width: auto !important;
  z-index: 4000 !important;
  cursor: pointer;
}
</style>

<style lang="scss" scoped>
.payStayDialog {
  position: fixed;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  margin: auto;
  width: 500px;
  height: 432px;
  @include common-dialog;
  border: 0;
  border-radius: 8px;
  background-color: #fff;
  z-index: 3002;

  .el-container {
    width: 500px;

    .el-main {
      padding: 20px 25px;
      width: 500px;
      height: 432px;
      background: #fff;
      border-radius: 8px;

      .close {
        position: relative;
        left: 433px;
        top: -9px;
        margin-bottom: 25px;
        height: 28px;
        width: 28px;
        cursor: pointer;

        &:hover {
          opacity: 0.7;
        }
      }

      .payCoupon {
        position: relative;
        background: url(/img/mind_map/table/<EMAIL>);
        background-size: contain;
        width: 438px;
        height: 130px;
        border-radius: 8px;

        .price {
          position: absolute;
          left: 48px;
          top: 36px;
          font-size: 48px;
          font-weight: 600;
          color: #fa5043;
          line-height: 50px;

          span {
            font-size: 27px;
          }
        }

        .tip1 {
          position: absolute;
          left: 135px;
          top: 41px;
          font-size: 16px;
          font-weight: 600;
          color: #333333;
        }

        .tip2 {
          position: absolute;
          left: 135px;
          top: 62px;
          font-size: 13px;
          font-weight: 600;
          color: #333333;
        }

        .tip3 {
          position: absolute;
          left: 338px;
          top: 72px;
          font-size: 16px;
          font-weight: 600;
          color: #560b01;
        }

        .remain {
          position: absolute;
          left: 315px;
          top: 33px;
          font-size: 23px;
          font-weight: 600;
          color: #fff;
        }

        .pop {
          position: absolute;
          right: 114px;
          top: -6px;

          .vip-pop {
            position: absolute;
            margin: 0 8px;
            width: 64px;
            height: 24px;
            left: 55px;
            top: -5px;
          }

          .vip-pop-tip {
            position: absolute;
            left: 66px;
            top: -2px;
            width: 64px;
            line-height: 17px;
            margin-left: 5px;
            font-size: 16px;
            font-weight: 600; // electron
            color: #662b04;
          }
        }
      }

      .payType {
        margin-top: 35px;
        line-height: 20px;

        img {
          width: 21px;
          height: 21px;
          margin-right: 6px;

          &:first-child {
            margin-left: 10px;
          }
        }

        p {
          font-size: 14px;
          font-weight: 600;
          color: #333333;
        }
      }

      .payBox {
        position: relative;

        .qrcodeBox {
          margin: 8px 30px 0 0;
          height: 136px;
          width: 136px;
          border: 3px solid #5fbd38;
          z-index: 1;
        }

        .qrcode {
          position: absolute;
          left: -2px;
          top: 6px;
          width: 140px;
          height: 140px;
          background-color: #666;
        }

        .payCouponInfo {
          .tip {
            margin-top: 6px;
            font-size: 20px;
            font-weight: 600;
            color: #e7b15c;

            img {
              width: 28px;
              height: 25px;
              margin-right: 5px;
            }
          }

          .price {
            margin: 6px 0;
            font-size: 48px;
            font-weight: 600;
            color: #fa5043;

            span {
              font-size: 27px;
            }

            span:last-child {
              margin-left: 8px;
              font-size: 18px;
              font-weight: 400;
              color: #696969;
              text-decoration: line-through;
            }
          }

          .tip2 {
            font-size: 14px;
            font-weight: 600;
            color: #333333;
          }

          .declaration {
            margin-top: 6px;

            img {
              width: 114px;
              height: 26px;
              margin-right: 5px;
            }

            .warning {
              position: relative;
              top: 3px;
              width: 20px;
              height: 20px;
            }
          }
        }
      }
    }
  }
}
</style>
