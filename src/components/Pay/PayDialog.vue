<template>
  <div v-if="payStore.showPayDialog || payStayDialogVisible" class="mask" />
  <div v-if="payStore.showPayDialog" class="payDialog">
    <PayInfo @getUserInfo="getUserInfo" @payStay="() => payStayDialogVisible = true"></PayInfo>
  </div>
  <PayStayDialog v-if="payStayDialogVisible" @close="() => {
    payStayDialogVisible = false;
    getUserInfo()
  }" />
  <PaySuccessDialog v-if="paySuccessDialogVisible" />
</template>

<script setup lang="ts">
import { ref, watch, onUnmounted } from 'vue'
import PayInfo from '@/components/Pay/PayInfo.vue'
import PayStayDialog from '@/components/Pay/PayStayDialog.vue'
import PaySuccessDialog from '@/components/Pay/PaySuccessDialog.vue'
import { GetUserInfoAndToken } from '@/api/user'
import { usePayStore } from '@/pinia/pay'
import { useUserStore } from '@/pinia/user'
import { CHANNEL } from '@/utils/configData/config'
import bridge from '@/utils/bridge'
const payStore = usePayStore()
const userStore = useUserStore()
const paySuccessDialogVisible = ref(false)
const payStayDialogVisible = ref(false)
let userInfo = JSON.parse(localStorage.user || '{}')

let timer: any = null
const getUserInfo = () => {
  GetUserInfoAndToken()
    .then(_ => {
      if (_.data.code === 0) {
        const oldUserInfo = JSON.parse(localStorage.user || '{}')
        userInfo = _.data.data || {}
        // 支付成功，获取到新的用户状态
        if (oldUserInfo.type != userInfo.type) {
          // type 1: "未付费", 2: "季度会员", 3: "年费会员", 4: "终身会员", 5: "半年会员", 6: "月会员"
          // 会员开通情况统计
          window.trackEvent('convert', `payVip${userInfo.type}_${CHANNEL}`, `${userInfo.id}`)
          userStore.setUser(userInfo)
          localStorage.user = JSON.stringify(userInfo)

          clearInterval(timer)
          payStore.setShowPayDialog(false)
          payStayDialogVisible.value = false
          paySuccessDialogVisible.value = true
        }

        bridge.updateAppIni({
          userType: userInfo.type,
          token: localStorage.token,
        })
      }
    })
    .catch(err => {
      console.log(err)
    })
}

watch(
  () => {
    return [payStore.showPayDialog, payStayDialogVisible.value]
  },
  () => {
    console.log('PayDialog watch & clear')
    // 开启支付弹框统计
    if (payStore.showPayDialog) {
      // category: 事件类别，通常用于区分不同的应用部分。
      // action: 事件操作，描述用户执行的具体动作。
      // label: 事件标签，用于进一步描述事件。
      // value: 事件的数值，通常用于事件的价值评估。
      window.trackEvent('convert', `payDialog_${CHANNEL}`, userInfo.id)
    }
    // 开启支付弹框统计 支付挽留
    if (payStayDialogVisible.value) {
      window.trackEvent('convert', `payStayDialog_${CHANNEL}`, userInfo.id)
    }
    clearInterval(timer)
    if (payStore.showPayDialog || payStayDialogVisible.value) {
      timer = setInterval(() => {
        getUserInfo()
      }, 3000)
    }
  }
)
onUnmounted(() => {
  console.log('onUnmounted')
  clearInterval(timer)
})
</script>

<style lang="scss" scoped>
.payDialog {
  position: fixed;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  margin: auto;
  width: 801px;
  z-index: 2001;

  .PayInfo {
    min-height: auto;
    margin: calc(50vh - 235px) 0px;
    padding: 0;
    @include common-dialog;
    border: 0;
    border-radius: 8px;
    background-color: transparent;
  }
}
</style>
