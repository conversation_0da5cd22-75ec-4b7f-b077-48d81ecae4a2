<template>
  <div class="PayInfo">
    <el-container>
      <el-aside width="244px">
        <img
          class="memberIcon center"
          src="/img/mind_map/icon/<EMAIL>"
          alt=""
        />
        <p class="tip">会员权益</p>
        <ul class="equity">
          <li>- 支持百种格式</li>
          <li>- 高清无损压缩</li>
          <li>- AI创意玩法</li>
          <li>- 支持高分辨率</li>
          <li>- 批量智能处理</li>
          <li>- 极速出图</li>
          <li>- 操作简单、便捷</li>
          <li>- 海量模板随心选</li>
          <li>- 持续更新</li>
          <li>- 专属客服</li>
        </ul>
      </el-aside>
      <el-main width="557px">
        <div class="close">
          <img
            v-if="showClose"
            src="/img/mind_map/table/<EMAIL>"
            alt=""
            @click="close"
          />
        </div>
        <!-- 特殊优惠券 -->
        <div v-if="isCoupon" class="payCoupon">
          <p class="price"><span>¥ </span>230</p>
          <p class="tip1">幸运用户</p>
          <p class="tip2">优惠券</p>
          <p class="tip3">限终身会员</p>
          <div class="pop">
            <img
              class="vip-pop"
              src="/img/mind_map/table/<EMAIL>"
            />
            <p class="vip-pop-tip">仅3.37%中奖率</p>
          </div>
        </div>
        <!-- 会员产品选择 -->
        <div v-else class="payList flex">
          <div
            v-if="productPrice[productId]"
            class="item"
            :class="{
              checked: productId === produce.productId
            }"
            @click="
              () => {
                if (productId === produce.productId) return
                productId = produce.productId
                getQRCode()
              }
            "
            v-for="(produce, index) in productPrice"
            :key="index"
          >
            <p class="price">¥{{ produce.price }}</p>
            <p
              class="tip"
              :style="{
                textDecoration: produce.productId === 3 ? 'line-through' : ''
              }"
            >
              {{ produce.tip }}
            </p>
            <p class="desc">{{ produce.desc }}</p>
            <div
              v-if="produce.productId === 3"
              class="pop"
              style="width: 120px"
            >
              <img
                v-if="isActivityTime"
                class="vip-pop"
                :src="produce.tipImage1"
              />
              <div v-else>
                <img
                  class="vip-pop"
                  src="/img/mind_map/table/<EMAIL>"
                />
                <p class="vip-pop-tip">周年大促</p>
              </div>
            </div>
            <img
              src="/img/mind_map/icon/<EMAIL>"
              alt=""
              class="icon"
            />
          </div>
        </div>
        <!-- 扫码支付文本提示 -->
        <div class="payType flex">
          <img
            class="weixin"
            src="/img/mind_map/icon/<EMAIL>"
            alt=""
          />
          <img
            class="zhifubao"
            src="/img/mind_map/icon/<EMAIL>"
            alt=""
          />
          <p>扫码支付</p>
        </div>
        <!-- 选择后的支付信息 -->
        <div class="payBox flex">
          <!-- 扫码支付二维码 -->
          <div class="qrcodeBox"></div>
          <img
            v-if="payUrl"
            class="qrcode"
            :src="'data:image/*;base64,' + payUrl"
            alt=""
          />
          <img
            v-else
            class="qrcode"
            style="background-color: #fff;"
            src="data:image/png;base64,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"
            alt=""
          />
          <!-- 优惠券支付信息 -->
          <div v-if="isCoupon" class="payCouponInfo">
            <p class="tip">终身VIP</p>
            <p class="price tip3"><span>¥</span>68<span>原价286</span></p>
            <p class="tip2">「共优惠230元」</p>
          </div>
          <!-- 普通支付信息 -->
          <div v-else class="payInfo">
            <div class="flex">
              <p class="tip">支付金额</p>
              <p class="price">
                <span>¥ </span>{{ productPrice[productId].price }}
              </p>
            </div>
            <div v-if="isLenovo" class="declaration flex">
              <img src="/img/mind_map/table/<EMAIL>" alt="" />
              <el-popover
                popper-class="declarationPopover"
                effect="dark"
                placement="bottom"
                trigger="hover"
                width="395px"
              >
                <template #reference>
                  <img
                    class="warning"
                    src="/img/mind_map/table/<EMAIL>"
                    alt=""
                  />
                </template>
                <template #default>
                  满足7天无理由退款的前提下，可在联想应用商店，我的订单处申请退款，也可联系联想软件商店客服申请退款
                </template>
              </el-popover>
            </div>
            <img
              v-if="productId === 3"
              class="tip-image2"
              :src="productPrice[productId].tipImage2 || '/img/mind_map/table/<EMAIL>'"
              alt=""
            />
          </div>
        </div>
      </el-main>
    </el-container>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRoute } from 'vue-router'
import { APPID } from '@/utils/configData/config'
import { isVip, isVipExpire, isDateInRange } from '@/utils'
import { GetSQBQRCode, GetLXQRCode } from '@/api/pay'
import { GetActivityInfo } from '@/api/activity'
import { usePayStore } from '@/pinia/pay'
import { useUserStore } from '@/pinia/user'
const payStore = usePayStore()
const userStore = useUserStore()
const isLenovo = window.isLenovo
const route = useRoute()
const showClose = route.name !== 'pay'
const { proxy } = getCurrentInstance()
const productId = ref(3)
const payUrl = ref('')
const isCoupon = ref(false)
const productPrice = ref({
  1: {
    productId: 1,
    productPayId: APPID + 46,
    price: 46,
    tip: '仅0.25元/天',
    desc: '半年VIP',
    tipImage1: '',
    tipImage2: ''
  },
  2: {
    productId: 2,
    productPayId: APPID + 66,
    price: 66,
    tip: '仅0.18元/天',
    desc: '一年VIP',
    tipImage1: '',
    tipImage2: ''
  },
  3: {
    productId: 3,
    productPayId: APPID + 86,
    price: 86,
    tip: '原价¥286',
    desc: '终身VIP',
    tipImage1: '',
    tipImage2: ''
  }
})

// 是否是活动时间
// const isActivityTime = window.hostName === 'wn.bestmind365.com' && isDateInRange(Date.now(), '2023-12-11', '2023-12-12')
let isActivityTime = ref(false)
window.hostName === 'wn.bestmind365.com' && GetActivityInfo().then(_ => {
  if (_.data.code === 0 || _.status === 200) {
    isActivityTime.value = isDateInRange(_.data.now * 1000, _.data.start_date * 1000, _.data.end_date * 1000)
    if (isActivityTime.value) {
      sessionStorage.isActivityTime = isActivityTime.value
      productPrice.value['3'] = {
        productId: 3,
        productPayId: APPID + 49,
        price: 49,
        tip: '原价¥286',
        desc: '终身VIP',
        tipImage1: '/img/mind_map/activity/1212.png',
        tipImage2: '/img/mind_map/activity/1212tip.png'
      }
      getQRCode()
    } else {
      sessionStorage.removeItem('isActivityTime')
    }
  }
})

// const specialPrice = {
//   4: {
//     productId: 4,
//     productPayId: APPID + 48,
//     price: 48,
//     tip: '终身VIP',
//     tip2: '「共优惠230元」',
//     tip3: '原价286'
//   }
// }

const emits = defineEmits(['getUserInfo', 'payStay'])
const close = () => {
  payStore.setShowPayDialog(false)

  if (isVip(userStore.user) && !isVipExpire(userStore.user)) {
    emits('getUserInfo')
  } else {
    emits('payStay')
  }
}

// 支付二维码
const getQRCode = () => {
  const getQRCodeMethod = isLenovo ? GetLXQRCode : GetSQBQRCode
  getQRCodeMethod({
    product_id: productPrice.value[productId.value].productPayId,
    lenovo_token: localStorage.lenovoToken || undefined
  })
    .then((_: any) => {
      if (_.data.code === 0) {
        payUrl.value = _.data.data
      } else {
        proxy.$message.error(_.data.message || '支付二维码获取失败')
      }
    })
    .catch(err => {
      console.log(err.message)
      proxy.$message.error(err.message || '支付二维码获取失败')
    })
}

// init
getQRCode()
</script>

<style lang="scss">
.declarationPopover {
  text-align: center !important;
  min-width: auto !important;
  z-index: 4000 !important;
  cursor: pointer;
}
</style>
<style lang="scss" scoped>
.PayInfo {
  // == start 给支付页使用 ==
  background-color: #f7f8f9;
  min-height: calc(100% - 80px - 32px);
  padding: 111px 60px 30px;
  // == end 给支付页使用 ==

  .el-container {
    width: 801px;
    margin: 0 auto;

    .el-aside {
      width: 244px;
      height: 471px;
      padding: 31px;
      background: #edf4ff;
      border-radius: 8px 0px 0px 8px;

      .memberIcon {
        width: 56px;
        height: 56px;
        margin: 0 auto;
      }

      .tip {
        margin: 10px 0 15px;
        font-size: 20px;
        font-weight: 600;
        color: #e7b15c;
        line-height: 28px;
        text-align: center;
      }

      .equity {
        li {
          font-size: 14px;
          font-weight: 600; // electron
          color: #333333;
          line-height: 26px;
        }
      }
    }

    .el-main {
      position: relative;
      width: 557px;
      height: 471px;
      background: #fff;
      border-radius: 0px 8px 8px 0;
      overflow: hidden;

      .close {
        position: relative;
        left: 495px;
        top: -9px;
        margin-bottom: 30px;
        height: 28px;
        width: 28px;

        &:hover {
          opacity: 0.7;
        }

        img {
          width: 28px;
          height: 28px;
          cursor: pointer;
        }
      }

      .payList {
        position: relative;

        .item {
          position: relative;
          width: 160px;
          height: 111px;
          margin-right: 16px;
          background: #ffffff;
          border-radius: 8px;
          box-sizing: border-box;
          border: 1px solid #e0e0e0;
          text-align: center;
          cursor: pointer;

          &:hover {
            background: #fff9ed;
            border: 2px solid #e7b15c;

            .pop {
              right: -1px;
              top: -8px;
            }
          }

          .price {
            font-size: 24px;
            font-weight: 600;
            color: #fa5043;
            line-height: 33px;
            margin-top: 8px;
          }

          .tip {
            font-size: 14px;
            color: #696969;
            line-height: 20px;
            margin-top: 8px;
          }

          .desc {
            font-size: 16px;
            font-weight: 600; // electron
            color: #333333;
            line-height: 22px;
            margin-top: 8px;
          }

          .pop {
            position: absolute;
            right: 0;
            top: -7px;

            .vip-pop {
              position: absolute;
              margin: 0 8px;
              width: 72px;
              height: 24px;
              left: 55px;
              top: -5px;
            }

            .vip-pop-tip {
              position: absolute;
              left: 64px;
              top: -2px;
              width: 60px;
              line-height: 17px;
              margin-left: 5px;
              font-size: 15px;
              font-weight: 600; // electron
              color: #ffffff;
            }
          }

          .icon {
            display: none;
          }
        }

        .checked {
          background: #fff9ed;
          border: 2px solid #e7b15c;

          .pop {
            right: -1px;
            top: -8px;
          }

          .icon {
            display: block;
            position: absolute;
            right: -1px;
            bottom: -1px;
            width: 29px;
            height: 29px;
          }
        }
      }

      .payCoupon {
        // <EMAIL>
        // <EMAIL>
        position: relative;
        background: url(/img/mind_map/table/<EMAIL>);
        background-size: contain;
        width: 432px;
        height: 112px;
        border-radius: 8px;

        .price {
          position: absolute;
          left: 48px;
          top: 30px;
          font-size: 42px;
          font-weight: 600;
          color: #fa5043;
          line-height: 50px;

          span {
            font-size: 27px;
          }
        }

        .tip1 {
          position: absolute;
          left: 148px;
          top: 35px;
          font-size: 16px;
          font-weight: 600;
          color: #333333;
        }

        .tip2 {
          position: absolute;
          left: 148px;
          top: 56px;
          font-size: 13px;
          font-weight: 600;
          color: #333333;
        }

        .tip3 {
          position: absolute;
          left: 321px;
          top: 60px;
          font-size: 16px;
          font-weight: 600;
          color: #560b01;
        }

        .pop {
          position: absolute;
          right: 173px;
          top: -6px;

          .vip-pop {
            position: absolute;
            margin: 0 8px;
            width: 129px;
            height: 24px;
            left: 55px;
            top: -5px;
          }

          .vip-pop-tip {
            position: absolute;
            left: 66px;
            top: -2px;
            width: 115px;
            line-height: 17px;
            margin-left: 5px;
            font-size: 16px;
            font-weight: 600; // electron
            color: #662b04;
          }
        }
      }

      .payType {
        margin-top: 67px;

        img {
          width: 21px;
          height: 21px;
          margin-right: 6px;

          &:first-child {
            margin-left: 10px;
          }
        }

        p {
          font-size: 14px;
          font-weight: 600;
          color: #333333;
        }
      }

      .payBox {
        position: relative;
        .qrcodeBox {
          margin: 8px 30px 0 0;
          height: 136px;
          width: 136px;
          border: 3px solid #5fbd38;
          z-index: 1;
        }
        .qrcode {
          position: absolute;
          left: -2px;
          top: 6px;
          width: 140px;
          height: 140px;
          background-color: #666;
        }

        .payCouponInfo {
          .tip {
            margin-top: 10px;
            font-size: 20px;
            font-weight: 600;
            color: #e7b15c;
          }

          .price {
            margin: 10px 0;
            font-size: 48px;
            font-weight: 600;
            color: #fa5043;

            span {
              font-size: 27px;
            }

            span:last-child {
              margin-left: 8px;
              font-size: 18px;
              font-weight: 400;
              color: #696969;
              text-decoration: line-through;
            }
          }

          .tip2 {
            font-size: 14px;
            font-weight: 600;
            color: #333333;
          }
        }

        .payInfo {
          .tip {
            position: relative;
            top: 20px;
            margin-right: 15px;
            font-size: 16px;
            font-weight: 400;
            color: #333333;
          }

          .price {
            font-size: 36px;
            font-weight: 600;
            color: #fa5043;
            line-height: 50px;

            span {
              font-size: 27px;
            }
          }

          .declaration {

            img {
              width: 114px;
              height: 26px;
              margin-right: 5px;
            }

            .warning {
              position: relative;
              top: 3px;
              width: 20px;
              height: 20px;
            }
          }

          img {
            width: 185px;
            height: 38px;
          }

          .tip-image2 {
            margin-top: 10px;
          }
        }
      }
    }
  }
}
</style>
