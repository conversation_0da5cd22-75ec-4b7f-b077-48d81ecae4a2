<template>
  <div class="mask" />
  <div class="passport-dialog">
    <div class="info flex">
      <img
        class="close"
        @click="closeDialog"
        src="/img/mind_map/table/<EMAIL>"
        alt=""
      />
      <div class="segmented-container">
        <el-segmented
          v-model="currentSegment"
          :options="['单张照', '排版照']"
          block
        />
      </div>
      <div v-if="currentSegment === '单张照'" class="single">
        <div class="ruler-container" :data-scale="scale?.toFixed(2)">
          <!-- 顶部标尺 -->
          <div
            class="ruler-horizontal"
            :style="{ width: naturalWidth * scale + 'px' }"
          >
            <div class="ruler-label">
              {{ naturalWidthMM }}mm / {{ naturalWidth }}px
            </div>
            <div class="ruler-line"></div>
          </div>
          <!-- 左侧标尺 -->
          <div
            class="ruler-vertical"
            :style="{ height: naturalHeight * scale + 'px' }"
          >
            <div class="ruler-label">
              {{ naturalHeightMM }}mm / {{ naturalHeight }}px
            </div>
            <div class="ruler-line"></div>
          </div>
          <img
            class="single-croped-image"
            :src="currentImg.mattingCropedImage"
            :style="{
              width: naturalWidth * scale + 'px',
              height: naturalHeight * scale + 'px',
            }"
            @load="handleImageLoad"
          />
        </div>
      </div>
      <div v-else class="multi">
        <div class="multi-left">
          <!-- 相纸尺寸选择 -->
          <div class="paper-size-section">
            <div class="section-title">相纸尺寸</div>
            <div class="paper-size-list">
              <div
                class="paper-size-item"
                :class="{ active: selectedPaperSize === '自定义' }"
                @click="selectPaperSize('自定义')"
              >
                自定义
              </div>
              <!-- 自定义行列设置 -->
              <div
                v-if="selectedPaperSize === '自定义'"
                class="custom-layout-section"
              >
                <div class="layout-control">
                  <label>目行</label>
                  <el-input-number
                    v-model="rows"
                    :min="1"
                    :max="10"
                    @change="drawMultiLayout"
                  />
                </div>
                <div class="layout-control">
                  <label>四列</label>
                  <el-input-number
                    v-model="cols"
                    :min="1"
                    :max="10"
                    @change="drawMultiLayout"
                  />
                </div>
              </div>
              <div
                v-for="(size, key) in paperSizes"
                :key="key"
                class="paper-size-item"
                :class="{ active: selectedPaperSize === key }"
                @click="selectPaperSize(key)"
              >
                {{ key }} ({{ size.width }}×{{ size.height }}mm)
              </div>

              <div
                class="paper-size-item"
                :class="{ active: selectedPaperSize === '其他' }"
                @click="selectPaperSize('其他')"
              >
                其他
              </div>
            </div>
          </div>

          <!-- 其他尺寸列表 -->
          <div v-if="selectedPaperSize === '其他'" class="other-sizes-section">
            <div class="other-size-item">
              <span class="checkmark">✓</span>
              A6 (148×105mm)
            </div>
            <div class="other-size-item">B5 (250)</div>
            <div class="other-size-item">B4 (148×105mm)</div>
            <div class="other-size-item">B3 (148×105mm)</div>
            <div class="other-size-item selected">A5 (210×148mm)</div>
          </div>
        </div>

        <div class="multi-right">
          <!-- 排版照画布容器 -->
          <div class="multi-canvas-wrapper">
            <div class="ruler-container" :data-scale="multiScale?.toFixed(2)">
              <!-- 顶部标尺 -->
              <div
                class="ruler-horizontal"
                :style="{
                  width:
                    getCurrentPaperSize().width * 11.81 * multiScale + 'px',
                }"
              >
                <div class="ruler-label">
                  {{ getCurrentPaperSize().width }}mm
                </div>
                <div class="ruler-line"></div>
              </div>
              <!-- 左侧标尺 -->
              <div
                class="ruler-vertical"
                :style="{
                  height:
                    getCurrentPaperSize().height * 11.81 * multiScale + 'px',
                }"
              >
                <div class="ruler-label">
                  {{ getCurrentPaperSize().height }}mm
                </div>
                <div class="ruler-line"></div>
              </div>
              <canvas
                ref="multiCanvas"
                class="multi-canvas"
                :style="canvasStyle"
              />
            </div>
          </div>
        </div>
      </div>
      <el-button type="primary" class="btn flex export" @click="">
        <img src="/img/pic/second2/<EMAIL>" class="icon" />
        <div>立即导出</div>
      </el-button>
    </div>
  </div>
</template>
<script setup lang="ts">
import { ref, nextTick, watch, computed } from 'vue'
import { storeToRefs } from 'pinia'
import { usePassportStore } from '@/pinia/passport'

const currentStore = usePassportStore()
let { showEditDialog, currentImg } = storeToRefs(currentStore)
const currentSegment = ref('单张照')

let naturalWidth = ref(0)
let naturalHeight = ref(0)
let naturalWidthMM = ref(0)
let naturalHeightMM = ref(0)
let scale = ref(1)

// 排版照相关变量
const multiCanvas = ref<HTMLCanvasElement>()
const selectedPaperSize = ref<string>('5寸') // 默认选择5寸，支持自定义
const rows = ref(2) // 默认2行
const cols = ref(5) // 默认5列
let multiScale = ref(1) // 排版缩放比例

// 证件照相纸尺寸配置（毫米）
const paperSizes = {
  '5寸': { width: 127, height: 89 }, // 修正：127mm宽，89mm高
  '6寸': { width: 152, height: 102 }, // 修正：152mm宽，102mm高
  '7寸': { width: 178, height: 127 },
  '8寸': { width: 203, height: 152 },
  A4: { width: 297, height: 210 },
} as const

// 选择相纸尺寸
const selectPaperSize = (size: string) => {
  selectedPaperSize.value = size
  nextTick(() => {
    drawMultiLayout()
  })
}

// 获取当前相纸尺寸（处理自定义情况）
const getCurrentPaperSize = () => {
  if (selectedPaperSize.value === '自定义') {
    return paperSizes['5寸'] // 自定义时使用5寸作为默认
  }
  return (
    paperSizes[selectedPaperSize.value as keyof typeof paperSizes] ||
    paperSizes['5寸']
  )
}

// 计算画布样式
const canvasStyle = computed(() => {
  calculateMultiScale()
  const paperSize = getCurrentPaperSize()
  return {
    width: paperSize.width * 11.81 * multiScale.value + 'px',
    height: paperSize.height * 11.81 * multiScale.value + 'px',
    maxWidth: '620px',
    maxHeight: '450px',
    border: '1px solid #ddd',
    boxShadow: '0px 2px 10px 0px rgba(132, 132, 132, 0.5)',
  }
})

const handleImageLoad = (e: any) => {
  const imgElement = e.target
  naturalWidth.value = imgElement.naturalWidth
  naturalHeight.value = imgElement.naturalHeight
  naturalWidthMM.value = Math.round(naturalWidth.value / 11.81)
  naturalHeightMM.value = Math.round(naturalHeight.value / 11.81)
  // console.log('图片原始宽度:', naturalWidth.value)
  // console.log('图片原始高度:', naturalHeight.value)
  // console.log('图片原始宽度MM:', naturalWidthMM.value)
  // console.log('图片原始高度MM:', naturalHeightMM.value)

  const limitSize = 450
  if (naturalWidth.value > limitSize || naturalHeight.value > limitSize) {
    scale.value =
      limitSize /
      (naturalWidth.value >= naturalHeight.value
        ? naturalWidth.value
        : naturalHeight.value)
  }

  // 当图片加载完成后，如果是排版照模式，需要重新绘制
  if (currentSegment.value === '排版照') {
    nextTick(() => {
      drawMultiLayout()
    })
  }
}

// 计算排版照的缩放比例
const calculateMultiScale = () => {
  const paperSize = getCurrentPaperSize()
  const maxWidth = 620
  const maxHeight = 450

  // 将毫米转换为像素（300dpi标准：1mm = 11.81px）
  const paperWidthPx = paperSize.width * 11.81
  const paperHeightPx = paperSize.height * 11.81

  // 计算缩放比例，使相纸适配到最大显示尺寸
  const scaleX = maxWidth / paperWidthPx
  const scaleY = maxHeight / paperHeightPx
  multiScale.value = Math.min(scaleX, scaleY)
}

// 移除检查函数，根据需求直接按比例缩放适配

// 绘制排版照
const drawMultiLayout = () => {
  if (!multiCanvas.value || !currentImg.value.mattingCropedImage) return

  calculateMultiScale()

  const canvas = multiCanvas.value
  const ctx = canvas.getContext('2d')!
  const paperSize = getCurrentPaperSize()

  // 设置画布尺寸（像素）
  const canvasWidth = paperSize.width * 11.81 * multiScale.value
  const canvasHeight = paperSize.height * 11.81 * multiScale.value
  canvas.width = canvasWidth
  canvas.height = canvasHeight

  // 清空画布
  ctx.fillStyle = '#ffffff'
  ctx.fillRect(0, 0, canvasWidth, canvasHeight)

  // 计算单个格子的尺寸
  const cellWidth = canvasWidth / cols.value
  const cellHeight = canvasHeight / rows.value

  // 绘制网格线
  ctx.strokeStyle = '#ddd'
  ctx.lineWidth = 1

  // 绘制垂直线
  for (let i = 1; i < cols.value; i++) {
    const x = i * cellWidth
    ctx.beginPath()
    ctx.moveTo(x, 0)
    ctx.lineTo(x, canvasHeight)
    ctx.stroke()
  }

  // 绘制水平线
  for (let i = 1; i < rows.value; i++) {
    const y = i * cellHeight
    ctx.beginPath()
    ctx.moveTo(0, y)
    ctx.lineTo(canvasWidth, y)
    ctx.stroke()
  }

  // 绘制外边框
  ctx.strokeStyle = '#ccc'
  ctx.lineWidth = 2
  ctx.strokeRect(0, 0, canvasWidth, canvasHeight)

  // 加载并绘制证件照
  const img = new Image()
  img.crossOrigin = 'anonymous'
  img.onload = () => {
    // 根据需求：直接将原图按比例缩放，以适配当前行列数
    // 计算证件照在每个格子中的尺寸（保持原图比例，适配格子大小）
    const photoScale =
      Math.min(cellWidth / img.width, cellHeight / img.height) * 0.95 // 留一点边距
    const photoWidth = img.width * photoScale
    const photoHeight = img.height * photoScale

    // 在每个格子中绘制证件照
    for (let row = 0; row < rows.value; row++) {
      for (let col = 0; col < cols.value; col++) {
        const x = col * cellWidth + (cellWidth - photoWidth) / 2
        const y = row * cellHeight + (cellHeight - photoHeight) / 2

        ctx.drawImage(img, x, y, photoWidth, photoHeight)
      }
    }
  }
  img.src = currentImg.value.mattingCropedImage
}

// 监听排版参数变化
watch([selectedPaperSize, rows, cols, currentSegment], () => {
  if (currentSegment.value === '排版照') {
    nextTick(() => {
      drawMultiLayout()
    })
  }
})

// 监听当前图片变化，重新绘制排版照
watch(
  () => currentImg.value.mattingCropedImage,
  () => {
    if (currentSegment.value === '排版照') {
      nextTick(() => {
        drawMultiLayout()
      })
    }
  }
)

async function closeDialog() {
  showEditDialog.value = false
}
</script>

<style lang="scss" scoped>
.passport-dialog {
  position: fixed;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  border-radius: 4px;
  z-index: 3001;

  .info {
    position: fixed;
    top: 50%;
    left: 50%;
    -webkit-transform: translate(-50%, -50%);
    transform: translate(-50%, -50%);
    text-align: center;
    width: 984px;
    height: 646px;
    background: #f7f7f7;
    border-radius: 4px;
    @include common-dialog;

    .close {
      position: absolute;
      right: 14px;
      top: 14px;
      margin-bottom: 25px;
      height: 20px;
      width: 20px;
      cursor: pointer;

      &:hover {
        opacity: 0.7;
      }
    }

    .segmented-container {
      position: absolute;
      left: 332px;
      top: 30px;
      border-radius: 6px;

      :deep(.el-segmented) {
        padding: 0;
        background: #fff !important;
        // Override Element Plus segmented control variables
        --el-segmented-item-selected-bg-color: #ffffff;

        .el-segmented__item {
          width: 160px;
          height: 36px;
          background: #fff !important;
          user-select: none;
          .el-segmented__item-label {
            line-height: 20px;
            font-size: 14px;
            font-weight: 500;
            color: #666666;
          }
        }

        .is-selected {
          border-radius: 6px;

          .el-segmented__item-label {
            font-weight: 500;
            color: #389bfd;
          }
        }
        .el-segmented__item-selected {
          border: 1px solid #389bfd;
          border-radius: 6px;
          box-sizing: border-box;
        }
      }
    }

    .single {
      position: relative;
      // top: 132px;
      margin: 130px auto;

      .single-croped-image {
        box-shadow: 0px 2px 10px 0px rgba(132, 132, 132, 0.5);
      }

      .ruler-container {
        position: relative;
        display: inline-block;
      }
      .ruler-horizontal {
        position: absolute;
        top: -28px;
        width: 250px;
        height: 32px;
        display: flex;
        flex-direction: column;
        align-items: center;
        z-index: 2;

        .ruler-label {
          padding-bottom: 4px;
          font-size: 14px;
          color: #666;
        }

        .ruler-line {
          width: 100%;
          height: 0;
          border-top: 1px solid #ccc;
          position: relative;

          &::before,
          &::after {
            content: '';
            position: absolute;
            top: -4px;
            width: 1px;
            height: 7px;
            background: #ccc;
          }

          &::before {
            left: 0;
          }
          &::after {
            right: 0;
          }
        }
      }

      .ruler-vertical {
        position: absolute;
        top: 0;
        left: -8px;
        height: 350px;
        z-index: 2;

        .ruler-label {
          position: absolute;
          width: 120px;
          left: -125px;
          top: calc(50% - 7px);
          font-size: 14px;
          color: #666;
          text-align: right;
        }

        .ruler-line {
          width: 0;
          height: 100%;
          border-left: 1px solid #ccc;
          position: relative;

          &::before,
          &::after {
            content: '';
            position: absolute;
            left: -4px;
            width: 7px;
            height: 1px;
            background: #ccc;
          }

          &::before {
            top: 0;
          }
          &::after {
            bottom: 0;
          }
        }
      }
    }

    .multi {
      position: relative;
      left: 22px;
      top: 110px;
      width: 880px;
      display: flex;
      gap: 30px;

      .multi-left {
        position: relative;
        width: 160px;
        height: 447px;
        margin-right: 30px;
        top: -10px;
        font-weight: 500;

        .section-title {
          font-size: 16px;
          color: #333333;
          margin-bottom: 15px;
          text-align: left;
        }

        .paper-size-list {
          display: flex;
          flex-direction: column;
          gap: 12px;
          margin-bottom: 20px;
          color: #666666;

          .paper-size-item {
            height: 48px;
            border-radius: 4px;
            line-height: 14px;
            padding: 16px 14px;
            font-size: 14px;
            background: #ffffff;
            cursor: pointer;
            transition: all 0.2s;

            &:hover {
              background: #f0f8ff;
              // opacity: .7;
            }

            &.active {
              color: #389bfd;
              outline: 1px solid #389bfd;
            }
          }
        }

        .custom-layout-section {
          .layout-control {
            display: flex;
            align-items: center;
            gap: 10px;
            &:first-child {
              margin-bottom: 10px;
            }

            label {
              font-size: 14px;
              color: #666;
              width: 60px;
            }

            :deep(.el-input-number) {
              width: 160px;
            }
          }
        }

        .other-sizes-section {
          .other-size-item {
            padding: 8px 12px;
            background: #fff;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin-bottom: 8px;
            font-size: 14px;
            color: #333;
            position: relative;

            &.selected {
              border-color: #389bfd;
              background: #f0f8ff;
              color: #389bfd;
            }

            .checkmark {
              color: #389bfd;
              font-weight: bold;
              margin-right: 8px;
            }
          }
        }
      }

      .multi-right {
        flex: 1;

        .multi-canvas-wrapper {
          display: flex;
          justify-content: center;
          align-items: center;

          .ruler-container {
            position: relative;
            display: inline-block;
          }

          .ruler-horizontal {
            position: absolute;
            top: -28px;
            width: 250px;
            height: 32px;
            display: flex;
            flex-direction: column;
            align-items: center;
            z-index: 2;

            .ruler-label {
              padding-bottom: 4px;
              font-size: 14px;
              color: #666;
            }

            .ruler-line {
              width: 100%;
              height: 0;
              border-top: 1px solid #ccc;
              position: relative;

              &::before,
              &::after {
                content: '';
                position: absolute;
                top: -4px;
                width: 1px;
                height: 7px;
                background: #ccc;
              }

              &::before {
                left: 0;
              }
              &::after {
                right: 0;
              }
            }
          }

          .ruler-vertical {
            position: absolute;
            top: 0;
            left: -8px;
            height: 350px;
            z-index: 2;

            .ruler-label {
              position: absolute;
              width: 120px;
              left: -125px;
              top: calc(50% - 7px);
              font-size: 14px;
              color: #666;
              text-align: right;
            }

            .ruler-line {
              width: 0;
              height: 100%;
              border-left: 1px solid #ccc;
              position: relative;

              &::before,
              &::after {
                content: '';
                position: absolute;
                left: -4px;
                width: 7px;
                height: 1px;
                background: #ccc;
              }

              &::before {
                top: 0;
              }
              &::after {
                bottom: 0;
              }
            }
          }

          .multi-canvas {
            display: block;
            background: #fff;
          }
        }
      }
    }

    .export {
      position: absolute;
      right: 32px;
      bottom: 32px;
      width: 120px;
      height: 40px;
      font-weight: 400;
      font-size: 16px;

      .icon {
        width: 18px;
        height: 18px;
        margin-right: 5px;
      }
    }
  }
}
</style>
