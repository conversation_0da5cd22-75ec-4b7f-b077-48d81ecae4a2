<template>
  <div class="mask" />
  <div class="passport-dialog">
    <div class="info flex">
      <img
        class="close"
        @click="closeDialog"
        src="/img/mind_map/table/<EMAIL>"
        alt=""
      />
      <div class="segmented-container">
        <el-segmented
          v-model="currentSegment"
          :options="['单张照', '排版照']"
          block
        />
      </div>
      <div v-if="currentSegment === '单张照'" class="single">
        <div class="ruler-container" :data-scale="scale?.toFixed(2)">
          <!-- 顶部标尺 -->
          <div
            class="ruler-horizontal"
            :style="{ width: naturalWidth * scale + 'px' }"
          >
            <div class="ruler-label">
              {{ naturalWidthMM }}mm / {{ naturalWidth }}px
            </div>
            <div class="ruler-line"></div>
          </div>
          <!-- 左侧标尺 -->
          <div
            class="ruler-vertical"
            :style="{ height: naturalHeight * scale + 'px' }"
          >
            <div class="ruler-label">
              {{ naturalHeightMM }}mm / {{ naturalHeight }}px
            </div>
            <div class="ruler-line"></div>
          </div>
          <img
            class="single-croped-image"
            :src="currentImg.mattingCropedImage"
            :style="{
              width: naturalWidth * scale + 'px',
              height: naturalHeight * scale + 'px',
            }"
            @load="handleImageLoad"
          />
        </div>
      </div>
      <div v-else class="multi">
        <div class="multi-left">
          <!-- 相纸尺寸选择 -->
          <div class="paper-size-section">
            <div class="section-title">相纸尺寸</div>
            <div class="paper-size-list">
              <div
                class="paper-size-item"
                :class="{ active: selectedPaperSize === '自定义' }"
                @click="selectPaperSize('自定义')"
              >
                自定义
              </div>
              <!-- 自定义行列设置 -->
              <div
                v-if="selectedPaperSize === '自定义'"
                class="custom-layout-section"
              >
                <div class="layout-control">
                  <label>目行</label>
                  <el-input-number
                    v-model="layoutRows"
                    :min="1"
                    :max="10"
                    @change="drawMultiLayout"
                  />
                </div>
                <div class="layout-control">
                  <label>四列</label>
                  <el-input-number
                    v-model="layoutCols"
                    :min="1"
                    :max="10"
                    @change="drawMultiLayout"
                  />
                </div>
              </div>
              <div
                v-for="(size, key) in paperSizes"
                :key="key"
                class="paper-size-item"
                :class="{ active: selectedPaperSize === key }"
                @click="selectPaperSize(key)"
              >
                {{ key }} ({{ size.width }}×{{ size.height }}mm)
              </div>

              <div
                class="paper-size-item"
                :class="{ active: selectedPaperSize === '其他' }"
                @click="selectPaperSize('其他')"
              >
                其他
              </div>
            </div>
          </div>

          <!-- 其他尺寸选择器 -->
          <div v-if="selectedPaperSize === '其他'" class="other-sizes-section">
            <el-select
              v-model="selectedOtherSize"
              placeholder="请选择其他尺寸"
              @change="handleOtherSizeChange"
              style="width: 100%"
              clearable
            >
              <el-option
                v-for="(size, key) in otherSizes"
                :key="key"
                :label="`${key} (${size.width}×${size.height}mm)`"
                :value="key"
              />
            </el-select>
          </div>
        </div>

        <div class="multi-right">
          <!-- 排版照画布容器 -->
          <div class="multi-canvas-wrapper">
            <div class="ruler-container" :data-scale="multiScale?.toFixed(2)">
              <!-- 顶部标尺 -->
              <div
                class="ruler-horizontal"
                :style="{
                  width:
                    getCurrentPaperSize().width * 11.81 * multiScale + 'px',
                }"
              >
                <div class="ruler-label">
                  {{ getCurrentPaperSize().width }}mm
                </div>
                <div class="ruler-line"></div>
              </div>
              <!-- 左侧标尺 -->
              <div
                class="ruler-vertical"
                :style="{
                  height:
                    getCurrentPaperSize().height * 11.81 * multiScale + 'px',
                }"
              >
                <div class="ruler-label">
                  {{ getCurrentPaperSize().height }}mm
                </div>
                <div class="ruler-line"></div>
              </div>
              <canvas
                ref="multiCanvas"
                class="multi-canvas"
                :style="canvasStyle"
              />
            </div>
          </div>
        </div>
      </div>
      <el-button type="primary" class="btn flex export" @click="handleExport">
        <img src="/img/pic/second2/<EMAIL>" class="icon" />
        <div>立即导出</div>
      </el-button>
    </div>
  </div>
</template>
<script setup lang="ts">
import { ref, nextTick, watch, computed } from 'vue'
import { storeToRefs } from 'pinia'
import { usePassportStore } from '@/pinia/passport'

const currentStore = usePassportStore()
let { showEditDialog, currentImg, selectedPaperSize, selectedOtherSize, layoutRows, layoutCols } = storeToRefs(currentStore)
const currentSegment = ref('单张照')

let naturalWidth = ref(0)
let naturalHeight = ref(0)
let naturalWidthMM = ref(0)
let naturalHeightMM = ref(0)
let scale = ref(1)

// 排版照相关变量
const multiCanvas = ref<HTMLCanvasElement>()
// selectedPaperSize, selectedOtherSize, layoutRows, layoutCols 现在从store获取
const rows = computed(() => layoutRows.value) // 使用store中的layoutRows
const cols = computed(() => layoutCols.value) // 使用store中的layoutCols
let multiScale = ref(1) // 排版缩放比例

// 证件照相纸尺寸配置（毫米）
const paperSizes = {
  '5寸': { width: 127, height: 89 }, // 修正：127mm宽，89mm高
  '6寸': { width: 152, height: 102 }, // 修正：152mm宽，102mm高
  '7寸': { width: 178, height: 127 },
  '8寸': { width: 203, height: 152 },
  A4: { width: 297, height: 210 },
} as const

// 其他尺寸配置（毫米）
const otherSizes = {
  A6: { width: 148, height: 105 },
  A5: { width: 210, height: 148 },
  A3: { width: 420, height: 297 },
  B5: { width: 250, height: 176 },
  B4: { width: 353, height: 250 },
  B3: { width: 500, height: 353 },
} as const

// 选择相纸尺寸
const selectPaperSize = (size: string) => {
  selectedPaperSize.value = size
  // 如果不是选择"其他"，清空其他尺寸选择
  if (size !== '其他') {
    selectedOtherSize.value = ''
  }
  nextTick(() => {
    drawMultiLayout()
  })
}

// 处理其他尺寸选择变化
const handleOtherSizeChange = (value: string) => {
  selectedOtherSize.value = value
  nextTick(() => {
    drawMultiLayout()
  })
}

// 获取当前相纸尺寸（处理自定义和其他尺寸情况）
const getCurrentPaperSize = () => {
  if (selectedPaperSize.value === '自定义') {
    return paperSizes['5寸'] // 自定义时使用5寸作为默认
  }
  if (selectedPaperSize.value === '其他' && selectedOtherSize.value) {
    return otherSizes[selectedOtherSize.value as keyof typeof otherSizes] || paperSizes['5寸']
  }
  return (
    paperSizes[selectedPaperSize.value as keyof typeof paperSizes] ||
    paperSizes['5寸']
  )
}

// 计算画布样式
const canvasStyle = computed(() => {
  calculateMultiScale()
  const paperSize = getCurrentPaperSize()
  return {
    width: paperSize.width * 11.81 * multiScale.value + 'px',
    height: paperSize.height * 11.81 * multiScale.value + 'px',
    maxWidth: '620px',
    maxHeight: '450px',
    border: '1px solid #ddd',
    boxShadow: '0px 2px 10px 0px rgba(132, 132, 132, 0.5)',
  }
})

const handleImageLoad = (e: any) => {
  const imgElement = e.target
  naturalWidth.value = imgElement.naturalWidth
  naturalHeight.value = imgElement.naturalHeight
  naturalWidthMM.value = Math.round(naturalWidth.value / 11.81)
  naturalHeightMM.value = Math.round(naturalHeight.value / 11.81)
  // console.log('图片原始宽度:', naturalWidth.value)
  // console.log('图片原始高度:', naturalHeight.value)
  // console.log('图片原始宽度MM:', naturalWidthMM.value)
  // console.log('图片原始高度MM:', naturalHeightMM.value)

  const limitSize = 450
  if (naturalWidth.value > limitSize || naturalHeight.value > limitSize) {
    scale.value =
      limitSize /
      (naturalWidth.value >= naturalHeight.value
        ? naturalWidth.value
        : naturalHeight.value)
  }

  // 当图片加载完成后，如果是排版照模式，需要重新绘制
  if (currentSegment.value === '排版照') {
    nextTick(() => {
      drawMultiLayout()
    })
  }
}

// 计算排版照的缩放比例
const calculateMultiScale = () => {
  const paperSize = getCurrentPaperSize()
  const maxWidth = 620
  const maxHeight = 450

  // 将毫米转换为像素（300dpi标准：1mm = 11.81px）
  const paperWidthPx = paperSize.width * 11.81
  const paperHeightPx = paperSize.height * 11.81

  // 计算缩放比例，使相纸适配到最大显示尺寸
  const scaleX = maxWidth / paperWidthPx
  const scaleY = maxHeight / paperHeightPx
  multiScale.value = Math.min(scaleX, scaleY)
}

// 移除检查函数，根据需求直接按比例缩放适配

// 绘制排版照
const drawMultiLayout = () => {
  if (!multiCanvas.value || !currentImg.value.mattingCropedImage) return

  calculateMultiScale()

  const canvas = multiCanvas.value
  const ctx = canvas.getContext('2d')!
  const paperSize = getCurrentPaperSize()

  // 设置画布尺寸（像素）
  const canvasWidth = paperSize.width * 11.81 * multiScale.value
  const canvasHeight = paperSize.height * 11.81 * multiScale.value
  canvas.width = canvasWidth
  canvas.height = canvasHeight

  // 清空画布
  ctx.fillStyle = '#ffffff'
  ctx.fillRect(0, 0, canvasWidth, canvasHeight)

  // 计算单个格子的尺寸
  const cellWidth = canvasWidth / cols.value
  const cellHeight = canvasHeight / rows.value

  // 绘制网格线 - 参考图片样式，使用更清晰的分隔线
  ctx.strokeStyle = '#999999'
  ctx.lineWidth = 1

  // 绘制垂直分隔线
  for (let i = 1; i < cols.value; i++) {
    const x = i * cellWidth
    ctx.beginPath()
    ctx.moveTo(x, 0)
    ctx.lineTo(x, canvasHeight)
    ctx.stroke()
  }

  // 绘制水平分隔线
  for (let i = 1; i < rows.value; i++) {
    const y = i * cellHeight
    ctx.beginPath()
    ctx.moveTo(0, y)
    ctx.lineTo(canvasWidth, y)
    ctx.stroke()
  }

  // 绘制外边框 - 使用更粗的边框
  ctx.strokeStyle = '#666666'
  ctx.lineWidth = 2
  ctx.strokeRect(0, 0, canvasWidth, canvasHeight)

  // 绘制剪裁辅助线 - 在每个格子内部绘制虚线边框
  ctx.strokeStyle = '#cccccc'
  ctx.lineWidth = 1
  ctx.setLineDash([3, 3]) // 设置虚线样式

  for (let row = 0; row < rows.value; row++) {
    for (let col = 0; col < cols.value; col++) {
      const x = col * cellWidth + 2 // 留2px边距
      const y = row * cellHeight + 2
      const width = cellWidth - 4
      const height = cellHeight - 4

      ctx.strokeRect(x, y, width, height)
    }
  }

  // 重置线条样式
  ctx.setLineDash([])

  // 加载并绘制证件照
  const img = new Image()
  img.crossOrigin = 'anonymous'
  img.onload = () => {
    // 根据需求：直接将原图按比例缩放，以适配当前行列数
    // 计算证件照在每个格子中的尺寸（保持原图比例，适配格子大小）
    const photoScale =
      Math.min(cellWidth / img.width, cellHeight / img.height) * 0.95 // 留一点边距
    const photoWidth = img.width * photoScale
    const photoHeight = img.height * photoScale

    // 在每个格子中绘制证件照
    for (let row = 0; row < rows.value; row++) {
      for (let col = 0; col < cols.value; col++) {
        const x = col * cellWidth + (cellWidth - photoWidth) / 2
        const y = row * cellHeight + (cellHeight - photoHeight) / 2

        ctx.drawImage(img, x, y, photoWidth, photoHeight)
      }
    }
  }
  img.src = currentImg.value.mattingCropedImage
}

// 监听排版参数变化
watch([selectedPaperSize, selectedOtherSize, layoutRows, layoutCols, currentSegment], () => {
  if (currentSegment.value === '排版照') {
    nextTick(() => {
      drawMultiLayout()
    })
  }
})

// 监听当前图片变化，重新绘制排版照
watch(
  () => currentImg.value.mattingCropedImage,
  () => {
    if (currentSegment.value === '排版照') {
      nextTick(() => {
        drawMultiLayout()
      })
    }
  }
)

// 处理导出
const handleExport = () => {
  if (currentSegment.value === '单张照') {
    // 单张照导出
    currentStore.beforeConvert('current')
  } else {
    // 排版照导出 - 需要先生成排版照图片
    exportMultiLayout()
  }
}

// 导出排版照
const exportMultiLayout = () => {
  if (!multiCanvas.value) return

  // 获取当前画布内容作为图片
  const canvas = multiCanvas.value
  const dataURL = canvas.toDataURL('image/png', 0.9)

  // 创建临时的图片数据用于导出
  const paperSize = getCurrentPaperSize()
  const exportData = {
    base64: dataURL,
    width: paperSize.width * 11.81, // 转换为像素
    height: paperSize.height * 11.81,
    filename: `排版照_${selectedPaperSize.value}_${rows.value}x${cols.value}`,
  }

  // 根据环境选择导出方式
  if (window.isElectron) {
    // Electron环境 - 保持原数据格式
    window.ipcRendererApi?.send('batch-base64-img-to-file', {
      maps: [exportData],
      options: {},
    })
  } else {
    // Web环境 - 直接下载
    const aLink = document.createElement('a')
    aLink.download = exportData.filename
    aLink.href = dataURL
    aLink.click()
  }
}

async function closeDialog() {
  showEditDialog.value = false
}
</script>

<style lang="scss" scoped>
.passport-dialog {
  position: fixed;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  border-radius: 4px;
  z-index: 3001;

  .info {
    position: fixed;
    top: 50%;
    left: 50%;
    -webkit-transform: translate(-50%, -50%);
    transform: translate(-50%, -50%);
    text-align: center;
    width: 984px;
    height: 646px;
    background: #f7f7f7;
    border-radius: 4px;
    @include common-dialog;

    .close {
      position: absolute;
      right: 14px;
      top: 14px;
      margin-bottom: 25px;
      height: 20px;
      width: 20px;
      cursor: pointer;

      &:hover {
        opacity: 0.7;
      }
    }

    .segmented-container {
      position: absolute;
      left: 332px;
      top: 30px;
      border-radius: 6px;

      :deep(.el-segmented) {
        padding: 0;
        background: #fff !important;
        // Override Element Plus segmented control variables
        --el-segmented-item-selected-bg-color: #ffffff;

        .el-segmented__item {
          width: 160px;
          height: 36px;
          background: #fff !important;
          user-select: none;
          .el-segmented__item-label {
            line-height: 20px;
            font-size: 14px;
            font-weight: 500;
            color: #666666;
          }
        }

        .is-selected {
          border-radius: 6px;

          .el-segmented__item-label {
            font-weight: 500;
            color: #389bfd;
          }
        }
        .el-segmented__item-selected {
          border: 1px solid #389bfd;
          border-radius: 6px;
          box-sizing: border-box;
        }
      }
    }

    .single {
      position: relative;
      // top: 132px;
      margin: 130px auto;

      .single-croped-image {
        box-shadow: 0px 2px 10px 0px rgba(132, 132, 132, 0.5);
      }

      .ruler-container {
        position: relative;
        display: inline-block;
      }
      .ruler-horizontal {
        position: absolute;
        top: -28px;
        width: 250px;
        height: 32px;
        display: flex;
        flex-direction: column;
        align-items: center;
        z-index: 2;

        .ruler-label {
          padding-bottom: 4px;
          font-size: 14px;
          color: #666;
        }

        .ruler-line {
          width: 100%;
          height: 0;
          border-top: 1px solid #ccc;
          position: relative;

          &::before,
          &::after {
            content: '';
            position: absolute;
            top: -4px;
            width: 1px;
            height: 7px;
            background: #ccc;
          }

          &::before {
            left: 0;
          }
          &::after {
            right: 0;
          }
        }
      }

      .ruler-vertical {
        position: absolute;
        top: 0;
        left: -8px;
        height: 350px;
        z-index: 2;

        .ruler-label {
          position: absolute;
          width: 120px;
          left: -125px;
          top: calc(50% - 7px);
          font-size: 14px;
          color: #666;
          text-align: right;
        }

        .ruler-line {
          width: 0;
          height: 100%;
          border-left: 1px solid #ccc;
          position: relative;

          &::before,
          &::after {
            content: '';
            position: absolute;
            left: -4px;
            width: 7px;
            height: 1px;
            background: #ccc;
          }

          &::before {
            top: 0;
          }
          &::after {
            bottom: 0;
          }
        }
      }
    }

    .multi {
      position: relative;
      left: 22px;
      top: 110px;
      width: 880px;
      display: flex;
      gap: 30px;

      .multi-left {
        position: relative;
        width: 160px;
        height: 447px;
        margin-right: 30px;
        top: -10px;
        font-weight: 500;

        .section-title {
          font-size: 16px;
          color: #333333;
          margin-bottom: 15px;
          text-align: left;
        }

        .paper-size-list {
          display: flex;
          flex-direction: column;
          gap: 12px;
          margin-bottom: 20px;
          color: #666666;

          .paper-size-item {
            height: 48px;
            border-radius: 4px;
            line-height: 14px;
            padding: 16px 14px;
            font-size: 14px;
            background: #ffffff;
            text-align: left;
            cursor: pointer;
            transition: all 0.2s;

            &:hover {
              background: #f0f8ff;
              // opacity: .7;
            }

            &.active {
              color: #389bfd;
              outline: 1px solid #389bfd;
            }
          }
        }

        .custom-layout-section {
          .layout-control {
            display: flex;
            align-items: center;
            gap: 10px;
            &:first-child {
              margin-bottom: 10px;
            }

            label {
              font-size: 14px;
              color: #666;
              width: 60px;
            }

            :deep(.el-input-number) {
              width: 160px;
            }
          }
        }

        .other-sizes-section {
          margin-top: 12px;

          :deep(.el-select) {
            .el-input__wrapper {
              background: #ffffff;
              border: 1px solid #ddd;
              border-radius: 4px;
              font-size: 14px;
              color: #333;
              transition: all 0.2s;

              &:hover {
                border-color: #389bfd;
              }

              &.is-focus {
                border-color: #389bfd;
                box-shadow: 0 0 0 2px rgba(56, 155, 253, 0.1);
              }
            }

            .el-input__inner {
              color: #333;
              font-size: 14px;
            }

            .el-select__placeholder {
              color: #999;
            }
          }
        }
      }

      .multi-right {
        flex: 1;

        .multi-canvas-wrapper {
          display: flex;
          justify-content: center;
          align-items: center;

          .ruler-container {
            position: relative;
            display: inline-block;
          }

          .ruler-horizontal {
            position: absolute;
            top: -28px;
            width: 250px;
            height: 32px;
            display: flex;
            flex-direction: column;
            align-items: center;
            z-index: 2;

            .ruler-label {
              padding-bottom: 4px;
              font-size: 14px;
              color: #666;
            }

            .ruler-line {
              width: 100%;
              height: 0;
              border-top: 1px solid #ccc;
              position: relative;

              &::before,
              &::after {
                content: '';
                position: absolute;
                top: -4px;
                width: 1px;
                height: 7px;
                background: #ccc;
              }

              &::before {
                left: 0;
              }
              &::after {
                right: 0;
              }
            }
          }

          .ruler-vertical {
            position: absolute;
            top: 0;
            left: -8px;
            height: 350px;
            z-index: 2;

            .ruler-label {
              position: absolute;
              width: 120px;
              left: -125px;
              top: calc(50% - 7px);
              font-size: 14px;
              color: #666;
              text-align: right;
            }

            .ruler-line {
              width: 0;
              height: 100%;
              border-left: 1px solid #ccc;
              position: relative;

              &::before,
              &::after {
                content: '';
                position: absolute;
                left: -4px;
                width: 7px;
                height: 1px;
                background: #ccc;
              }

              &::before {
                top: 0;
              }
              &::after {
                bottom: 0;
              }
            }
          }

          .multi-canvas {
            display: block;
            background: #fff;
          }
        }
      }
    }

    .export {
      position: absolute;
      right: 32px;
      bottom: 32px;
      width: 120px;
      height: 40px;
      font-weight: 400;
      font-size: 16px;

      .icon {
        width: 18px;
        height: 18px;
        margin-right: 5px;
      }
    }
  }
}
</style>
