<template>
  <div class="manual-refine-box chessboard-bg box">
    <!-- 效果图 -->
    <div
      v-show="!currentStore.isOrigin"
      class="canvas-box"
      :style="{
        width: `${canvasSize.width}px`,
        height: `${canvasSize.height}px`,
        transform: manualCanvasTransform,
        position: 'relative'
      }"
    >
      <!-- 显示画布 - 显示原图 -->
      <canvas
        ref="manualRefineCanvas"
        class="manual-refine-canvas"
        :style="{
          width: '100%',
          height: '100%',
          position: 'absolute',
          top: '0',
          left: '0',
          zIndex: '1',
          cursor: currentStore.maskDragMode ? 'grab' : 'crosshair'
        }"
      />
      <!-- 操作画布 - 用于红色蒙层操作 -->
      <canvas
        ref="redMaskCanvas"
        class="red-mask-canvas"
        :style="{
          width: '100%',
          height: '100%',
          position: 'absolute',
          top: '0',
          left: '0',
          zIndex: '2',
          cursor: currentStore.maskDragMode ? 'grab' : getCursorCss
        }"
        @mousedown="startCanvasDrawing"
        @mousemove="handleCanvasDrawing"
        @mouseup="stopCanvasDrawing"
        @mouseleave="stopCanvasDrawing"
      />
    </div>
    <!-- 原图 -->
    <div
      v-show="currentStore.isOrigin"
      ref="originBoxRef"
      class="origin-box"
      :style="{
        width: `${canvasSize.width}px`,
        height: `${canvasSize.height}px`,
        transform: manualCanvasTransform
      }"
    >
      <img
        class="origin-matting-img"
        :src="currentStore.currentImg.mattingImage"
        :style="{
          width: '100%',
          height: '100%',
          objectFit: 'contain'
        }"
      />
    </div>
    <!-- Moveable 组件用于手动精修的拖拽和缩放 -->
    <Moveable
      v-if="currentStore.currentSegment === currentStore.segmentsType.custom && (currentStore.manualRefineCanvas || originBoxRef)"
      ref="manualMoveableRef"
      :target="currentStore.isOrigin ? '.origin-box' : '.canvas-box'"
      :draggable="currentStore.maskDragMode"
      :scalable="currentStore.maskDragMode"
      :rotatable="false"
      :clickable="false"
      :origin="false"
      :throttleDrag="0"
      :throttleScale="0"
      :renderDirections="[]"
      :edge="false"
      :zoom="1"
      :hideDefaultLines="true"
      :style="{
        visibility: 'hidden'
      }"
      @drag="onManualDrag"
      @scale="onManualScale"
      @dragEnd="onManualTransformEnd"
      @scaleEnd="onManualTransformEnd"
    />
    <!-- 底部操作栏 -->
    <div v-show="currentStore.currentSegment === currentStore.segmentsType.custom" class="info-box flex">
      <div
        class="origin-info center"
        :class="{
          selected: currentStore.isOrigin,
        }"
        @click="handleOriginClick"
      >
        原图
      </div>
      <div
        class="result-info center"
        :class="{
          selected: !currentStore.isOrigin,
        }"
        @click="handleEffectClick"
      >
        效果图
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, watch, nextTick, onMounted, onUnmounted, type PropType } from 'vue'
import { storeToRefs } from 'pinia'
import Moveable from 'vue3-moveable'
import { useMattingStore } from '@/pinia/matting'
import { usePassportStore } from '@/pinia/passport'
const { proxy } = getCurrentInstance()

const props = defineProps({
  currentStore: {
    type: Object as PropType<ReturnType<typeof useMattingStore | typeof usePassportStore>>,
    required: true,
  },
})

// 解构出需要的状态
let {
  currentImg,
  manualRefineCanvas,
  redMaskCanvas,
} = storeToRefs(props.currentStore)

// 画布尺寸
const canvasSize = ref({ width: 0, height: 0 })

// DOM 元素引用
const originBoxRef = ref<HTMLElement | null>(null)
const manualMoveableRef = ref<any>(null)

// 手动精修相关
const isDrawing = ref(false)
const lastX = ref(0)
const lastY = ref(0)

// 手动精修的变换状态
const manualTransform = ref({
  translate: { x: 0, y: 0 },
  scale: { x: 1, y: 1 }
})

// 计算手动精修画布的变换字符串
const manualCanvasTransform = computed(() => {
  const { translate, scale } = manualTransform.value
  return `translate(${translate.x}px, ${translate.y}px) scale(${scale.x}, ${scale.y})`
})

// 获取游标样式
const getCursorCss = computed(() => {
  const tempCanvas = document.createElement('canvas')
  const tempCtx = tempCanvas.getContext('2d')!

  // 计算缩放比例：根据当前画布显示宽度与默认宽度710px的比例
  const defaultWidth = 710
  const currentWidth = canvasSize.value.width || defaultWidth
  const scaleRatio = currentWidth / defaultWidth

  // 根据缩放比例调整画笔大小
  const scaledBrushSize = props.currentStore.brushSize * scaleRatio
  const gapSize = 2
  const radius = scaledBrushSize / 2
  const size = 2 * (radius + gapSize)

  // console.log('======getCursorCss', scaledBrushSize, gapSize)

  // 设置Canvas尺寸
  tempCanvas.width = size
  tempCanvas.height = size

  // 清空Canvas
  tempCtx.clearRect(0, 0, size, size)

  // 绘制圆形
  tempCtx.beginPath()
  tempCtx.lineWidth = 2 * scaleRatio // 线条宽度也根据缩放比例调整
  tempCtx.strokeStyle = '#e63946'
  tempCtx.arc(radius + gapSize, radius + gapSize, radius, 0, 2 * Math.PI)
  tempCtx.stroke()

  // 生成CSS光标值
  const centerOffset = (radius + gapSize).toString()
  const cursorValue = `url('${tempCanvas.toDataURL(
    'image/png'
  )}') ${centerOffset} ${centerOffset}, auto`

  return cursorValue
})

// Moveable 事件处理函数
const onManualDrag = (e: any) => {
  console.log('[ManualRefineBox] 手动精修拖拽:', e.transform)

  // 从 transform 中提取 translate 值
  const translateMatch = e.transform.match(/translate\(([^,]+),\s*([^)]+)\)/)
  if (translateMatch && translateMatch.length >= 3) {
    const translateX = parseFloat(translateMatch[1])
    const translateY = parseFloat(translateMatch[2])

    manualTransform.value.translate = { x: translateX, y: translateY }
    console.log('[ManualRefineBox] 更新拖拽位置:', manualTransform.value.translate)
  }
}

const onManualScale = (e: any) => {
  console.log('[ManualRefineBox] 手动精修缩放:', e.transform)

  // 从 transform 中提取 scale 值
  const scaleMatch = e.transform.match(/scale\(([^,]+),\s*([^)]+)\)/)
  if (scaleMatch && scaleMatch.length >= 3) {
    const scaleX = parseFloat(scaleMatch[1])
    const scaleY = parseFloat(scaleMatch[2])

    manualTransform.value.scale = { x: scaleX, y: scaleY }
    console.log('[ManualRefineBox] 更新缩放比例:', manualTransform.value.scale)

    // 同步更新 store 中的缩放值
    props.currentStore.maskZoom = Math.max(scaleX, scaleY)
  }
}

const onManualTransformEnd = () => {
  console.log('[ManualRefineBox] 手动精修变换结束')

  // 更新 Moveable 组件
  if (manualMoveableRef.value) {
    manualMoveableRef.value.updateRect()
  }
}

// 处理原图按钮点击
const handleOriginClick = () => {
  console.log('[ManualRefineBox] 点击原图按钮')

  // 先更新抠图图像
  updateMattingImageFromCanvas()

  // 切换到原图模式
  props.currentStore.isOrigin = true
}

// 处理效果图按钮点击
const handleEffectClick = () => {
  console.log('[ManualRefineBox] 点击效果图按钮')

  // 切换到效果图模式
  props.currentStore.isOrigin = false
}

// ================ 画布绘制相关函数 ================

const hasVisibleContent = (canvasElement: any) => {
  const ctx: any = canvasElement.getContext('2d');
  const imageData = ctx.getImageData(0, 0, canvasElement.width, canvasElement.height);
  const data = imageData.data;

  // 遍历所有像素的Alpha通道（索引3、7、11...）
  for (let i = 3; i < data.length; i += 4) {
    if (data[i] > 0) {
      return true; // 发现非透明像素
    }
  }

  return false; // 所有像素都是透明的
}

// 初始化画布
const initCanvas = (canAddHistory: boolean = true) => {
  if (!manualRefineCanvas.value || !redMaskCanvas.value || !currentImg.value?.mattingImage || !currentImg.value?.file) {
    console.warn('[ManualRefineBox] 无法初始化画布:', {
      manualRefineCanvas: !!manualRefineCanvas.value,
      redMaskCanvas: !!redMaskCanvas.value,
      mattingImage: !!currentImg.value?.mattingImage,
      originalFile: !!currentImg.value?.file,
    })
    return
  }
  console.log('[ManualRefineBox] 开始初始化画布')

  // 首先计算画布尺寸
  calculateCanvasSize()

  const canvas = manualRefineCanvas.value
  const ctx = canvas.getContext('2d')
  const redMaskCanvasEl = redMaskCanvas.value
  const redMaskCtx = redMaskCanvasEl.getContext('2d')

  if (!ctx || !redMaskCtx) {
    console.error('[ManualRefineBox] 无法获取画布上下文')
    return
  }

  props.currentStore.redMaskCanvasContext = redMaskCtx
  console.log('[ManualRefineBox] 画布上下文已设置')

  // 加载原始图像 (currentImg.file)
  const originalImg = new Image()
  originalImg.crossOrigin = 'anonymous'
  originalImg.onload = () => {
    console.log('[ManualRefineBox] 原始图像加载完成, 尺寸:', originalImg.width, 'x', originalImg.height)

    // 设置两个画布的内部尺寸
    canvas.width = originalImg.width
    canvas.height = originalImg.height
    redMaskCanvasEl.width = originalImg.width
    redMaskCanvasEl.height = originalImg.height
    console.log('[ManualRefineBox] 画布内部尺寸已设置为:', canvas.width, 'x', canvas.height)

    // 0. 完全清空两个画布
    ctx.clearRect(0, 0, canvas.width, canvas.height)
    redMaskCtx.clearRect(0, 0, redMaskCanvasEl.width, redMaskCanvasEl.height)
    console.log('[ManualRefineBox] 画布已完全清空')

    // 1. 在显示画布上用原始图像填满画布（作为背景）
    ctx.drawImage(originalImg, 0, 0)
    console.log('[ManualRefineBox] 原始图像已填满显示画布作为背景')

    // 2. 同时在红色蒙层画布上也添加初始红色蒙层
    addPortraitRedOverlay(redMaskCtx, originalImg.width, originalImg.height, canAddHistory)

    console.log('[ManualRefineBox] 初始蒙层状态已保存到历史记录')
  }
  originalImg.onerror = () => {
    console.error('[ManualRefineBox] 原始图像加载失败')
  }

  // 使用原始文件创建 URL
  if (currentImg.value.file instanceof File) {
    originalImg.src = URL.createObjectURL(currentImg.value.file)
  } else {
    console.error('[ManualRefineBox] 原始文件不是有效的 File 对象')
  }
}

// 在人像区域添加红色相对透明蒙层（基于当前蒙版）
const addPortraitRedOverlay = (ctx: CanvasRenderingContext2D, width: number, height: number, canAddHistory: boolean = true) => {
  console.log('[ManualRefineBox] 开始基于当前蒙版添加红色蒙层')

  // 使用 mattingImage 来确定人像区域（基于透明度）
  if (!currentImg.value.mattingImage) {
    console.warn('[ManualRefineBox] 没有抠图图像，无法添加红色蒙层')
    return
  }

  // 加载抠图图像
  const mattingImg = new Image()
  mattingImg.crossOrigin = 'anonymous'
  mattingImg.onload = () => {
    console.log('[ManualRefineBox] 抠图图像加载完成，开始分析并添加红色蒙层')

    // 创建临时画布来处理抠图图像
    const tempCanvas = document.createElement('canvas')
    tempCanvas.width = width
    tempCanvas.height = height
    const tempCtx = tempCanvas.getContext('2d')
    if (!tempCtx) return

    // 绘制抠图图像到临时画布，显式匹配画布尺寸
    tempCtx.drawImage(mattingImg, 0, 0, width, height)

    // 获取抠图图像的像素数据
    const imageData = tempCtx.getImageData(0, 0, width, height)
    const imagePixels = imageData.data

    // 创建红色蒙层画布
    const overlayCanvas = document.createElement('canvas')
    overlayCanvas.width = width
    overlayCanvas.height = height
    const overlayCtx = overlayCanvas.getContext('2d')
    if (!overlayCtx) return

    // 创建蒙层的像素数据
    const overlayData = overlayCtx.createImageData(width, height)
    const overlayPixels = overlayData.data

    // 遍历每个像素，在人像区域（不透明区域）创建红色相对透明蒙层
    for (let i = 0; i < imagePixels.length; i += 4) {
      const alpha = imagePixels[i + 3]
      // 只保留 Alpha 人像区域
      const isPortraitArea = alpha > 0

      if (isPortraitArea) {
        // 在人像区域设置红色相对透明蒙层 (#F60000 相对透明)
        overlayPixels[i] = 246     // R
        overlayPixels[i + 1] = 0   // G
        overlayPixels[i + 2] = 0   // B
        // 根据原始alpha值动态调整蒙层透明度，实现边缘羽化效果
        overlayPixels[i + 3] = Math.min(77, Math.floor(alpha * 0.3))
      } else {
        // 非人像区域保持透明
        overlayPixels[i] = 0
        overlayPixels[i + 1] = 0
        overlayPixels[i + 2] = 0
        overlayPixels[i + 3] = 0
      }
    }

    // 将蒙层数据绘制到蒙层画布
    overlayCtx.putImageData(overlayData, 0, 0)

    // 设置混合模式为 source-over，确保正确叠加
    ctx.globalCompositeOperation = 'source-over'

    // 设置画布高质量抗锯齿
    overlayCtx.imageSmoothingQuality = 'high'
    // 将红色蒙层绘制到主画布上（叠加在原始图像上）
    ctx.drawImage(overlayCanvas, 0, 0)

    console.log('[ManualRefineBox] 红色蒙层已添加到人像区域')

    canAddHistory && props.currentStore.addMaskHistory()
  }

  mattingImg.onerror = () => {
    console.error('[ManualRefineBox] 抠图图像加载失败')
  }

  mattingImg.src = currentImg.value.mattingImage
}

// 绘制红色透明蒙层
const drawRedMask = (x: number, y: number) => {
  const ctx: any = props.currentStore.redMaskCanvasContext

  if (!ctx) return

  // 设置画笔样式
  ctx.lineJoin = 'round'
  ctx.lineCap = 'round'
  ctx.lineWidth = props.currentStore.brushSize
  // 设置画布高质量抗锯齿
  ctx.imageSmoothingQuality = 'high'

  // 根据画笔类型设置颜色和混合模式
  if (props.currentStore.brushType === 'keep') {
    // 保留模式：增加红色相对透明蒙层
    ctx.globalCompositeOperation = 'source-over'
    ctx.strokeStyle = 'rgba(246, 0, 0, 0.3)' // #F60000 相对透明
  } else {
    // 擦除模式：移除红色蒙层部分
    ctx.globalCompositeOperation = 'destination-out'
    ctx.strokeStyle = 'rgba(255, 255, 255, 1)'
  }

  // 设置边缘柔和度
  if (props.currentStore.edgeSoftness > 0) {
    ctx.shadowColor = props.currentStore.brushType === 'keep' ? 'rgba(246, 0, 0, 0.3)' : 'rgba(255, 255, 255, 1)'
    ctx.shadowBlur = props.currentStore.edgeSoftness
  } else {
    ctx.shadowBlur = 0
  }

  // 开始绘制连续线条
  ctx.beginPath()
  ctx.moveTo(lastX.value, lastY.value)
  // 坐标对齐到像素中心：Canvas 的 1px 线宽实际上是在路径两侧各渲染 0.5px
  // 如果路径坐标是小数（如(100.5, 150)），线条会跨两个像素，导致模糊或毛刺
  ctx.lineTo(Math.floor(x) + 0.5, Math.floor(y) + 0.5)
  ctx.stroke()

  // 重置设置
  ctx.shadowBlur = 0
  ctx.globalCompositeOperation = 'source-over'
  ctx.globalAlpha = 1.0 // 重置透明度

  normalizeRedMaskColors() // 修正重色区域
}

// 开始绘制
const startCanvasDrawing = (e: MouseEvent) => {
  console.log('[ManualRefineBox] 开始画布绘制')

  if (!props.currentStore.redMaskCanvasContext || props.currentStore.maskDragMode) {
    console.log('[ManualRefineBox] 跳过绘制:', {
      redMaskCanvasContext: !!props.currentStore.redMaskCanvasContext,
      maskDragMode: props.currentStore.maskDragMode,
    })
    return
  }

  isDrawing.value = true

  // 获取鼠标相对于操作画布的位置
  const rect = redMaskCanvas.value?.getBoundingClientRect()
  if (!rect) {
    console.error('[ManualRefineBox] 无法获取操作画布边界信息')
    return
  }

  // 计算缩放比例
  const scaleX = redMaskCanvas.value!.width / rect.width
  const scaleY = redMaskCanvas.value!.height / rect.height

  const startX = (e.clientX - rect.left) * scaleX
  const startY = (e.clientY - rect.top) * scaleY

  // 更新最后的位置
  lastX.value = startX
  lastY.value = startY

  drawRedMask(startX, startY) // 位置不能更换
}

const handleCanvasDrawing = (e: MouseEvent) => {
  if (!isDrawing.value || !props.currentStore.redMaskCanvasContext || !redMaskCanvas.value || props.currentStore.maskDragMode) {
    return
  }

  // 获取鼠标相对于操作画布的位置
  const rect = redMaskCanvas.value.getBoundingClientRect()
  if (!rect) return

  // 计算缩放比例
  const scaleX = redMaskCanvas.value.width / rect.width
  const scaleY = redMaskCanvas.value.height / rect.height

  const x = (e.clientX - rect.left) * scaleX
  const y = (e.clientY - rect.top) * scaleY

  drawRedMask(x, y) // 位置不能更换

  // 更新最后的位置
  lastX.value = x
  lastY.value = y
}

const stopCanvasDrawing = () => {
  console.log('[ManualRefineBox] 停止画布绘制')

  if (isDrawing.value && props.currentStore.redMaskCanvasContext && redMaskCanvas.value) {
    isDrawing.value = false

    updateMattingImageFromCanvas()
    props.currentStore.addMaskHistory()

    console.log('[ManualRefineBox] 画笔操作完成，历史记录已保存')
  }
}

// 修正重色区域：将redMaskCanvas有色区域都改成 标准色
const normalizeRedMaskColors = () => {
  if (!props.currentStore.redMaskCanvasContext || !redMaskCanvas.value) return

  console.log('[ManualRefineBox] 开始标准化红色蒙层颜色')

  const canvas = redMaskCanvas.value
  const ctx = props.currentStore.redMaskCanvasContext

  // 获取画布的像素数据
  const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height)
  const pixels = imageData.data

  // 遍历每个像素，将有色区域都改成标准的红色
  for (let i = 0; i < pixels.length; i += 4) {
    const alpha = pixels[i + 3]

    // 如果像素有颜色（alpha > 0），则将其改成标准的红色
    if (alpha > 0) {
      pixels[i] = 246     // R
      pixels[i + 1] = 0   // G
      pixels[i + 2] = 0   // B
      pixels[i + 3] = Math.min(77, alpha) // 保留原来的羽化度
    }
  }

  // 将修改后的像素数据放回画布
  ctx.putImageData(imageData, 0, 0)

  console.log('[ManualRefineBox] 红色蒙层颜色标准化完成')
}

// 从操作画布提取蒙层信息并更新 mattingImage、mattingCropImage
const updateMattingImageFromCanvas = () => {
  if (!props.currentStore.redMaskCanvasContext || !redMaskCanvas.value || !currentImg.value?.file) {
    console.warn('[ManualRefineBox] 无法更新 mattingImage，缺少必要条件')
    return
  }

  console.log('[ManualRefineBox] 开始从操作画布提取蒙层信息并生成新抠图')

  const redMaskCanvasEl = redMaskCanvas.value
  const redMaskCtx = props.currentStore.redMaskCanvasContext

  // 获取操作画布的像素数据（红色蒙层）
  const redMaskData = redMaskCtx.getImageData(0, 0, redMaskCanvasEl.width, redMaskCanvasEl.height)
  const redMaskPixels = redMaskData.data

  // 创建新的 mattingImage
  const mattingCanvas = document.createElement('canvas')
  mattingCanvas.width = redMaskCanvasEl.width
  mattingCanvas.height = redMaskCanvasEl.height
  const mattingCtx = mattingCanvas.getContext('2d')
  if (!mattingCtx) return

  // 加载原始图像
  const originalImg = new Image()
  originalImg.crossOrigin = 'anonymous'
  originalImg.onload = () => {
    console.log('[ManualRefineBox] 原始图像加载完成，开始生成新抠图')

    // 设置画布高质量抗锯齿
    mattingCtx.imageSmoothingQuality = 'high'
    // 绘制原始图像到画布
    mattingCtx.drawImage(originalImg, 0, 0)

    // 获取原始图像的像素数据
    const mattingData = mattingCtx.getImageData(0, 0, mattingCanvas.width, mattingCanvas.height)
    const mattingPixels = mattingData.data

    // 根据红色蒙层生成新的抠图，参考 addPortraitRedOverlay 的羽化方法
    for (let i = 0; i < mattingPixels.length; i += 4) {
      // 获取红色蒙层的alpha值
      const redMaskAlpha = redMaskPixels[i + 3]

      if (redMaskAlpha > 0) {
        // 有红色蒙层的区域保留原图像素
        // 使用红色蒙层的alpha值来设置最终的透明度，实现羽化效果
        // 参考 addPortraitRedOverlay 中的公式：Math.min(77, Math.floor(alpha * 0.3))
        // 这里我们反向计算，将蒙层的alpha值映射到最终透明度
        const finalAlpha = Math.min(255, Math.floor(redMaskAlpha * 3.3)) // 反向映射
        mattingPixels[i + 3] = finalAlpha
      } else {
        // 没有红色蒙层的区域设置为透明
        mattingPixels[i + 3] = 0 // 完全透明
      }
    }

    // 将处理后的图像数据放回画布
    mattingCtx.putImageData(mattingData, 0, 0)

    // 更新抠图图像
    currentImg.value.mattingImage = mattingCanvas.toDataURL('image/png', 0.95)
    console.log('[ManualRefineBox] 新抠图已生成并更新')

    // 触发改尺寸模块更新
    props.currentStore.createMattingCropImage()
    console.log('[ManualRefineBox] 已触发改尺寸模块更新')
  }

  originalImg.onerror = () => {
    console.error('[ManualRefineBox] 原始图像加载失败')
  }

  // 使用原始文件创建 URL
  if (currentImg.value.file instanceof File) {
    originalImg.src = URL.createObjectURL(currentImg.value.file)
  }
}

// 计算画布尺寸
const calculateCanvasSize = async () => {
  try {
    const result = await props.currentStore.calculateCanvasSize()
    if (result) {
      canvasSize.value = result
      
      // 重置位置和缩放
      manualTransform.value = {
        translate: { x: 0, y: 0 },
        scale: { x: 1, y: 1 }
      }
      // 同步重置 store 中的缩放值
      props.currentStore.maskZoom = 1
    }
  } catch (error) {
    console.error('[ManualRefineBox] 计算画布尺寸失败:', error)
  }
}

// 监听 store 中的缩放值变化，同步到 Moveable
watch(() => props.currentStore.maskZoom, (newZoom) => {
  console.log('[ManualRefineBox] Store 缩放值变化:', newZoom)

  // 如果缩放值变化不是由 Moveable 引起的，则更新 Moveable 的缩放
  if (Math.abs(manualTransform.value.scale.x - newZoom) > 0.01) {
    manualTransform.value.scale = { x: newZoom, y: newZoom }
    console.log('[ManualRefineBox] 同步缩放到 Moveable:', newZoom)

    // 如果是重置操作（缩放为1），同时重置位置
    if (newZoom === 1) {
      manualTransform.value.translate = { x: 0, y: 0 }
      console.log('[ManualRefineBox] 重置操作，同时重置位置')
    }

    // 更新 Moveable 组件
    if (manualMoveableRef.value) {
      nextTick(() => {
        manualMoveableRef.value.updateRect()
      })
    }
  }
})

// 监听当前图片变化，重新计算画布尺寸
watch(() => currentImg.value?.mattingImage, () => {
  if (currentImg.value?.mattingImage) {
    nextTick(() => calculateCanvasSize())
  }
})

// 监听手动精修模式变化，初始化画布
watch(() => props.currentStore.currentSegment, (newSegment) => {
  if (newSegment === props.currentStore.segmentsType.custom) {
    console.log('[ManualRefineBox] 进入手动精修模式，检查是否需要初始化画布')

    // 只有在历史记录为空时才进行初始化（真正的第一次进入）
    if (props.currentStore.maskHistory.length === 0) {
      console.log('[ManualRefineBox] 首次进入手动精修模式，初始化画布')
      nextTick(() => initCanvas(true))
    } else {
      console.log('[ManualRefineBox] 已有历史记录，根据条件判断是否初始化')
      if (!hasVisibleContent(redMaskCanvas.value)) {
        nextTick(() => initCanvas(false))
      }
    }
  }
})

// 处理窗口大小变化
const handleResize = () => {
  // 如果当前在手动精修模式，重新计算画布尺寸
  if (props.currentStore.currentSegment === props.currentStore.segmentsType.custom) {
    console.log('[ManualRefineBox] 窗口大小变化，重新计算画布尺寸')
    nextTick(() => calculateCanvasSize())
  }
}

// 处理蒙版图片抠图更新 mattingImage、mattingCropImage 事件的防抖函数
const handleMattingImageUpdated = () => {
  // 从操作画布提取蒙层信息并更新 mattingImage、mattingCropImage
  proxy?.$filter?.tools.debounce(() => updateMattingImageFromCanvas(), 300)()
}

onMounted(() => {
  // 添加窗口大小变化监听
  window.addEventListener('resize', handleResize)

  // 添加蒙版更新事件监听
  window.addEventListener('mattingImageUpdated', handleMattingImageUpdated)

  // 设置canvas refs到store
  if (manualRefineCanvas.value) {
    props.currentStore.manualRefineCanvas = manualRefineCanvas.value
  }
  if (redMaskCanvas.value) {
    props.currentStore.redMaskCanvas = redMaskCanvas.value
  }

  // 如果当前就在手动精修模式且有mattingImage，进行初始化
  if (props.currentStore.currentSegment === props.currentStore.segmentsType.custom && currentImg.value?.mattingImage) {
    // 只有在历史记录为空时才进行初始化（真正的第一次进入）
    if (props.currentStore.maskHistory.length === 0) {
      console.log('[ManualRefineBox] 组件挂载时首次初始化画布')
      nextTick(() => initCanvas(true))
    } else {
      console.log('[ManualRefineBox] 组件挂载时已有历史记录，根据条件判断是否初始化')
      if (!hasVisibleContent(redMaskCanvas.value)) {
        nextTick(() => initCanvas(false))
      }
    }
  } else if (currentImg.value?.mattingImage) {
    // 不在手动精修模式，只计算画布尺寸
    nextTick(() => calculateCanvasSize())
  }
})

onUnmounted(() => {
  // 移除窗口大小变化监听
  window.removeEventListener('resize', handleResize)

  // 移除蒙版更新事件监听
  window.removeEventListener('mattingImageUpdated', handleMattingImageUpdated)
})

// 暴露方法给父组件使用
// defineExpose({
//   calculateCanvasSize,
// })
</script>

<style lang="scss" scoped>
// .box {
//   position: absolute;
//   margin: 0 47px;
//   width: calc(100% - 289px - 94px);
//   height: calc(100% - 202px);
//   box-shadow: 0px 0px 20px 0px rgba(0, 0, 0, 0.07);
// }
// 手动精修
.manual-refine-box {
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;

  .manual-refine-canvas {
    cursor: crosshair;
    background: transparent;
    transform-origin: center center;

    // 当处于拖动模式时改变光标
    &.drag-mode {
      cursor: grab;

      &:active {
        cursor: grabbing;
      }
    }
  }

  .origin-box {
    cursor: default;
    background: transparent;
    transform-origin: center center;
    -webkit-user-drag: none;
    user-select: none;

    .origin-matting-img {
      width: 100%;
      height: 100%;
      object-fit: contain;
      pointer-events: none;
    }
  }

  .info-box {
    position: fixed;
    bottom: 45px;
    user-select: none;

    height: 40px;
    font-weight: 500; // electron
    font-size: 14px;
    color: #333;
    line-height: 20px;
    border-radius: 8px;
    background-color: #fff;

    .origin-info {
      padding: 0px 37px;
      text-wrap: nowrap;
      cursor: pointer;
    }

    .result-info {
      padding: 0px 30px;
      text-wrap: nowrap;
      cursor: pointer;
    }

    .selected {
      border-radius: 6px;
      color: #fff;
      background: #389bfd;
      transition: all 0.5s;
    }
  }
}
</style>
