<template>
  <div class="cropper-box box">
    <img
      ref="cropperRef"
      :src="currentStore.currentImg.mattingCropImage"
      @load="initCropper"
      @cropend="currentStore.updateCropBoxData"
    />
  </div>
</template>

<script lang="ts" setup>
import { watch, type PropType } from 'vue'
import { storeToRefs } from 'pinia'
import Cropper from 'cropperjs'
import 'cropperjs/dist/cropper.css'
import { useMattingStore } from '@/pinia/matting'
import { usePassportStore } from '@/pinia/passport'

const props = defineProps({
  currentStore: {
    type: Object as PropType<ReturnType<typeof useMattingStore | typeof usePassportStore>>, // 临时类型，后期扩展再改掉
    required: true,
  },
})

// 解构出需要的状态
let { currentImg, option, cropper, cropperRef } = storeToRefs(
  props.currentStore
)

// 监听 currentImg 变化
watch(
  () => [currentImg.value],
  async () => {
    try {
      clearCropper()
    } catch (error) {
      console.error('Error clearing cropper:', error)
    }
  }
)

// 初始化裁剪器
const initCropper = () => {
  console.log('initCropper');
  if (cropperRef.value) {
    try {
      cropper.value = new Cropper(cropperRef.value, {
        dragMode: option.value.dragMode as any,
        viewMode: option.value.viewMode as any,
        guides: option.value.guides, // 是否显示网格线
        movable: option.value.movable, // 图片是否可移动
        zoomable: option.value.zoomable, // 是否可以缩放图片（以图片左上角为原点进行缩放）
        autoCrop: option.value.autoCrop, // 是否自动裁剪
        autoCropArea: option.value.autoCropArea, // 默认裁剪区域大小，值为0-1，表示裁剪框占图片的比例，1表示裁剪框与图片一样大
        cropBoxMovable: option.value.cropBoxMovable, // 裁剪框是否可移动
        cropBoxResizable: !option.value.cropBoxResizable, // 裁剪框是否可调整大小
        minCropBoxWidth: option.value.minCropBoxWidth, // 裁剪框最小宽度
        minCropBoxHeight: option.value.minCropBoxHeight, // 裁剪框最小高度
        aspectRatio: option.value.aspectRatio || 0, // 裁剪框最小高度

        ready() {
          if (!cropper.value) return
          // console.log(111, currentImg.value?.record)
          if (currentImg.value?.record?.aspectRatio) {
            // console.log(222)
            cropper.value.setAspectRatio(currentImg.value?.record?.aspectRatio)
          }
          if (currentImg.value?.record?.left) {
            // console.log(333)
            cropper.value.setCropBoxData({
              left: currentImg.value?.record?.left || 0,
              top: currentImg.value?.record?.top || 0,
              width: currentImg.value?.record?.autoCropWidth || undefined,
              height: currentImg.value?.record?.autoCropHeight || undefined,
            })
          }
          cropper.value.crop() // 手动触发裁剪框显示
          // console.log(`Cropper 初始化完成`, cropper.value);
          props.currentStore.updateCropBoxData()
          // props.currentStore.changeSegmenting = false
        },
        cropend() {
          console.log('cropend')
          if (!currentImg.value.record.aspectRatio && currentImg.value.record.name !== '自由') {
            currentImg.value.record.name = '自由'
          }
        },
      })
    } catch (error) {
      console.error('初始化裁剪器失败:', error)
    }
  }
}

// 销毁旧的裁剪器实例
const clearCropper = () => {
  console.log('clearCropper')
  if (cropper.value) {
    cropper.value.destroy()
    cropper.value = null
  }
}

// 暴露方法给父组件使用
defineExpose({
  clearCropper,
})
</script>

<style lang="scss" scoped>
// .box {
//   position: absolute;
//   margin: 0 47px;
//   width: calc(100% - 289px - 94px);
//   height: calc(100% - 202px);
//   box-shadow: 0px 0px 20px 0px rgba(0, 0, 0, 0.07);
// }
// 改尺寸样式
.cropper-box {
  overflow: hidden;

  img {
    // max-width: 100%;
    // max-height: 100%;
    -webkit-user-drag: none;
  }

  :deep(.cropper-crop-box) {
    position: relative;
    box-shadow: 0px 2px 9px 0px rgba(0, 0, 0, 0.31);
    .cropper-view-box {
      outline: 1px solid #ffffff;
    }
    /* 为裁剪框添加网格线 */
    &::before,
    &::after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      pointer-events: none;
      z-index: 1;
    }
    /* 垂直网格线 */
    &::before {
      background-image: linear-gradient(to right, #fff 1px, transparent 1px);
      background-size: 33.33% 100%;
    }
    /* 水平网格线 */
    &::after {
      background-image: linear-gradient(to bottom, #fff 1px, transparent 1px);
      background-size: 100% 33.33%;
    }

    .cropper-line {
      background-color: white;
    }

    .cropper-dashed {
      border: 0 solid #fff;
      border-left-width: 1px;
      border-right-width: 1px;
      border-bottom-width: 1px;
      border-top-width: 1px;
    }

    .cropper-point {
      opacity: 1;
      background-color: white;
    }

    .point-n,
    .point-s {
      display: none;
    }

    .point-nw {
      // 左上
      width: 26px;
      height: 26px;
      border: 6px solid #ffffff;
      border-radius: 0;
      border-right-width: 0;
      border-bottom-width: 0;
      background-color: transparent;
    }
    .point-ne {
      // 右上
      width: 26px;
      height: 26px;
      border: 6px solid #ffffff;
      border-radius: 0;
      border-left-width: 0;
      border-bottom-width: 0;
      background-color: transparent;
    }
    .point-w {
      // 左中
      width: 6px;
      height: 26px;
      background: #ffffff;
      border-radius: 0;
      margin-top: -12px;
    }
    .point-e {
      // 右中
      width: 6px;
      height: 26px;
      background: #ffffff;
      border-radius: 0;
      margin-top: -12px;
    }
    .point-sw {
      // 左下
      width: 26px;
      height: 26px;
      border: 6px solid #ffffff;
      border-radius: 0;
      border-top-width: 0;
      border-right-width: 0;
      background-color: transparent;
    }
    .point-se {
      // 右下
      width: 26px;
      height: 26px;
      border: 6px solid #ffffff;
      border-radius: 0;
      border-top-width: 0;
      border-left-width: 0;
      background-color: transparent;
    }
  }
}
</style>
