<template>
  <div class="background-box chessboard-bg box">
    <div
      class="background-container"
      :style="{
        background:
          currentStore.currentImg.backgroundColor === 'transparent' ||
          currentStore.currentImg.backgroundImage
            ? ''
            : currentStore.currentImg.backgroundColor,
      }"
    >
      <img
        v-if="currentStore.currentImg.backgroundImage"
        class="background-image"
        :src="currentStore.currentImg.backgroundImage"
      />
      <img
        v-show="currentStore.currentImg.mattingImage"
        class="matting-image"
        ref="mattingImageRef"
        :src="currentStore.currentImg.mattingImage"
        :style="{
          opacity: currentStore.currentImg.record?.opacity,
          transform: `
            translate(${currentStore.currentImg.record?.translate?.x || 0}px, ${currentStore.currentImg.record?.translate?.y || 0}px)
            rotate(${currentStore.currentImg.record?.rotate || 0}deg)
            scale(${
              currentStore.currentImg.record?.scaleX ||
              currentStore.currentImg.record?.zoom ||
              1
            }, ${
              currentStore.currentImg.record?.scaleY ||
              currentStore.currentImg.record?.zoom ||
              1
            })
            scaleX(${currentStore.currentImg.record?.transformX || 1})
            scaleY(${currentStore.currentImg.record?.transformY || 1})
          `,
        }"
      />
      <Moveable
        v-if="currentStore.currentImg.mattingImage"
        ref="moveableRef"
        :target="'.matting-image'"
        :draggable="true"
        :scalable="true"
        :rotatable="true"
        :clickable="true"
        :origin="false"
        :throttleDrag="1"
        :throttleRotate="1"
        :throttleScale="0.01"
        :style="{
          visibility: isMoveableActive ? 'visible' : 'hidden',
        }"
        @drag="onDrag"
        @scale="onScale"
        @rotate="onRotate"
        @dragEnd="onTransformEnd"
        @scaleEnd="onTransformEnd"
        @rotateEnd="onTransformEnd"
        @click="isMoveableActive = true"
      />
    </div>
    <!-- 抠图进度条 -->
    <div v-if="showProgress" class="matting-progress">
      <el-progress type="circle" :percentage="progressValue" />
    </div>

    <FooterTool
      v-show="
        currentStore.currentSegment !==
        currentStore.segmentsType.custom
      "
      :imgsMap="imgsMap"
    />
  </div>
</template>

<script lang="ts" setup>
import { ref, watch, nextTick, onMounted, onUnmounted, type PropType } from 'vue'
import { storeToRefs } from 'pinia'
import Moveable from 'vue3-moveable'
import { useMattingStore } from '@/pinia/matting'
import { usePassportStore } from '@/pinia/passport'
import FooterTool from '@/components/Common/FooterTool.vue'
const { proxy } = getCurrentInstance()

const props = defineProps({
  currentStore: {
    type: Object as PropType<ReturnType<typeof useMattingStore | typeof usePassportStore>>,
    required: true,
  },
  imgsMap: {
    type: Map,
    required: true,
  },
  showProgress: {
    type: Boolean,
    default: false,
  },
  progressValue: {
    type: Number,
    default: 0,
  },

})

// 解构出需要的状态
let { currentImg } = storeToRefs(props.currentStore)

let isMoveableActive = ref(false)

// 背景盒子的尺寸
const backgroundBoxSize = ref({ width: 0, height: 0 })
// 抠图图片元素引用
const mattingImageRef = ref(null)
// Moveable组件引用 - 使用any类型避免TypeScript错误
const moveableRef = ref<any>(null)
// 缓存的matting-image DOM元素引用
const mattingImageElement = ref<HTMLElement | null>(null)

// Vue3-moveable 事件处理函数
const onDrag = (e: any) => {
  if (!currentImg.value) return

  isMoveableActive.value = true

  // 获取拖拽后的变换矩阵
  const transform = e.transform

  // 从transform中提取translate值
  const translateMatch = transform.match(/translate\(([^,]+),\s*([^)]+)\)/)
  if (translateMatch && translateMatch.length >= 3) {
    // 提取数值并去掉单位（如px）
    const translateX = parseFloat(translateMatch[1])
    const translateY = parseFloat(translateMatch[2])

    // 确保translate对象存在
    if (!currentImg.value.record.translate) {
      currentImg.value.record.translate = { x: 0, y: 0 }
    }

    // 更新translate值
    currentImg.value.record.translate.x = translateX
    currentImg.value.record.translate.y = translateY
  }

  // 应用变换到DOM元素
  if (mattingImageElement.value) {
    mattingImageElement.value.style.transform = transform
  }
}

const onScale = (e: any) => {
  if (!currentImg.value) return

  // 获取缩放值和变换矩阵
  const scale = e.scale
  const transform = e.transform

  // 更新缩放值
  currentImg.value.record.scaleX = scale[0]
  currentImg.value.record.scaleY = scale[1]

  console.log('Scale values:', {
    scaleX: currentImg.value.record.scaleX,
    scaleY: currentImg.value.record.scaleY,
  })

  // 应用变换到DOM元素
  if (mattingImageElement.value) {
    mattingImageElement.value.style.transform = transform
  }
}

const onRotate = (e: any) => {
  if (!currentImg.value) return

  // 获取旋转角度和变换矩阵
  const rotate = e.rotate
  const transform = e.transform

  // 更新旋转角度
  currentImg.value.record.rotate = rotate

  // 应用变换到DOM元素
  if (mattingImageElement.value) {
    mattingImageElement.value.style.transform = transform
  }
}

const onTransformEnd = () => {
  if (currentImg.value?.mattingImage) {
    if (mattingImageElement.value) {
      // 获取当前的transform样式
      const transformStyle = mattingImageElement.value.style.transform

      console.log('Transform end with style:', transformStyle)

      // 从transform中提取rotate值（如果尚未在onRotate中提取）
      const rotateMatch = transformStyle.match(/rotate\(([^)]+)\)/)
      if (rotateMatch && rotateMatch.length >= 2) {
        const rotateValue = rotateMatch[1]
        // 提取数值并去掉单位（如deg）
        const rotateNumber = parseFloat(rotateValue.replace('deg', ''))
        currentImg.value.record.rotate = rotateNumber
      }

      // 从transform中提取scale值（如果尚未在onScale中提取）
      const scaleMatch = transformStyle.match(/scale\(([^,)]+)(?:,\s*([^)]+))?\)/)
      if (scaleMatch && scaleMatch.length >= 2) {
        const scaleX = parseFloat(scaleMatch[1])
        const scaleY = scaleMatch[2] ? parseFloat(scaleMatch[2]) : scaleX
        currentImg.value.record.scaleX = scaleX
        currentImg.value.record.scaleY = scaleY
      }

      // 确保translate对象存在
      if (!currentImg.value.record.translate) {
        currentImg.value.record.translate = { x: 0, y: 0 }
      }

      // 从transform中提取translate值（如果尚未在onDrag中提取）
      const translateMatch = transformStyle.match(
        /translate\(([^,]+),\s*([^)]+)\)/
      )
      if (translateMatch && translateMatch.length >= 3) {
        // 提取数值并去掉单位（如px）
        const translateX = parseFloat(translateMatch[1].replace('px', ''))
        const translateY = parseFloat(translateMatch[2].replace('px', ''))
        currentImg.value.record.translate.x = translateX
        currentImg.value.record.translate.y = translateY
      }

      console.log('Transform end with values:', {
        translate: currentImg.value.record.translate,
        rotate: currentImg.value.record.rotate,
        scaleX: currentImg.value.record.scaleX,
        scaleY: currentImg.value.record.scaleY,
      })
    }

    // 生成抠图后的图片与背景的组合图，加上防抖
    proxy?.$filter?.tools.debounce(() => props.currentStore.createMattingCropImage(), 300)()
  }
}

// 手动触发Moveable组件更新
const updateMoveableRender = () => {
  if (moveableRef.value) {
    // 强制Moveable组件重新渲染
    moveableRef.value.updateRect()

    if (mattingImageElement.value) {
      moveableRef.value.updateTarget()
    }
  }
}

// 获取背景盒子的尺寸
const getBackgroundBoxSize = () => {
  const result = props.currentStore.getBackgroundBoxSize()
  if (result) {
    backgroundBoxSize.value = result
  }
}

// 处理文档点击事件，用于取消Moveable选择
const handleDocumentClick = (event: MouseEvent) => {
  const target = event.target as HTMLElement
  
  // 检查点击的元素是否是matting-image或其子元素
  if (!target.closest('.matting-image') && !target.closest('.moveable-control')) {
    isMoveableActive.value = false
  }
}

// 监听背景色、背景图片的变化
watch(
  () => [currentImg.value?.backgroundColor, currentImg.value?.backgroundImage],
  () => {
    if (currentImg.value?.mattingImage) {
      // 当背景变化时，重新计算尺寸
      const backgroundBoxSizeValue = props.currentStore.getBackgroundBoxSize()
      if (backgroundBoxSizeValue) {
        props.currentStore.updateMattingImageSize(backgroundBoxSizeValue)
          .then(() => {
            // 手动触发 Moveable 组件更新
            updateMoveableRender()
            // 尺寸更新完成后，创建组合图片
            props.currentStore.createMattingCropImage()
          })
          .catch((error) => {
            console.error('[BackgroundBox] Error updating matting image size:', error)
          })
      }
    }
  }
)

// 监听图片变换属性的变化
watch(
  () => [
    currentImg.value?.record?.opacity,
    currentImg.value?.record?.rotate,
    currentImg.value?.record?.zoom,
    currentImg.value?.record?.scaleX,
    currentImg.value?.record?.scaleY,
    currentImg.value?.record?.transformX,
    currentImg.value?.record?.transformY,
    currentImg.value?.record?.translate?.x,
    currentImg.value?.record?.translate?.y,
  ],
  () => {
    if (currentImg.value?.mattingImage) {
      // 手动触发 Moveable 组件更新
      updateMoveableRender()

      // 生成抠图后的图片与背景的组合图，加上防抖
      proxy?.$filter?.tools.debounce(() => props.currentStore.createMattingCropImage(), 300)()
    }
  }
)

// 监听抠图图片的变化
watch(
  () => currentImg.value?.mattingImage,
  () => {
    if (currentImg.value?.mattingImage) {
      const backgroundBoxSizeValue = props.currentStore.getBackgroundBoxSize()
      if (backgroundBoxSizeValue) {
        props.currentStore.updateMattingImageSize(backgroundBoxSizeValue)
      }
    }
  }
)

// 处理窗口大小变化
const handleResize = () => {
  getBackgroundBoxSize()
}

onMounted(() => {
  // 添加窗口大小变化监听
  window.addEventListener('resize', handleResize)
  
  // 添加文档点击事件监听，用于取消Moveable选择
  document.addEventListener('click', handleDocumentClick)

  // 缓存matting-image元素引用
  mattingImageElement.value = document.querySelector('.matting-image')

  // 初始获取背景盒子尺寸
  nextTick(() => getBackgroundBoxSize())
})

onUnmounted(() => {
  // 移除事件监听
  window.removeEventListener('resize', handleResize)
  document.removeEventListener('click', handleDocumentClick)
})

// 暴露方法给父组件使用
// defineExpose({
//   updateMoveableRender,
//   getBackgroundBoxSize,
// })
</script>

<style lang="scss" scoped>
// .box {
//   position: absolute;
//   margin: 0 47px;
//   width: calc(100% - 289px - 94px);
//   height: calc(100% - 202px);
//   box-shadow: 0px 0px 20px 0px rgba(0, 0, 0, 0.07);
// }
// 换背景
.background-box {
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1;

  .background-container {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    overflow: hidden;
    margin: auto; /* Center in parent */
    max-width: 100%; /* Ensure it doesn't exceed parent width */
    max-height: 100%; /* Ensure it doesn't exceed parent height */

    .background-image {
      position: relative;
      max-width: 100%;
      max-height: 100%;
      -webkit-user-drag: none;
      z-index: 0;
    }
    .matting-image {
      position: absolute;
      max-width: 100%;
      max-height: 100%;
      -webkit-user-drag: none;
      z-index: 1;
    }
  }

  .matting-progress {
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: #fff;
    border-radius: 50%;
    box-shadow: 0px 2px 9px 0px rgba(0, 0, 0, 0.31);
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 10;
  }
}
</style>
