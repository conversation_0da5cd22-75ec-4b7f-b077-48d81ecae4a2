.picture-btns {
  margin: 16px 0 22px;
  .add-pic,
  .add-folder,
  .remove {
    height: 32px;
    font-size: 14px;
    padding: 6px 10px;
    margin-right: 8px;
    border-radius: 4px;
    color: #fff;

    img {
      position: relative;
      width: 15px;
      height: 15px;
      right: 4px;
    }

    span {
      line-height: 20px;
    }
  }

  .add-pic {
    margin-right: 4px;
    border: 0;
    background: #389bfd;
  }

  .add-folder {
    border: 1px solid #389bfd;
    color: #389bfd;

    &:hover {
      color: #389bfd;
      border-color: #389bfd;
      background-color: #fff;
      opacity: 0.7;
    }
    &:focus {
      color: #389bfd;
      border-color: #389bfd;
      background-color: var(--el-button-bg-color);
      opacity: 1;
    }
  }

  .pdf-tip {
    font-weight: 400;
    font-size: 14px;
    color: #999999;
    line-height: 20px;
    text-align: left;
    font-style: normal;
    margin: 6px 0 0 10px;
  }

  .remove {
    border: 1px solid #e0e0e0;
    color: #333;
    &:hover {
      color: #333;
      border-color: #e0e0e0;
      background-color: #fff;
      opacity: 0.7;
    }
    &:focus {
      color: #333;
      border-color: #e0e0e0;
      background-color: var(--el-button-bg-color);
      opacity: 1;
    }
  }
}
