/**
 * 定时器小助手
 * @param param0 { timeout, canCallback, callback }
 */
const setIntervalHandler = ({ timeout, canCallback, callback }: any) => {
  let timer: any = null
  clearInterval(timer)

  timer = setInterval(() => {
    if (canCallback && canCallback()) {
      clearInterval(timer)
      callback && callback()
    }
  }, timeout)
}

const getQueryStringByName = function (name: string) {
  var result = location.search.match(
    new RegExp('[?&]' + name + '=([^&]+)', 'i')
  )
  if (result == null || result.length < 1) {
    return ''
  }
  return result[1]
}

const transformData = function (data: any) {
  const params = new FormData()
  for (const item in data) {
    params.append(item, data[item])
  }
  return params
}

// map to [{key1, key2, ...}, {key1, key2, ...}]
function mapToArray(map: any) {
  const array: Array<any> = []
  for (let [, value] of map) {
    array.push({
      ...(typeof value === 'object'
        ? JSON.parse(JSON.stringify(value))
        : value),
    })
  }
  return array
}

const DateFormat = function (date: any, fmt: string) {
  fmt = fmt || 'yyyy-MM-dd hh:mm:ss'
  if (date === null || typeof date === 'undefined' || date === '') {
    return null
  } else {
    // 时间要转成obj，否则报错
    date = new Date(date)
  }
  var o: any = {
    'M+': date.getMonth() + 1, // 月
    'd+': date.getDate(), // 日
    'h+': date.getHours(), // 时
    'm+': date.getMinutes(), // 分
    's+': date.getSeconds(), // 秒
    'q+': Math.floor((date.getMonth() + 3) / 3), // 季度
    S: date.getMilliseconds(), // 毫秒
  }
  if (/(y+)/.test(fmt))
    fmt = fmt.replace(
      RegExp.$1,
      (date.getFullYear() + '').substr(4 - RegExp.$1.length)
    )
  for (var k in o) {
    if (new RegExp('(' + k + ')').test(fmt)) {
      fmt = fmt.replace(
        RegExp.$1,
        RegExp.$1.length === 1 ? o[k] : ('00' + o[k]).substr(('' + o[k]).length)
      )
    }
  }
  return fmt
}

/**
 * 改变时间
 *   changeDate(new Date(), 7)
 * @param {Date} date
 * @param {Number} day 可以为负数
 * @param {Number} second 可以为负数
 */
const changeDate = ({ date = '', day = 0, second = 0 }: any) => {
  let temp: any = null

  if (!date) {
    return ''
  }
  if (!day && !second) {
    return date
  }
  if (typeof date === 'string') {
    temp = new Date(date.replace(/-/g, '/'))
  }
  if (typeof date === 'number') {
    temp = new Date(date)
  }
  temp = date

  date = new Date(temp.getTime() + day * 24 * 60 * 60 * 1000 + second * 1000)

  return date
}

/**
 * 深拷贝对象
 * 将 source 拷贝到 dest 并返回
 * @param {object} source
 * @param {object} dest
 * @returns new object
 */
const deepCopy = (
  source: { [x: string]: any; hasOwnProperty: (arg0: string) => any },
  dest?: {}
) => {
  const result = dest || {}
  for (const i in source) {
    if (Object.prototype.hasOwnProperty.call(source, i)) {
      if (typeof source[i] === 'object' && source[i] !== null) {
        result[i] = source[i].constructor === Array ? [] : {}
        deepCopy(source[i], result[i])
      } else {
        result[i] = source[i]
      }
    }
  }
  return result
}

export {
  setIntervalHandler,
  getQueryStringByName,
  transformData,
  mapToArray,
  DateFormat,
  deepCopy,
  changeDate,
}
