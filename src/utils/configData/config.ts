import bridge from '@/utils/bridge'

window.hostName = location.hostname
// window.hostName = 'ba.bestmind365.com' // test
const local = ['0.0.0.0', '127.0.0.1', 'localhost']
const isLocal = local.includes(location.hostname)
const defaultHostName = 'pic.miaoji66.com'
window.isLocal = isLocal
window.hostName = isLocal ? defaultHostName : window.hostName
const hostName = window.hostName
const titleConfig: Record<string, string> = {
    'pic.miaoji66.com': '妙吉图片转换器',
    'wnpic.miaoji66.com': window.navigator.userAgent.includes('lianxiang') ? '妙吉图片转换器' : '万能图片转换器', // 兼容联想渠道名称被占用问题
    // 'pic.mymind365.com': '全能图片转换器',
    // 'pic.bestmind365.com': '万能图片转换器',
    // 'www.mymind365.com': '全能图片转换器',
    // 'wn.bestmind365.com': '万能图片转换器',
    // 'a.bestmind365.com': 'A图片转换器',
    // 'bao.bestmind365.com': '图片转换器宝',
    // 'ba.bestmind365.com': '图片转换器吧',
}

export const projectConfig: any = {
    'pic.miaoji66.com': {
        APPNAME: titleConfig[hostName], // 妙吉图片转换器
        APPLOGO: '/img/pic/logo/mj/<EMAIL>',
        APPLOGOABOUT: '/img/pic/logo/mj/<EMAIL>',
        APPLOGOHOME: '/img/pic/logo/mj/<EMAIL>',
        APPLOGOTEMPLATE: '/img/pic/logo/mj/<EMAIL>',
        APPLOGOSIDE: '/img/pic/logo/mj/<EMAIL>',
        DOWNLOADURL: 'https://static.miaoji66.com/package/MjPicConvert_3.1.0.0_360_release.exe',
        APPID: '60000',
        CHANNEL: hostName,
    },
    'wnpic.miaoji66.com': {
        APPNAME: titleConfig[hostName], // 万能图片转换器
        APPLOGO: '/img/pic/logo/wn/<EMAIL>', // <EMAIL>
        APPLOGOABOUT: '/img/pic/logo/wn/<EMAIL>',
        APPLOGOHOME: '/img/pic/logo/wn/<EMAIL>',
        APPLOGOTEMPLATE: '/img/pic/logo/wn/<EMAIL>',
        APPLOGOSIDE: '/img/pic/logo/wn/<EMAIL>',
        DOWNLOADURL: 'https://static.miaoji66.com/package/MjPicConvert_3.1.0.0_360_release.exe',
        APPID: '60001',
        CHANNEL: hostName,
    },
    // 'pic.mymind365.com': {
    //     APPNAME: titleConfig[hostName], // 全能图片转换器
    //     APPLOGO: '/img/pic/logo/<EMAIL>',
    //     APPLOGOHOME: '/img/mind_map/icon/project/qnswdt/icon_logo_official_website.svg',
    //     APPLOGOTEMPLATE: '/img/mind_map/icon/project/qnswdt/icon_logo_template.svg',
    //     APPLOGOSIDE: '/img/pic/logo/<EMAIL>',
    //     DOWNLOADURL: 'https://static.miaoji66.com/package/qn_mind_small_7.0.3.0.exe',
    //     MacMDOWNLOADURL: 'https://static.miaoji66.com/package/qn_mind-*******-m2.dmg',
    //     MacIntelDOWNLOADURL: 'https://static.miaoji66.com/package/qn_mind-*******-x64.dmg',
    //     APPID: '60001',
    //     CHANNEL: hostName,
    // },
    // 'pic.bestmind365.com': {
    //     APPNAME: titleConfig[hostName], // 万能图片转换器
    //     APPLOGO: '/img/mind_map/icon/project/wnswdt/<EMAIL>',
    //     APPLOGOHOME: '/img/mind_map/icon/project/wnswdt/icon_logo_official_website.svg',
    //     APPLOGOTEMPLATE: '/img/mind_map/icon/project/wnswdt/icon_logo_template.svg',
    //     APPLOGOSIDE: '/img/mind_map/icon/project/wnswdt/icon_index_logo.svg',
    //     DOWNLOADURL: 'https://lestore.lenovo.com/detail/L104285',
    //     APPID: '60002',
    //     CHANNEL: hostName,
    // },
    // 'www.mymind365.com': {
    //     APPNAME: titleConfig[hostName], // 全能图片转换器
    //     APPLOGO: '/img/mind_map/icon/project/qnswdt/icon_about.png',
    //     APPLOGOHOME: '/img/mind_map/icon/project/qnswdt/icon_logo_official_website.svg',
    //     APPLOGOTEMPLATE: '/img/mind_map/icon/project/qnswdt/icon_logo_template.svg',
    //     APPLOGOSIDE: '/img/mind_map/icon/project/qnswdt/icon_index_logo.svg',
    //     DOWNLOADURL: 'https://static.miaoji66.com/package/qn_mind_small_7.0.3.0.exe',
    //     MacMDOWNLOADURL: 'https://static.miaoji66.com/package/qn_mind-*******-m2.dmg',
    //     MacIntelDOWNLOADURL: 'https://static.miaoji66.com/package/qn_mind-*******-x64.dmg',
    //     APPID: '60004',
    //     CHANNEL: hostName,
    // },
    // 'wn.bestmind365.com': {
    //     APPNAME: titleConfig[hostName], // 万能图片转换器
    //     APPLOGO: '/img/mind_map/icon/project/wnswdt/<EMAIL>',
    //     APPLOGOHOME: '/img/mind_map/icon/project/wnswdt/icon_logo_official_website.svg',
    //     APPLOGOTEMPLATE: '/img/mind_map/icon/project/wnswdt/icon_logo_template.svg',
    //     APPLOGOSIDE: '/img/mind_map/icon/project/wnswdt/icon_index_logo.svg',
    //     DOWNLOADURL: 'https://lestore.lenovo.com/detail/L104285',
    //     APPID: '60000',
    //     CHANNEL: hostName,
    // },
    // 'a.bestmind365.com': {
    //     APPNAME: titleConfig[hostName], // A图片转换器
    //     APPLOGO: '/img/mind_map/icon/project/aswdt/icon_about.png',
    //     APPLOGOHOME: '/img/mind_map/icon/project/aswdt/icon_logo_official_website.svg',
    //     APPLOGOTEMPLATE: '/img/mind_map/icon/project/aswdt/icon_logo_template.svg',
    //     APPLOGOSIDE: '/img/mind_map/icon/project/aswdt/icon_index_logo.svg',
    //     DOWNLOADURL: 'https://static.miaoji66.com/package/a_mind_guanwang_win_Installer_7.0.3.0.exe',
    //     APPID: '60001',
    //     CHANNEL: hostName,
    // },
    // 'bao.bestmind365.com': {
    //     APPNAME: titleConfig[hostName], // 图片转换器宝
    //     APPLOGO: '/img/mind_map/icon/project/swdtbao/icon_about.png',
    //     APPLOGOHOME: '/img/mind_map/icon/project/swdtbao/icon_logo_official_website.svg',
    //     APPLOGOTEMPLATE: '/img/mind_map/icon/project/swdtbao/icon_logo_template.svg',
    //     APPLOGOSIDE: '/img/mind_map/icon/project/swdtbao/icon_index_logo.svg',
    //     DOWNLOADURL: 'https://lestore.lenovo.com/detail/L104285',
    //     APPID: '60002',
    //     CHANNEL: hostName,
    // },
    // 'ba.bestmind365.com': {
    //     APPNAME: titleConfig[hostName],// 图片转换器吧
    //     APPLOGO: '/img/mind_map/icon/project/swdtba/icon_about.png',
    //     APPLOGOHOME: '/img/mind_map/icon/project/swdtba/icon_logo_official_website.svg',
    //     APPLOGOTEMPLATE: '/img/mind_map/icon/project/swdtba/icon_logo_template.svg',
    //     APPLOGOSIDE: '/img/mind_map/icon/project/swdtba/icon_index_logo.svg',
    //     DOWNLOADURL: 'https://lestore.lenovo.com/detail/L104285',
    //     APPID: '60003',
    //     CHANNEL: hostName,
    // },
}
// 设置配置默认值
let current = projectConfig[hostName] || projectConfig[defaultHostName]
current.APPID = (bridge.getAppId() || current.APPID) + ''
current.CHANNEL = `${bridge.getChannel() || 'default'}`
projectConfig[hostName] = current

// const topLevelDomain = location.hostname.split('.').slice(-2).join('.')
// export const CONVERTDOMAIN = 'http://apipic.mymind365.com'
export const CONVERTDOMAIN = isLocal ? 'http://localhost:3001' : '//apipic.mymind365.com'
export const APIDOMAIN = '//api2.mymind365.com'
// export const APIDOMAIN = (isLocal || hostName.includes('mymind365')) ? 'https://api2.mymind365.com' : `//api.${topLevelDomain}`
export const MAPDOMAIN = isLocal ? 'http://localhost:8080' : ''
export const STATICDOMAIN = '//static.mymind365.com'
export const DOMAIN = '//www.miaoji66.com'
export const APPNAME = titleConfig[hostName]
export const APPLOGO = projectConfig[hostName].APPLOGO
export const APPLOGOABOUT = projectConfig[hostName].APPLOGOABOUT
export const APPLOGOHOME = projectConfig[hostName].APPLOGOHOME
export const APPID = current.APPID
export const CHANNEL = projectConfig[hostName].CHANNEL
export const VERSION = bridge.getVersion()
export const OS = bridge.getOS()

// 修改title和icon
document.title = titleConfig[hostName] || '图片转换器'
const favicon: any = document.getElementById('favicon')
favicon && (favicon.href = APPLOGO)