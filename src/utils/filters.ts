interface IWeek {
  [key: string]: string;
}
interface IObject {
  [key: string]: any;
}

/**
 * 格式化数字
 * 示例：{{12345 | num}} 输出：12.3万  (1.5.0版本整站去除'w'单位)
 * @returns 12.3万
 */
function num(num: number) {
  if (num < 10000) {
    return num;
  }

  if (num >= 10000) {
    return (num / 10000).toFixed(2) + "万";
  }
}

/**
 * 格式化数字
 * 示例：{{1234 | num}} 输出：1.2k
 * @returns 1.2k
 */
function thousand(num: number) {
  if (num < 1000) {
    return num;
  }

  if (num >= 1000) {
    return (num / 1000).toFixed(1) + "k";
  }
}

/**
 * 格式化数字，超过3个加 逗号 分割
 * @returns 123,456
 */
function intervalNum(data: number | string) {
  if (data === undefined) {
    return;
  }
  if (typeof data === "number") {
    data = data.toFixed(2);
  }
  data = data.replace(".00", "");
  data =
    data.substring(data.length - 2) === ".0" ? data.replace(".0", "") : data;
  // 小于3位数，直接返回
  if (data < "1000") {
    return data;
  }
  if (!(data.split(".")[0].length > 3)) {
    return data;
  }
  let price = data.split(".")[0];
  const pricePoint = data.split(".")[1];
  let result = "";
  price = price.toString();
  result =
    price.length > 6
      ? `${price.substring(0, price.length - 6)},${price.substring(
          price.length - 6,
          price.length - 3
        )},${price.substring(price.length - 3, price.length)}`
      : `${price.substring(0, price.length - 3)},${price.substring(
          price.length - 3,
          price.length
        )}`;
  result = pricePoint ? `${result},${pricePoint}` : result;
  return result;
}

/**
 * 格式化秒，转化为02:25（时分）、01:02:48（时分秒）,格式
 * @returns 02:25、01:02:48
 */
function formatSeconds(value: number) {
  if (value > 0) {
    const hour = Math.floor(value / 3600);
    const min = Math.floor(value / 60) % 60;
    const sec = value % 60;
    // if (hour === 0) {
    //   result = ''
    // } else if (hour < 10) {
    //   result = '0' + hour + ':'
    // } else {
    //   result = hour + ':'
    // }
    // if (min < 10) {
    //   result += '0'
    // }
    // result += min + ':'
    // if (sec < 10) {
    //   result += '0'
    // }
    // result += sec
    return {
      hour,
      min,
      sec,
    };
  } else {
    return {
      hour: 0,
      min: 0,
      sec: 0,
    };
  }
}

/**
 * 格式化年份
 * @param {String} date Date 格式
 * @param {String} fmt 想要格式化的格式 'YYYY-MM-DD HH:mm:ss'、'YYYY-MM-DD'、'YYYY年MM月DD日 HH时mm分ss秒'、'YYYY年MM月DD日'
 * @return 仅返回年份
 */
function replacementYear(date: Date, fmt: string) {
  if (/(Y+)/.test(fmt)) {
    fmt = fmt.replace(
      RegExp.$1,
      (date.getFullYear() + "").substr(4 - RegExp.$1.length)
    );
  }
  return fmt;
}

/**
 * 格式化时间  详情内容里的时间格式
 * @param {Object} data 格式，可参考format 中的o属性
 * @param {String} fmt  想要格式化的格式 'YYYY-MM-DD HH:mm:ss'、'YYYY-MM-DD'、'YYYY年MM月DD日 HH时mm分ss秒'、'YYYY年MM月DD日'
 * @return 返回fmt 格式 时间
 */
function replacementDate(data: IObject, fmt: string) {
  for (const k in data) {
    if (new RegExp("(" + k + ")").test(fmt)) {
      fmt = fmt.replace(
        RegExp.$1,
        RegExp.$1.length === 1
          ? data[k]
          : `00${data[k]}`.substr(("" + data[k]).length)
      );
    }
  }
  return fmt;
}

/**
 * 格式化时间
 * @param {String|Number} date 需要格式化的时间 2017-11-11、2017/11/11、linux time
 * @param {String} fmt 想要格式化的格式 'YYYY-MM-DD HH:mm:ss'、'YYYY-MM-DD'、'YYYY年MM月DD日 HH时mm分ss秒'、'YYYY年MM月DD日'
 * @return {String} fmt 'YYYY-MM-DD HH:mm:ss'
 */
function format(date: any, fmt = "YYYY-MM-DD HH:mm:ss") {
  if (!date) return "";
  let timeData: any =
    typeof date === "string" ? new Date(date.replace(/-/g, "/")) : date;
  timeData = typeof date === "number" ? new Date(date) : timeData;
  const o: IObject = {
    "M+": timeData.getMonth() + 1,
    "D+": timeData.getDate(),
    "h+": timeData.getHours() % 12 === 0 ? 12 : timeData.getHours() % 12,
    "H+": timeData.getHours(),
    "m+": timeData.getMinutes(),
    "s+": timeData.getSeconds(),
    "q+": Math.floor((timeData.getMonth() + 3) / 3),
    S: timeData.getMilliseconds(),
  };

  const week: IWeek = {
    "0": "\u65e5",
    "1": "\u4e00",
    "2": "\u4e8c",
    "3": "\u4e09",
    "4": "\u56db",
    "5": "\u4e94",
    "6": "\u516d",
  };

  fmt = replacementYear(timeData, fmt);
  if (/(E+)/.test(fmt)) {
    fmt = fmt.replace(
      RegExp.$1,
      (RegExp.$1.length > 1
        ? RegExp.$1.length > 2
          ? "\u661f\u671f"
          : "\u5468"
        : "") + week[`${timeData.getDay()} `]
    );
  }
  return replacementDate(o, fmt);
}

const filter = {
  num, // 格式化数字 1.2k 12.3w
  thousand, // 格式化数字 1.2k
  intervalNum, // 格式化数字，超过3个加 逗号 分割
  formatSeconds, // 将数字转化为天，时，分，秒
  format,
};

export default filter;
