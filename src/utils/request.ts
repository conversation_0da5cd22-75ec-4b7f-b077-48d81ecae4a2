import axios from "axios";
// import qs from "qs";
import { ElMessage, ElLoading } from "element-plus";
import {
  DOMAIN,
  STATICDOMAIN,
  MAPDOMAIN,
  APIDOMAIN,
  APPID,
  CHANNEL,
  // VERSION,
  OS,
} from '@/utils/configData/config'
import bridge from '@/utils/bridge'

let loading: any = null;
axios.defaults.headers = {
  // "X-Requested-With": "XMLHttpRequest",
  // "Access-Control-Allow-Origin": "*",
  // "Access-Control-Allow-Method": "POST,GET",
};
const service = axios.create({
  baseURL: APIDOMAIN, // DOMAIN,
  timeout: 60000, // request timeout
  // transformRequest: [
  //   function (data) {
  //     // `transformRequest` 允许在向服务器发送前，修改请求数据
  //     // 只能用在 'PUT', 'POST' 和 'PATCH' 这几个请求方法
  //     // if (data) { data['platform'] = platformList2['OSS']; }
  //     return qs.stringify(data);
  //   },
  // ],
});

// let uid = JSON.parse(localStorage.user || '{}').id
// request interceptor
service.interceptors.request.use(
  (config: any) => {
    // console.log(config)
    config.headers = {
      ...config.headers,
      // "Content-type": "application/json",
    };
    // uid = uid || JSON.parse(localStorage.user || '{}').id || ''
    // config.headers['uid'] = config.params?.uid || config.data?.uid || uid
    // config.headers['version'] = config.params?.version || config.data?.version || VERSION || ''
    config.headers['app_id'] = config.params?.app_id || config.data?.app_id || APPID
    config.headers['channel'] = config.params?.channel || config.data?.channel || CHANNEL
    config.headers['device_id'] = config.params?.device_id || config.data?.device_id || localStorage.deviceId || '1234567890'
    config.headers['os'] = OS || ''
    if (config.params?.showLoading) {
      loading = ElLoading.service({
        lock: true,
        text: "正在请求数据....",
        background: "rgba(0, 0, 0, 0.5)",
      });
    }
    config.headers['Authorization'] = localStorage.token || ''

    if (config.method === "get") {
      config.params = config.params || {};
    }
    if (config.method === "post") {
      if (config.data?.showLoading) {
        loading = ElLoading.service({
          lock: true,
          text: "正在请求数据....",
          background: "rgba(0, 0, 0, 0.5)",
        });
      }
      config.data = config.data || {};
    }
    return config;
  },
  (error: { message: any }) => {
    // Do something with request error
    console.error(error); // for debug
    ElMessage.error({
      message: error.message,
      type: "error",
      // duration: 5 * 1000,
    });
    Promise.reject(error);
  }
);

service.interceptors.response.use(
  (response) => {
    if (loading) {
      loading.close();
    }
    const code = response.data.code;
    // response.status === 200
    const url = response.config.url || ''
    const passDomain = code === undefined && (url.includes(DOMAIN) || url.includes(STATICDOMAIN))
    if ([0, 1, 200, 8005].includes(code) || passDomain) { // 1 目前是登录token为空
      // 返回正常
      return response;
    } else if (code === 90001) {
      // 用户没有权限
      ElMessage.error({
        message: "您没有操作权限, 请刷新页面重试",
        type: "error",
        // duration: 5 * 1000,
      });
      return Promise.reject({
        // 保持结构和error的一致 可以在业务里面统一处理
        message: response.data.message,
        response,
      });
    } else {
      if ([1003].includes(code)) {
        // setTimeout(() => {
        //   location.reload()
        // }, 1000)
      } else {
        ElMessage.error({
          message: response.data.message || "接口响应信息异常",
          type: "error",
          // duration: 5 * 1000,
        })
      }

      if ([1000].includes(code)) { // token过期
        bridge.logout()
        localStorage.token = ''
        localStorage.user = ''
        sessionStorage.mindNums = ''
        setTimeout(() => {
          location.href = `${MAPDOMAIN}`
        }, 1000);
      }

      console.error(response.data.message || response.data)
      return Promise.reject({
        message: response.data.message || response.data,
        response,
      });
    }
  },
  (error) => {
    console.error(error.message);
    const message =
      error.response && error.response.data && error.response.data.message;
    ElMessage.error({
      message: message || "网络超时",
      type: "error",
      // duration: 5 * 1000,
    });
    return Promise.reject(error);
  }
);

export default service;
