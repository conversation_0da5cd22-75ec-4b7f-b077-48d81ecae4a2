export default class Semaphore {
  queue: any[]
  count: any
  constructor(count: any) {
    this.count = count
    this.queue = []
  }

  async acquire() {
    return new Promise<void>((resolve, reject) => {
      if (this.count > 0) {
        this.count--
        resolve()
      } else {
        this.queue.push(resolve)
      }
    })
  }

  release() {
    if (this.queue.length > 0) {
      this.queue.shift()()
    } else {
      this.count++
    }
  }
}