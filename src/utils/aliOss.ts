/**
 * 阿里云图片上传
 * 文档1：https://help.aliyun.com/document_detail/383950.htm?spm=a2c4g.11186623.0.0.48a25b59M6wQqE#concept-2161572
 * 文档2：https://help.aliyun.com/document_detail/64041.html?spm=a2c4g.11186623.2.15.4c8e5d26hmxgzg#concept-64041-zh
 */
// import request from '@/utils/request'
import { GetSTS, UploadImage, GetImage } from '@/api/doc'
import dayjs from 'dayjs'
import md5 from 'blueimp-md5'
import OSS from 'ali-oss'
import lrz from 'lrz'

let client: any = null
let uid = JSON.parse(localStorage.user || '{}').id || ''

const aliOss = {
    suffixEnum: {
        nowater: '!nowater', // 不打水印
        official: '!official', // 车库、经销商水印，默认
        panoram: '!panoram', // 全景图水印
        forum: '!forum',
        avatar: '!avatar', // 头像
        square: '!square',
        carport: '!carport' // 车库、经销商水印
    },
    // 加载图片
    imgUpload(file) {
        return new Promise((resolve, reject) => {
            let _URL = window.URL || window.webkitURL
            let image: any = new Image()
            image.src = _URL.createObjectURL(file)
            image.onload = function () {
                resolve(image)
                window.URL.revokeObjectURL(image.src)
            }
            image.error = function (res) {
                reject(res)
                window.URL.revokeObjectURL(image.src)
            }
        })
    },
    loadImage({ url, val, suffix, file, option, resolve, reject, isGif }) {
        let img: any = new Image()
        img.src = url
        // console.time()
        img.onload = function () {
            val.imgOrgUrl = `${url}?_${img.width}_${img.height}`
            // 全景图水印图"suffix === '!panoram'"缩略图后缀不带300，其他需要
            // gif 图片不需要300
            val.imgUrl = suffix === '!panoram' || isGif ? `${url}?_${img.width}_${img.height}` : `${url}300?_${img.width}_${img.height}`
            val.name = file.name
            val.fileName = file.name
            // console.log(val);
            option.onSuccess(val)
            resolve(val)
            // console.timeEnd()
            img = undefined
        }
        img.onerror = function () {
            option.onError('图片加载失败')
            reject('图片加载失败')
            console.log('图片加载失败')
            img = null
        }
    },
    /**
     * 创建oss客户端对象
     * @returns {*}
     */
    createOssClient({
        type = 'picture/image/temp',
        fileName = ''
    }) {
        return new Promise(async (resolve, reject) => {
            const date = dayjs(new Date()).format('YYYY/MM/DD')
            uid = uid || JSON.parse(localStorage.user || '{}').id || ''

            const fileExt = fileName.split('.').reverse()[0]
            const hashFileName = `${md5(fileName.replace(`.${fileExt}`, ''))}.${fileExt || ''}`
            const ossFileName = `${type}/${date}/${uid}/${Date.now()}${hashFileName}`

            // cache client
            if (client && (client.expire > new Date())) {
                // console.log('cache client')
                return resolve({
                    client,
                    fileName: fileName.includes('picture/') ? fileName : ossFileName,
                })
            }

            const instanceOSS = (data: any = {}) => {
                client = new OSS({
                    region: 'oss-cn-shanghai',
                    accessKeyId: data.credentials.AccessKeyId,
                    accessKeySecret: data.credentials.AccessKeySecret,
                    stsToken: data.credentials.SecurityToken,
                    bucket: data.bucket || 'mjmind'
                })
                client.expire = new Date(data.credentials.Expiration)
                const resolveRes = {
                    client,
                    fileName: fileName.includes('picture/') ? fileName : ossFileName,
                }
                // console.log('GetSTS', fileName, resolveRes)
                resolve(resolveRes)
            }

            // cache sts
            if (localStorage.sts) {
                // console.log('cache sts')
                try {
                    let data = JSON.parse(localStorage.sts) || ''
                    if (data.credentials && data.credentials !== 'null' && new Date(data.credentials.Expiration) > new Date()) {
                        return instanceOSS(data)
                    }
                } catch (err: any) {
                    console.error(err.message)
                }
            }

            GetSTS().then(sts => {
                if (sts.data.code === 0) {
                    localStorage.sts = JSON.stringify(sts.data.data || {})
                    instanceOSS(sts.data.data)
                } else {
                    reject(sts)
                    console.error(sts)
                }
            }).catch(e => {
                reject(e.message)
                console.error(e.message)
            })
        })
    },
    /**
     * 图片上传
     * 参考csdn: https://blog.csdn.net/qq_27626333/article/details/81463139
     * @param option
     *   option.file 文件
     *   option.fileId 文件ID
     *   option.quality 压缩质量
     *  demo:
            :http-request="httpRequest"
            async httpRequest(option) {
                this.$oss.ossUploadImage(option);
            }
        demo2:
            this.$oss.ossUploadImage({
                file: me.file,
                fileId: Number(me.$route.params.id) || uid
            }).then(_ => {
     */
    ossUploadImage(option) {
        // console.log(option)
        const self = this
        const file = option.file
        const fileId = Number(option.fileId || 0)
        return this.createOssClient({
            type: 'picture/image/temp',
            fileName: Date.now() + file?.name
        }).then((_: any) => {
            // console.log(_);
            if (file.type.includes('image')) {
                return new Promise((resolve, reject) => {
                    let quality = option.quality || 1 // 图片压缩比例，默认 1

                    self.imgUpload(file).then((image: any) => {
                        lrz(file, {
                            quality: quality
                        }).then(function (rst) {
                            // console.log(rst)

                            // 长图压缩有问题， isLong 判断是否是长图，是长图不使用压缩后的文件rst
                            const w = image.width
                            const h = image.height
                            const b = w > h ? w : h
                            const s = w < h ? w : h
                            const isLong = b / s > 2

                            // 超出大小
                            const imgSize = isLong ? file.size : rst.file.size
                            // console.log(imgSize / 1024);
                            if (imgSize / 1024 > 10 * 1024) {
                                return reject('图片大小不超过10M，请压缩后上传')
                            }

                            // 设置文件名
                            if (!rst.file.name) {
                                rst.file.name = file.name
                            }

                            // const isGif = file.type === 'image/gif'
                            // if (isGif) {
                            //     let fd = new FormData()
                            //     fd.append('file', file)
                            // }

                            // 分片上传文件
                            _.client.multipartUpload(_.fileName, isLong ? file : rst.file, {
                                progress: function (p) {
                                    const e: any = {}
                                    e.percent = Math.floor(p * 100)
                                    // option.onProgress(e)
                                },
                                // meta: { year, people: 'x-oss-meta-mjmind' },
                                mime: isLong ? file.type : rst.file.type
                            }).then(
                                val => {
                                    // console.info(val)
                                    if (val.res.statusCode === 200) {
                                        UploadImage({
                                            path: val.name,
                                            file_id: fileId
                                        }).then(_ => {
                                            // console.log(val, 'UploadImage', _)
                                            if (_.data.code === 0) {
                                                GetImage({
                                                    images: [val.name],
                                                    file_id: fileId
                                                }).then(_ => {
                                                    if (_.data.code === 0 && _.data.data) {
                                                        // console.log('GetImage', _)
                                                        resolve({
                                                            url: _.data.data[val.name],
                                                            name: val.name,
                                                            width: w,
                                                            height: h,
                                                            fileName: file.name,
                                                        })
                                                    } else {
                                                        reject('获取图片失败')
                                                        console.error('获取图片失败：', _)
                                                    }
                                                }).catch(err => {
                                                    reject('获取图片异常')
                                                    console.error('获取图片异常：', err.message)
                                                })
                                            } else {
                                                reject('图片上报失败')
                                                console.error('图片上报失败：', _)
                                            }
                                        }).catch(err => {
                                            reject('图片上报异常')
                                            console.error('图片上报异常：', err.message)
                                        })
                                    } else {
                                        reject('阿里云上传失败')
                                        console.error('阿里云上传失败：', val)
                                    }
                                },
                                err => {
                                    reject(err.message)
                                    console.error('阿里云上传异常：', err.message)
                                }
                            )
                        }).catch(function (e) {
                            reject(e.message)
                            console.error(e)
                        })
                    }).catch(res => {
                        reject(res)
                    })
                })
            }
            if (file.type.includes('pdf')) {
                return new Promise((resolve, reject) => {
                    // 超出大小
                    const imgSize = file.size
                    // console.log(imgSize / 1024);
                    if (imgSize / 1024 > 10 * 1024) {
                        return reject('PDF大小不超过10M，请压缩后上传')
                    }

                    _.client.multipartUpload(_.fileName, file, {
                        progress: function (p) {
                            const e: any = {}
                            e.percent = Math.floor(p * 100)
                            // option.onProgress(e)
                        },
                        // meta: { year, people: 'x-oss-meta-mjmind' },
                        mime: file.type
                    }).then(
                        val => {
                            // console.info(val)
                            if (val.res.statusCode === 200) {
                                UploadImage({
                                    path: val.name,
                                    file_id: fileId
                                }).then(_ => {
                                    // console.log(val, 'UploadImage', _)
                                    if (_.data.code === 0) {
                                        GetImage({
                                            images: [val.name],
                                            file_id: fileId
                                        }).then(_ => {
                                            if (_.data.code === 0 && _.data.data) {
                                                // console.log('GetImage', _)
                                                resolve({
                                                    url: _.data.data[val.name],
                                                    name: val.name,
                                                    // width: w,
                                                    // height: h,
                                                    fileName: file.name,
                                                })
                                            } else {
                                                reject('获取图片失败')
                                                console.error('获取图片失败：', _)
                                            }
                                        }).catch(err => {
                                            reject('获取图片异常')
                                            console.error('获取图片异常：', err.message)
                                        })
                                    } else {
                                        reject('图片上报失败')
                                        console.error('图片上报失败：', _)
                                    }
                                }).catch(err => {
                                    reject('图片上报异常')
                                    console.error('图片上报异常：', err.message)
                                })
                            } else {
                                reject('阿里云上传失败')
                                console.error('阿里云上传失败：', val)
                            }
                        },
                        err => {
                            reject(err.message)
                            console.error('阿里云上传异常：', err.message)
                        }
                    )
                })
            }

            return window.alert('请上传图片文件')
        })
    },
    /**
     * 文件上传
     * @param option
     * demo:
            :http-request="httpRequest"
            async httpRequest(option) {
                this.$oss.ossUploadFile(option);
            }
    */
    ossUploadFile(option) {
        const optFileName = option.fileName || ''

        return this.createOssClient({
            type: 'picture/file/temp',
            fileName: optFileName,
        }).then((_: any) => {
            const file: any = new Blob([option.file])
            const fileName = _.fileName
            const headers = {
                'Content-Encoding': 'UTF-8',
                'Content-Type': 'application/octet-stream',
            };
            return new Promise((resolve, reject) => {
                // 分片上传文件
                _.client.multipartUpload(fileName, file, {
                    progress: function (p) {
                        const e: any = {}
                        e.percent = Math.floor(p * 100)
                        // console.log(e)
                        // option.onProgress(e)
                    },
                    // meta: { year, people: 'x-oss-meta-mjmind' },
                    mime: option.mime || 'text/plain',
                    headers
                }).then(
                    val => {
                        if (val.res.statusCode === 200) {
                            const url = val.name
                            val.url = url
                            val.fileName = file.name || optFileName
                            resolve(val)
                        } else {
                            reject('上传失败')
                            console.error('上传失败')
                        }
                    },
                    err => {
                        reject(err)
                        console.error(err.message)
                    }
                ).catch(err => {
                    reject(err)
                    console.error(err.message)
                })
            })
        })
    },
}

export default {
    install(app) {
        app.config.globalProperties.$oss = aliOss
    },
    aliOss
}
