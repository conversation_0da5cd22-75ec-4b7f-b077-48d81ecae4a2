// 全局桥说明
// 命名规范：window.bridge***
// 是否联想客户端：window.isLenovo

// 历史方法先不管：
// window.Login window.loginCallback
// window.Logout window.logoutCallback
// window.fullScreen
// window.exitFullScreen

// 命名规范后的方法：
// window.bridgeSetTitle(title)
import qs from 'qs'
const query = qs.parse(location.search.replace('?', ''))

// sessionStorage.userAgent 不需要了
let userAgent = sessionStorage.userAgent || window.navigator.userAgent
window.ipcRendererApi?.invoke('getUserAgent').then((res: any) => {
  sessionStorage.userAgent = res
  // console.log('sessionStorage.userAgent', res)
})

const login = () => {
  window.isLenovo && window.ipcRendererApi?.send('start-login', { type: 'login' })
}

const logout = () => {
  window.isLenovo && window.ipcRendererApi?.send('start-login', { type: 'logout' })
}

// 使用系统浏览器打开
const openSystem = (url: string) => {
  if (window.isElectron) {
    window.ipcRendererApi?.send('openSystem', url)
  } else {
    window.open(url)
  }
}

/**
 * windows下更新app登录信息
 * @param message {userType, token}
 */
const updateAppIni = (message: any) => {
  if (window.isElectron) {
    window.ipcRendererApi?.send('updateAppIni', message)
  }
}

// eVersion=******* appId=40000 channel=lianxiang
const getVersion = () => {
  // 定义匹配版本号的正则表达式
  const versionRegex = /eVersion=(\d+\.\d+\.\d+\.\d+)/i
  // 使用正则表达式匹配版本号
  const match = userAgent.match(versionRegex)
  if (match && match[1]) {
    return match[1]
  } else {
    // console.log("Version not found in userAgent.")
    return ''
  }
}
const getAppId = () => {
  // 定义匹配版本号的正则表达式
  const versionRegex = /appId=(\d+)/i
  // 使用正则表达式匹配版本号
  const match = userAgent.match(versionRegex)
  if (match && match[1]) {
    return match[1]
  } else {
    // console.log("AppId not found in userAgent.")
    return ''
  }
}
const getChannel = () => {
  // 定义匹配版本号的正则表达式
  const versionRegex = /channel=([\w|\.]+)/i
  // 使用正则表达式匹配版本号
  const match = userAgent.match(versionRegex)
  if (match && match[1]) {
      return match[1]
  } else {
      const temp: any = query.channel || 'default'
      let channel = temp.split('?')[0]
      if (query.channel) {
          // channel = `${channel}_${getSystem()}${getPlatform()}`
          localStorage.channel = channel
      } else if (localStorage.channel) {
          channel = localStorage.channel
      }
      return channel
  }
}
const getOS = () => {
  return `${getSystem()}${getPlatform()}`
}
const getSystem = () => {
  let system = ''
  if (userAgent.indexOf('Windows') !== -1) {
    system = 'win'
  } else if (userAgent.indexOf('Mac OS') !== -1) {
    system = 'mac'
  }
  return system
}
const getPlatform = () => {
  const versions = getVersion()
  const isClient = !!versions

  let platform = ''
  if (!isClient) {
    platform = 'web'
  }
  return platform
}

/**
 * 比较版本号大小
 * demo：compareVersions('2.3.1.0')
 * @param {*} version1 目标版本号
 * @param {*} version2 系统版本号，不传时自动获取
 * @returns 1 大于，0 等于，-1 小于
 */
const compareVersions = (version1: string, version2 = getVersion()) => {
  const parts1 = version1.split('.').map(Number)
  const parts2 = version2.split('.').map(Number)

  for (let i = 0; i < 4; i++) {
    if (parts1[i] > parts2[i]) {
      return 1 // version1 > version2
    } else if (parts1[i] < parts2[i]) {
      return -1 // version1 < version2
    }
  }

  return 0 // 版本号相等
}

// function compareVersions(version1, version2) {
//   const v1Parts = version1.split('.');
//   const v2Parts = version2.split('.');
//   const maxLength = Math.max(v1Parts.length, v2Parts.length);
//   for (let i = 0; i < maxLength; i++) {
//     const num1 = parseInt(v1Parts[i] || 0, 10);
//     const num2 = parseInt(v2Parts[i] || 0, 10);
//     if (num1 > num2) {
//       return 1;
//     } else if (num1 < num2) {
//       return -1;
//     }
//   }
//   return 0;
// }

export default {
  login,
  logout,
  openSystem,
  updateAppIni,
  getVersion,
  getAppId,
  getChannel,
  getOS,
  getSystem,
  getPlatform,
  compareVersions,
}
