// 移 到utils
import { ElMessage } from 'element-plus'

export const chooseWebImages = async (
  choose: any = {},
  callback: Function
) => {
  const files: any = choose.length
    ? choose
    : choose.target && choose.target.files

  let res: any = []
  for await (const file of files) {
    const { type } = file
    if (!type) {
      console.log('暂不支持文件夹')
      ElMessage.error('暂不支持文件夹')
    } else if (type.includes('image/') || type.includes('pdf')) {
      const fileSizeInBytes = file.size || 0
      const fileSizeInKB = fileSizeInBytes / 1024
      const fileSizeInMB = fileSizeInKB / 1024
      res.push({
        key: file.name,
        file,
        size:
          fileSizeInMB >= 1
            ? `${fileSizeInMB.toFixed(1)}MB`
            : `${fileSizeInKB.toFixed(1)}KB`,
      })
    } else {
      console.log('存在不支持的文件')
      ElMessage.error('存在不支持的文件')
    }
  }
  // console.log(res)
  callback && callback(res)
}

export const dragHandler = (callback: Function) => {
  // 获取拖拽区域的 DOM 元素
  const dragArea: any = document.querySelector('.drag-area')
  if (!dragArea) {
    return
  }

  // 阻止默认行为
  ;['dragenter', 'dragover', 'dragleave', 'drop'].forEach((eventName) => {
    dragArea.removeEventListener(eventName, preventDefaults)
    dragArea.addEventListener(eventName, preventDefaults, {
      passive: false,
    })
  })
  // 处理拖放事件
  dragArea.removeEventListener('drop', handleDrop)
  dragArea.addEventListener('drop', handleDrop, { passive: false })

  function preventDefaults(e: any) {
    e.preventDefault() // 阻止默认行为
    e.stopPropagation()
  }
  function handleDrop(e: any) {
    // e.preventDefault(); // 这里也要阻止默认行为，防止打开新标签页
    // e.stopPropagation();
    const files: any = e.dataTransfer.files

    if (window.isElectron) {
      // 遍历获取到的文件
      for (const file of files) {
        // 根据类型判断是否支持，支持 '' 'image/*' '.pdf'
        const { type, path } = file
        if (!type) {
          console.log(e, type)
          window.ipcRendererApi
            ?.invoke('select-folder-by-path', [path])
            .then(callback)
        } else if (type.includes('image/') || type.includes('pdf')) {
          console.log(e, type)
          window.ipcRendererApi
            ?.invoke('select-pic-by-path', [path])
            .then(callback)
        } else {
          console.log('存在不支持的文件')
          ElMessage.warning('存在不支持的文件')
        }
      }
    } else {
      chooseWebImages(files, callback)
    }
  }

  // // 高亮拖拽区域
  // ;['dragenter', 'dragover'].forEach((eventName) => {
  //   dragArea.removeEventListener(eventName, highlight)
  //   dragArea.addEventListener(eventName, highlight, { passive: true })
  // })
  // ;['dragleave', 'drop'].forEach((eventName) => {
  //   dragArea.removeEventListener(eventName, unhighlight)
  //   dragArea.addEventListener(eventName, unhighlight, { passive: true })
  // })
  // function highlight() {
  //   dragArea.style.border = '2px solid #000'
  // }
  // function unhighlight() {
  //   dragArea.style.border = '2px dashed #ccc'
  // }
}

/** 获取拖拽的文件夹 */
export const onDragChange = (e: any, callback: Function) => {
  if (!window.isElectron) return
  // 根据类型判断是否支持，支持 '' 'image/*' '.pdf'
  const { type, path } = e.raw
  if (!type) {
    console.log(e, type)
    window.ipcRendererApi
      ?.invoke('select-folder-by-path', [path])
      .then(callback)
  } else if (type.includes('image/') || type.includes('pdf')) {
    console.log(e, type)
    window.ipcRendererApi
      ?.invoke('select-pic-by-path', [path])
      .then(callback)
  }
}
