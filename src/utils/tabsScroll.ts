import { ref, Ref } from 'vue'

/**
 * TabsScroll 类 - 支持多个实例的tabs级联滚动
 */
class TabsScroll {
  private id: string
  private enableComputedScroll: boolean = true
  private timer: number | null = null
  private tabs: HTMLElement | null = null
  private category: HTMLElement | null = null
  private threshold: number = 0
  public currentTypeId: Ref<number | string> = ref(0)
  private scrollHandler: ((this: HTMLElement, ev: Event) => any) | null = null

  /**
   * 创建一个新的TabsScroll实例
   * @param id 实例的唯一标识符
   */
  constructor(id: string) {
    this.id = id
    console.log(`TabsScroll instance created with id: ${id}`)

    // 创建绑定到当前实例的scrollHandler
    this.scrollHandler = this.handleScroll.bind(this)
  }

  /**
   * 处理标签点击事件
   * @param item 点击的标签项
   * @param event 点击事件
   */
  tabClick = (item: any, event?: Event) => {
    // 防止默认的锚点跳转行为
    if (event) {
      event.preventDefault()
    }

    this.enableComputedScroll = false
    if (this.timer) {
      clearTimeout(this.timer)
    }
    this.timer = window.setTimeout(
      () => (this.enableComputedScroll = true),
      1000
    )

    this.currentTypeId.value = item.id

    // 使用自定义滚动逻辑
    if (this.category) {
      // 找到目标元素
      const targetId = `${this.id}_${item.id}${item.name}`
      const targetElement = document.getElementById(targetId)

      if (targetElement) {
        // 平滑滚动到目标元素
        this.category.scrollTo({
          top: targetElement.offsetTop,
          behavior: 'smooth',
        })
      }
    }
  }

  /**
   * 处理滚动事件
   */
  private handleScroll = () => {
    if (!this.enableComputedScroll || !this.category) return

    const titles = this.category.querySelectorAll('.title')
    titles.forEach((title: Element) => {
      if (title.getBoundingClientRect().top < this.threshold) {
        this.currentTypeId.value = title.getAttribute('data-id') || 0
      }
    })
  }

  /**
   * 初始化TabsScroll
   * @param tabsSelector 标签选择器
   * @param categorySelector 分类容器选择器
   * @param gap 阈值间隙
   */
  init = (
    tabsSelector: string = '.category-box .tabs',
    categorySelector: string = '.category-box .category',
    gap: number = 100
  ) => {
    setTimeout(() => {
      console.log(`Initializing TabsScroll instance: ${this.id}`)
      this.currentTypeId.value = 0 // 重置索引

      this.tabs = document.querySelector(tabsSelector)
      this.category = document.querySelector(categorySelector)

      if (!this.tabs || !this.category) {
        console.warn(
          `TabsScroll: Could not find elements for selectors ${tabsSelector} or ${categorySelector}`
        )
        return
      }

      // 计算阈值
      const tabsHeight = this.tabs.offsetHeight || 0
      const paddingTop = 42
      const titleHeight = 20
      this.threshold = tabsHeight + paddingTop + titleHeight + gap

      // 添加滚动事件监听
      if (this.scrollHandler) {
        this.category.addEventListener('scroll', this.scrollHandler)
      }
    }, 0)
  }

  /**
   * 清理TabsScroll实例
   */
  cleanup = () => {
    console.log(`Cleaning up TabsScroll instance: ${this.id}`)

    // 移除滚动事件监听
    if (this.category && this.scrollHandler) {
      this.category.removeEventListener('scroll', this.scrollHandler)
    }

    // 清除定时器
    if (this.timer) {
      clearTimeout(this.timer)
      this.timer = null
    }
  }
}

// 创建一个实例管理器，用于向后兼容
const instances: Record<string, TabsScroll> = {}
let defaultInstance: TabsScroll | null = null
// 获取或创建实例
const getInstance = (id: string = 'default'): TabsScroll => {
  if (!instances[id]) {
    instances[id] = new TabsScroll(id)
    if (id === 'default') {
      defaultInstance = instances[id]
    }
  }
  return instances[id]
}

export { getInstance }
