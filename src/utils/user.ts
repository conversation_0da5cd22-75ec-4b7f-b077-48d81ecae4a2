import { usePayStore } from '@/pinia/pay'
import { useUserStore } from '@/pinia/user'
import { isVip, isVipExpire, needLogin } from '@/utils'
import { ElMessage } from 'element-plus'

/**
 * 是否去登录
 * @returns true 跳转登录，false 不去登录
 */
const goLogin = () => {
  const userStore = useUserStore()
  if (needLogin(userStore)) return true

  return false
}

/**
 * 是否有效期内的会员
 * @returns true 是会员，false 非会员
 */
const isValidVip = () => {
  const userStore = useUserStore()
  const vip =
    isVip(userStore.user) &&
    !isVipExpire(userStore.user, { $message: ElMessage })

  return vip
}

/**
 * 是否去支付
 * @returns true 跳转支付，false 未跳转支付
 */
const goPay = () => {
  const vip = isValidVip()
  if (vip) return false

  const payStore = usePayStore()
  payStore.setShowPayDialog(true)
  return true
}

export { goLogin, isValidVip, goPay }
