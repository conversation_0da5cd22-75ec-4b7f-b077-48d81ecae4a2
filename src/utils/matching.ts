/**
 * 此文件主要正则匹配使用
 */

/**
 * 正则匹配手机号码
 */
export function matchPhone(mobile: string): Boolean {
  return !/^1[1|3|4|5|6|7|8|9][0-9]{9}$/.test(mobile)
}
/**
 * 正则匹配身份证号码
 */

export function matchIDCard(idcard: string): Boolean {
  return !/(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/.test(idcard)
}

/**
 * 正则匹配电子邮箱
 */

export function matchEmail(idcard: string): Boolean {
  return !/^([a-zA-Z0-9]+[_|\_|\.]?)*[a-zA-Z0-9]+@([a-zA-Z0-9]+[_|\_|\.]?)*[a-zA-Z0-9]+\.[a-zA-Z]{2,3}$/.test(idcard)
}

/**
 * 正则匹配正整数
 */

export function matchPositiveInteger(value: string): <PERSON><PERSON><PERSON> {
  return !/^(0?|-?[1-9]\d*)$/.test(value)
}
