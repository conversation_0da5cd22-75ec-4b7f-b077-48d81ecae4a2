import { ElMessage } from 'element-plus'; // 引入message
// type MessageType = typeof ElMessage

let messageInstance: any = null;
const resetMessage: any = (options: any) => {
    if (messageInstance) {
        messageInstance.close() //关闭
    }

    messageInstance = ElMessage(options)
};
['error', 'success', 'info', 'warning'].forEach(type => {
    resetMessage[type] = (options: any) => {
        if (typeof options === 'string') {
            options = {
                message: options
            }
        }
        options.type = type
        return resetMessage(options)
    }
})
export const messageSimple = resetMessage