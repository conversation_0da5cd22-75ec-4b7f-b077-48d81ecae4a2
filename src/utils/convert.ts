/**
 * object 对象转 array 数组
 * demo:
 *  objectToArray({a:1,b:2})  输出：["a=1", "b=2"]
 * @param {any} obj
 * @returns
 */
export const objectToArray = (obj: { [key: string]: any }) => {
  var arr: any = []

  if (typeof obj === 'object') {
    for (var key in obj) {
      if (Object.prototype.hasOwnProperty.call(obj, key)) {
        arr.push([key, obj[key]].join('='))
      }
    }
  }

  return arr
}

/**
 * convertEnum
 * 枚举键值互换
 */
export const convertKeyValueEnum = (obj: any) => {
  const result = {}

  if (typeof obj === 'object') {
    for (const key in obj) {
      if (Object.prototype.hasOwnProperty.call(obj, key)) {
        result[obj[key]] = key
      }
    }
  }

  return result
}
