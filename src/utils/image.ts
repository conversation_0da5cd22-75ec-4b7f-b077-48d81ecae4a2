// ★ 导出图片说明 针对toDataURL
// image/jpeg 快，小，有损，透明丢失 (jpeg比jpg压缩效率高)
// image/webp 慢，小，无损
// image/png 慢，大，无损
// 涉及模块：裁剪、水印、旋转、PDF、改尺寸
// 大图优化：
// const sizeKB = key.file.size / 1024
// const isLargeImg = sizeKB > 1024 * 5
// const exportFormat = isLargeImg ? 'image/jpeg' : 'image/webp'

/**
 * 获取图片限制后的宽高
 * @param options - { limitSize 限定最大的宽或高-其中0为不限制, limitWidth 限定宽, limitheight 限定高 }
 */
const getLimitWH = (image: any, options: any) => {
  var rate = image.width / image.height
  var scale = 1
  var width = image.width
  var height = image.height

  if (options.limitSize) {
    width = rate > 1 ? options.limitSize : options.limitSize * rate
    height = rate < 1 ? options.limitSize : options.limitSize / rate
  } else {
    if (options.limitWidth && options.limitHeight) {
      width = options.limitWidth
      height = options.limitHeight
    } else if (options.limitWidth) {
      width = width > options.limitWidth ? options.limitWidth : width
      scale = width / image.width
      height = height * scale
    } else if (options.limitHeight) {
      height = height > options.limitHeight ? options.limitHeight : height
      scale = height / image.height
      width = width * scale
    }
  }

  return {
    width,
    height,
  }
}

/**
 * canvas图片预览
 */
const previewImageByCanvas = (canvas: any) => {
  canvas.toBlob((blob: any) => window.open(URL.createObjectURL(blob)))
}

/**
 * 下载canvas图片
 */
const downloadImageByCanvas = (
  canvas: any,
  options = {
    format: 'image/webp',
    name: `${Date.now()}.${'webp'}`,
    quality: 0.9,
    key: '',
  }
) => {
  // 下载时，会阻塞tab切换
  // const base64 = canvas.toDataURL(options.format, options.quality || 0.9)
  // const link = document.createElement('a')
  // link.href = base64
  // link.download = options.name || `${Date.now()}.${'webp'}`
  // link.click()

  // 下载时，不会阻塞tab切换
  canvas.toBlob((blob: Blob) => {
    const url = URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = options.name || `${Date.now()}.${'webp'}`
    link.click()
    console.timeEnd('watermark-download：' + options.key)
  }, options.format)
}

/**
 * 下载url图片 by A标签
 */
const downloadImageByA = (url: string, name?: string) => {
  const link = document.createElement('a')
  link.href = url
  link.download = name || `${Date.now()}.jpg`
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
}

/**
 * 下载url图片 by Xhr
 */
const downloadImageByXhr = (url: any, name?: string) => {
  // const a = document.createElement("a");
  // a.href = key.record.aliImgUrl;
  // a.download = key.newFile; // 跨域不支持自定义命名
  // a.click()

  const xhr = new XMLHttpRequest()
  xhr.open('GET', url)
  xhr.responseType = 'blob'
  xhr.onload = function () {
    const blob = xhr.response
    const url = window.URL.createObjectURL(blob)

    const a = document.createElement('a')
    a.href = url
    a.download = name || `${Date.now()}.jpg` // `${key.newFileName}.${key.record.newFormat}`
    a.click()

    window.URL.revokeObjectURL(url) // 释放 URL 对象
  }
  xhr.send()
}

/**
 * 加载图片
 */
const loadImage = (src: string) => {
  return new Promise((resolve, reject) => {
    const img = new Image()
    img.crossOrigin = 'anonymous'
    img.src = src
    img.onload = () => {
      resolve(img)
    }
    img.onerror = reject = (e) => {
      reject(e)
    }
  })
}
/**
 * 获取图片大小
 */
const getImageSize = (src: string) => {
  return new Promise((resolve, reject) => {
    var image = new Image()
    image.onload = function () {
      var rate = image.width / image.height
      resolve({
        width: image.width,
        height: image.height,
        rate,
        image,
      })
    }
    image.onerror = function (e) {
      reject(e)
      console.log(e)
    }
    image.src = src
  })
}

/**
 * 获取图片信息 {thumbnail, width, height}
 */
const getImageInfo = async (content: File | Blob) => {
  const getInfo = (img: any) => {
    const imgUrl = URL.createObjectURL(img)
    return new Promise((resolve, reject) => {
      var image = new Image()
      image.crossOrigin = 'Anonymous'
      image.onload = function () {
        URL.revokeObjectURL(imgUrl)
        var canvas = document.createElement('canvas')
        var ctx: any = canvas.getContext('2d')
        var rate = image.width / image.height
        canvas.width = 200
        canvas.height = canvas.width / rate
        ctx.drawImage(image, 0, 0, canvas.width, canvas.height)

        resolve({
          thumbnail: canvas.toDataURL('image/jpeg', 0.5), // 缩略图，直接jpeg
          width: image.width,
          height: image.height,
        })
      }
      image.onerror = function (e) {
        // console.error(e)
        // reject({})
        resolve({}) // 图片信息获取失败，也可以进行转换
      }
      image.src = imgUrl
    })
  }

  const res = await getInfo(content)
  return res

  // if (!content.type.includes('heic')) {
  //   return await getInfo(content)
  // }
  // const imgUrl = URL.createObjectURL(content)
  // return fetch(imgUrl)
  //   .then((res) => res.blob())
  //   .then((blob) =>
  //     heic2any({
  //       blob,
  //       toType: 'image/webp',
  //     })
  //   )
  //   .then(async (conversionResult: any) => {
  //     URL.revokeObjectURL(imgUrl)
  //     return await getInfo(conversionResult)
  //   })
  //   .catch((e) => {
  //     console.error(e)
  //     return Promise.resolve({})
  //   })
}

/**
 * file转base64，jpeg速度快，其他速度慢
 * 需要使用 new Image()，drawImage入参：<img>、<canvas>、<video>、<ImageBitmap> 或 <ImageData>
 * @param file file or blob
 * @param callback
 * @param foramt 示例：image/webp
 */
const fileToBase64 = (file: any, callback: any, foramt?: string) => {
  const imgUrl = URL.createObjectURL(file)
  var image = new Image()
  image.crossOrigin = 'Anonymous'
  image.onload = function () {
    URL.revokeObjectURL(imgUrl)
    var canvas = document.createElement('canvas')
    var ctx: any = canvas.getContext('2d')
    canvas.width = image.width
    canvas.height = image.height
    ctx.drawImage(image, 0, 0, canvas.width, canvas.height)
    var base64 = canvas.toDataURL(foramt || 'image/webp')
    callback(base64)
  }
  image.onerror = function (e) {
    console.log(e)
  }
  image.src = imgUrl
}
/**
 * file转base64，速度快
 * @param file file or blob
 * @param callback
 */
const fileToBase64ByReader = (file: any, callback: any) => {
  var reader = new FileReader()
  reader.onload = function () {
    callback(reader.result)
  }
  reader.readAsDataURL(file)
}
/**
 * fileToCanvas
 * @param file file or blob
 * @param callback(res = { canvas, image })
 * @param options - { limitSize 限定最大的宽或高-其中0为不限制, limitWidth 限定宽, limitheight 限定高 }
 */
const fileToCanvas = (file: any, callback: Function, options: any = {}) => {
  const imgUrl = URL.createObjectURL(file)
  var image = new Image()
  image.crossOrigin = 'Anonymous'
  image.onload = function () {
    URL.revokeObjectURL(imgUrl)
    var canvas = document.createElement('canvas')
    var ctx: any = canvas.getContext('2d')
    var { width, height } = getLimitWH(image, options)
    canvas.width = width
    canvas.height = height

    ctx.drawImage(image, 0, 0, canvas.width, canvas.height)
    callback({ canvas, image })
  }
  image.onerror = function (e: any) {
    console.log(e)
    callback({ error: e.message, image })
  }
  image.src = imgUrl
}
/**
 * 压缩file，并返回：returnFormat（base64或file，默认base64）、rate、width、height
 * @param options { file: file or blob, limitSize, foramt, returnFormat, quality }
 * getLimitSize 如果方法存在，通过 getLimitSize 更改 limitSize
 * returnFormat（base64或file，默认base64）
 * @param callback
 */
const compressFile = (options: any, callback: any) => {
  const { file, getLimitSize } = options
  let { limitSize = 500 } = options
  const imgUrl = URL.createObjectURL(file)
  var image = new Image()
  image.crossOrigin = 'Anonymous'
  image.onload = function () {
    URL.revokeObjectURL(imgUrl)
    var canvas = document.createElement('canvas')
    var ctx: any = canvas.getContext('2d')

    if (typeof getLimitSize === 'function') {
      limitSize = getLimitSize(image)
    }

    // 文件像素不能低于：32*32
    if (options.returnFormat === 'file' && limitSize < 32) limitSize = 32

    var rate = image.width / image.height
    var width = rate > 1 ? limitSize : limitSize * rate
    var height = rate < 1 ? limitSize : limitSize / rate

    canvas.width = width
    canvas.height = height
    ctx.drawImage(image, 0, 0, width, height)

    if (options.returnFormat === 'file') {
      return canvas.toBlob(
        (blob: any) => {
          const newFile = new File([blob], file.name, { type: file.type })

          callback({
            file: newFile,
            rate,
            width,
            height,
          })
        },
        options.foramt || 'image/webp',
        options.quality || 0.9
      )
    } else {
      var base64 = canvas.toDataURL(
        options.foramt || 'image/webp',
        options.quality || 0.9
      )
      // console.log(base64, options)

      callback({
        base64,
        rate,
        width,
        height,
      })
    }
  }
  image.onerror = function (e) {
    console.log(e)
  }
  image.src = imgUrl
}

/**
 * 触发网页端的文件上传操作
 * @param e 事件对象，通常由用户点击等操作触发
 * @param id 文件输入DOM元素的id，默认值为'imgUploadInput'
 */
const webUpload = (e: any, id: string = 'imgUploadInput') => {
  const choose = document.getElementById(id)
  if (choose) {
    // 解决文件不能重复上传问题
    choose.setAttribute('type', 'text')
    choose.setAttribute('type', 'file')
    choose.click()
  }
}
/**
 * 触发网页端的文件夹上传操作
 * @param e 事件对象，通常由用户点击等操作触发
 * @param id 文件输入DOM元素的id，默认值为'imgUploadInputFolder'
 */
const webUploadFolder = (e: any, id: string = 'imgUploadInputFolder') => {
  const choose = document.getElementById(id)
  if (choose) {
    // 解决文件不能重复上传问题
    choose.setAttribute('type', 'text')
    choose.setAttribute('type', 'file')
    choose.click()
  }
}

function isBlob(obj: any) {
  return obj instanceof Blob
}

/**
 * 图片blobToFile
 * @param content buffer 或 blob
 * @param options { format: 'png', name: 'image.png' }
 */
const blobToFile = (content: any, options: any = {}) => {
  const blob: any = isBlob(content)
    ? content
    : new Blob([content], {
        type: `image/${options.format || 'webp'}`,
      })

  let file = new File([blob], options.name || `${Date.now()}.webp`, {
    type: blob.type,
    lastModified: blob.lastModified || Date.now(), // 或者使用 blob.lastModified 如果可用
  })
  return file
}

/**
 * 获取图片变换后的base64
 * 图片转pdf、批量旋转时使用（包含了图片旋转、反转这种）
 */
const getTransformImage = ({
  rotate = 0,
  scaleX = 1,
  scaleY = 1,
  opacity = 100,
  quality = 0.9,
  limitWidth,
  format = 'webp',
  currentImage = new Image(),
}: {
  rotate?: number
  scaleX?: number
  scaleY?: number
  opacity?: number
  quality?: number
  limitWidth?: number
  format?: string
  currentImage?: any
}) => {
  const canvas = document.createElement('canvas')
  const ctx: any = canvas.getContext('2d')

  // pdf限宽4k
  const options = limitWidth ? { limitWidth } : {}
  const { width, height } = getLimitWH(currentImage, options)

  const originalWidth = width
  const originalHeight = height

  // 旋转角度（以弧度为单位）
  const angle = rotate * (Math.PI / 180)

  // 计算旋转后的包围盒尺寸
  const sin = Math.abs(Math.sin(angle))
  const cos = Math.abs(Math.cos(angle))
  const rotatedWidth = originalWidth * cos + originalHeight * sin
  const rotatedHeight = originalWidth * sin + originalHeight * cos

  // 清空画布，避免图像重叠
  ctx.clearRect(0, 0, canvas.width, canvas.height)

  // 保存画布状态
  ctx.save()

  // 使canvas能够包含旋转后的图像，并保持比例
  canvas.width = rotatedWidth
  canvas.height = rotatedHeight

  // 设置背景色（应用于旋转后的背景色）
  ctx.fillStyle = '#FFFFFF' // 例如，设置为白色
  ctx.fillRect(0, 0, rotatedWidth, rotatedHeight) // 填充整个画布

  // 设置透明度
  ctx.globalAlpha = opacity / 100

  // 将 canvas 的原点移到中心位置以实现基于中心的旋转
  ctx.translate(rotatedWidth / 2, rotatedHeight / 2)

  // 旋转画布
  ctx.rotate(angle)

  // 根据 scaleX 和 scaleY 进行翻转，必须在旋转后进行
  ctx.scale(scaleX, scaleY)

  // 还原到图像左上角的绘制位置，并保持图像原始比例
  ctx.translate(-originalWidth / 2, -originalHeight / 2)
  ctx.drawImage(currentImage, 0, 0, originalWidth, originalHeight)

  // 恢复画布状态
  ctx.restore()

  // 将 canvas 转化为 base64 格式的图像
  const base64 = canvas.toDataURL(`image/${format}`, quality)
  return base64
}
/**
 * 获取裁剪后的图片
 */
const getCropImage = ({
  rotate = 0,
  scaleX = 1,
  scaleY = 1,
  opacity = 100,
  quality = 0.9,
  limitWidth,
  format = 'webp',
  currentImage = new Image(),
}: {
  rotate?: number
  scaleX?: number
  scaleY?: number
  opacity?: number
  quality?: number
  limitWidth?: number
  format?: string
  currentImage?: any
}) => {
  const canvas = document.createElement('canvas')
  const ctx: any = canvas.getContext('2d')

  // pdf限宽4k
  const options = limitWidth ? { limitWidth } : {}
  const { width, height } = getLimitWH(currentImage, options)

  const originalWidth = width
  const originalHeight = height

  // 旋转角度（以弧度为单位）
  const angle = rotate * (Math.PI / 180)

  // 计算旋转后的包围盒尺寸
  const sin = Math.abs(Math.sin(angle))
  const cos = Math.abs(Math.cos(angle))
  const rotatedWidth = originalWidth * cos + originalHeight * sin
  const rotatedHeight = originalWidth * sin + originalHeight * cos

  // 清空画布，避免图像重叠
  ctx.clearRect(0, 0, canvas.width, canvas.height)

  // 保存画布状态
  ctx.save()

  // 使canvas能够包含旋转后的图像，并保持比例
  canvas.width = rotatedWidth
  canvas.height = rotatedHeight

  // 设置背景色（应用于旋转后的背景色）
  ctx.fillStyle = '#FFFFFF' // 例如，设置为白色
  ctx.fillRect(0, 0, rotatedWidth, rotatedHeight) // 填充整个画布

  // 设置透明度
  ctx.globalAlpha = opacity / 100

  // 将 canvas 的原点移到中心位置以实现基于中心的旋转
  ctx.translate(rotatedWidth / 2, rotatedHeight / 2)

  // 旋转画布
  ctx.rotate(angle)

  // 根据 scaleX 和 scaleY 进行翻转，必须在旋转后进行
  ctx.scale(scaleX, scaleY)

  // 还原到图像左上角的绘制位置，并保持图像原始比例
  ctx.translate(-originalWidth / 2, -originalHeight / 2)
  ctx.drawImage(currentImage, 0, 0, originalWidth, originalHeight)

  // 恢复画布状态
  ctx.restore()

  // 将 canvas 转化为 base64 格式的图像
  const base64 = canvas.toDataURL(`image/${format}`, quality)
  return base64
}

export {
  getLimitWH,
  downloadImageByCanvas,
  previewImageByCanvas,
  downloadImageByA,
  downloadImageByXhr,
  loadImage,
  getImageSize,
  getImageInfo,
  fileToBase64ByReader,
  fileToBase64,
  fileToCanvas,
  compressFile,
  webUpload,
  webUploadFolder,
  blobToFile,
  getTransformImage,
  getCropImage,
}
