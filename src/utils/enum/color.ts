// 颜色
export const colorBaseList = [
  '#FFFFFF', // #D9D9D9
  '#000000',
  '#FF0000',
  '#FFFF02',
  '#00FF02',
  '#01FFFF',
  '#0000FF',
  '#FF00FF',
]
export const colorList = [
  '#4D4D4D',
  '#999999',
  '#FFFFFF',
  '#F44E3B',
  '#FE9200',
  '#FCDC00',
  '#DBDF00',
  '#A4DD00',
  '#68CCCA',
  '#73D8FF',
  '#AEA1FF',
  '#FDA1FF',
  '#333333',
  '#808080',
  '#cccccc',
  '#D33115',
  '#E27300',
  '#FCC400',
  '#B0BC00',
  '#68BC00',
  '#16A5A5',
  '#009CE0',
  '#7B64FF',
  '#FA28FF',
  '#000000',
  '#666666',
  '#B3B3B3',
  '#9F0500',
  '#C45100',
  '#FB9E00',
  '#808900',
  '#194D33',
  '#0C797D',
  '#0062B1',
  '#653294',
  '#AB149E',
  'transparent',
]
export const colorModule: any = {
  常用: [
    '#EEECEB',
    '#F2F2F2',
    '#E7EBED',
    '#FADCDB',
    '#FBEADA',
    '#FBF9EA',
    '#E5F6DA',
    '#DBF5F5',
    '#D2D6F9',
    '#FADDED',

    '#DED9D7',
    '#D9D9D9',
    '#E0E0E0',
    '#F5B9B7',
    '#F8D5B5',
    '#F1E4A2',
    '#CAEDB4',
    '#B6EAEB',
    '#A5AEF3',
    '#F6BBDB',

    '#BEB3AF',
    '#BFBFBF',
    '#9E9E9E',
    '#F19594',
    '#F4C18F',
    '#F1E4A3',
    '#AFE38F',
    '#94E0E1',
    '#7985EC',
    '#F199C8',

    '#9D8C88',
    '#A6A6A6',
    '#616161',
    '#ED7270',
    '#F1AC69',
    '#E9D66F',
    '#95DA69',
    '#6FD5D7',
    '#5B79E8',
    '#ED77B7',

    '#5C4038',
    '#7F7F7F',
    '#262626',
    '#A23635',
    '#A56A30',
    '#A7932C',
    '#569131',
    '#368E90',
    '#314AA4',
    '#A23C73',

    '#FFFFFF',
    '#7F8B98',
    '#000000',
    '#E74F4C',
    '#ED9745',
    '#E0C431',
    '#7AD144',
    '#4CCBCD',
    '#4769EA',
    '#E855A4',
  ],
  潘通色: [
    '#E2F0E1',
    '#EEE7F1',
    '#E3D5D8',
    '#FDF8DC',
    '#DFE3F5',
    '#FFF0D5',
    '#DFE5DB',
    '#E1EFF5',
    '#DDE6E6',
    '#F9DDD6',

    '#C7E1C3',
    '#DFCFE3',
    '#CAAAB1',
    '#FBF1B8',
    '#BEC7EA',
    '#FEE2AC',
    '#BFCAB6',
    '#C6DFEA',
    '#BDCECD',
    '#F3BBAE',

    '#ACD1A6',
    '#CCB8D6',
    '#AF808A',
    '#F8EB95',
    '#9EAAE0',
    '#FED480',
    '#A0B092',
    '#A9CEE0',
    '#9EB5B5',
    '#ED9984',

    '#8FC287',
    '#BDA1C9',
    '#955563',
    '#F6E471',
    '#7E8DD5',
    '#FDC556',
    '#80956D',
    '#8DBED4',
    '#7D9D9C',
    '#E6775B',

    '#5B8F55',
    '#8A6E96',
    '#612231',
    '#C3B13D',
    '#4B5CA2',
    '#CB9224',
    '#4D623A',
    '#5A8BA2',
    '#496A69',
    '#B44327',

    '#FFFFFF',
    '#7F8B98',
    '#000000',
    '#E74F4C',
    '#ED9745',
    '#E0C431',
    '#7AD144',
    '#4CCBCD',
    '#4769EA',
    '#E855A4',
  ],
  莫兰迪: [
    '#F1F1E8',
    '#ECECED',
    '#F3FAF9',
    '#F4F0EA',
    '#F9FAEE',
    '#F8F9F5',
    '#F5E9ED',
    '#FBE7EB',
    '#F9EEE1',
    '#FEF5EF',

    '#E3E2D1',
    '#D9DADB',
    '#E7F5F4',
    '#EAE1D5',
    '#F4F6DD',
    '#F1F3EC',
    '#EBD2DC',
    '#F9CFD7',
    '#F3DDC3',
    '#FDEBDE',

    '#D6D4BA',
    '#C5C7CA',
    '#DDEFEE',
    '#DDD1C1',
    '#EFF1CB',
    '#EAECE2',
    '#E0BCCA',
    '#F6B6C4',
    '#EECCA5',
    '#FCE2CF',

    '#C7C5A2',
    '#B2B5B8',
    '#D1EAE9',
    '#D2C2AB',
    '#E8EDBA',
    '#E1E6D9',
    '#D6A5B9',
    '#F39EB0',
    '#E7BB87',
    '#FBD8BF',

    '#949270',
    '#808285',
    '#9EB7B6',
    '#9F8F7A',
    '#B5BA86',
    '#AEB3A6',
    '#A37286',
    '#C06B7D',
    '#B48854',
    '#C8A58C',

    '#FFFFFF',
    '#7F8B98',
    '#000000',
    '#E74F4C',
    '#ED9745',
    '#E0C431',
    '#7AD144',
    '#4CCBCD',
    '#4769EA',
    '#E855A4',
  ],
  更多: [],
}
