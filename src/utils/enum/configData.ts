export const forwardPickerOptions = {
  shortcuts: [
    {
      text: '最近一周',
      value: () => {
        const end = new Date()
        const start = new Date()
        start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
        return [start, end]
      },
    },
    {
      text: '最近一个月',
      value: () => {
        const end = new Date()
        const start = new Date()
        start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
        return [start, end]
      },
    },
    {
      text: '最近二个月',
      value: () => {
        const end = new Date()
        const start = new Date()
        start.setTime(start.getTime() - 3600 * 1000 * 24 * 60)
        return [start, end]
      },
    },
    {
      text: '最近三个月',
      value: () => {
        const end = new Date()
        const start = new Date()
        start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
        return [start, end]
      },
    },
    {
      text: '最近一年',
      value: () => {
        const end = new Date()
        const start = new Date()
        start.setTime(start.getTime() - 3600 * 1000 * 24 * 365)
        return [start, end]
      },
    },
  ],
}

export const flowTypes = ['flowchart', 'swimlanes', 'uml', 'network', 'bpmn']

export const imgExts = [
  'jpg',
  'jpeg',
  'png',
  'bmp',
  'tif',
  'tiff',
  'webp',
  'pdf',
  'gif',
  'psd',
  'svg',
  'art',
  'aai',
  'avif',
  'avs',
  'bmp2',
  'bmp3',
  'cgm',
  'cin',
  'dcx',
  'dds',
  'dib',
  'dpx',
  'epdf',
  'epi',
  'eps',
  'eps2',
  'sps3',
  'epsf',
  'epsi',
  'ept',
  'exr',
  'fax',
  'fig',
  'fits',
  'fl32',
  'flif',
  'fpx',
  'hdr',
  'heic',
  'heif',
  'hif',
  'hpgl',
  'hrz',
  'ico',
  'j2c',
  'j2k',
  'jbig',
  'jfif',
  'jng',
  'jp2',
  'jxl',
  'kernel',
  'livp',
  'miff',
  'mng',
  'mpr',
  'mrsid',
  'msl',
  'mtv',
  'mvg',
  'otb',
  'p7',
  'palm',
  'pam',
  'pbm',
  'pcd',
  'pcds',
  'pcx',
  'pdb',
  'pes',
  'pfm',
  'pgm',
  'phm',
  'pict',
  'png00',
  'png24',
  'png32',
  'png48',
  'png64',
  'png8',
  'pnm',
  'ppm',
  'ps',
  'ps2',
  'ps3',
  'psb',
  'ptif',
  'rgb565',
  'sfw',
  'sgi',
  'sun',
  'tga',
  'vicar',
  'viff',
  'wbmp',
  'xbm',
  'xpm',
  'xwd',
  'clipboard',
  'farbfeld',
  'pocketmod',
] // 102种

export const imgToPdfExts = [
  'jpg',
  'jpeg',
  'png',
  'bmp',
  'tif',
  'tiff',
  'webp',
  'gif',
  'psd',
  'svg',
  'heic',
  'heif',
  'ico',
  'j2c',
  'jp2',
  'jxl',
  'avif',
  'bmp2',
  'bmp3',
  'dpx',
  'exr',
  'hdr',
  'jng',
  'jfif',
  'fits',
  'mng',
  'mtv',
  'otb',
  'pam',
  'palm',
  'pbm',
  'pcd',
  'pcx',
  'pdb',
  'pgm',
  'pict',
  'ppm',
  'rgb565',
  'sfw',
  'sgi',
  'sun',
  'tga',
  'viff',
  'wbmp',
  'xbm',
  'xpm',
  'xwd',
] // 47种

export const imgCompressExts = ['jpg', 'jpeg', 'png', 'gif'] // 4种
export const imgWatermarkExts = ['jpg', 'jpeg', 'png', 'bmp', 'webp', 'gif']

/** 获取web端支持的格式，示例：getWebExtsByName('imgWatermarkExts') */
export const getWebExtsByName = (name: string) => {
  let result: any = []
  switch (name) {
    case 'imgExts':
      result = imgExts
    case 'imgToPdfExts':
      result = imgToPdfExts
    case 'imgCompressExts':
      result = imgCompressExts
    case 'imgWatermarkExts':
      result = imgWatermarkExts
  }
  return result.map((_: string) => `image/${_}`).join(', ')
}
