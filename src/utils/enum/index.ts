export enum pageType {
  collect = 'collect',
  list = 'list',
  recycle = 'recycle',
  home = 'home', // 官网首页

  index = 'index', // 功能导航介绍页
  convert = 'convert',
  raw = 'raw',
  heic = 'heic',
  livp = 'livp',
  compress = 'compress',
  watermark = 'watermark',
  pdf = 'pdf',
  enlarge = 'enlarge',
  oldPicture = 'oldPicture',
  repair = 'repair',
  cartoon = 'cartoon',
  sketch = 'sketch',

  changeSize = 'changeSize',
  crop = 'crop',
  rename = 'rename',
  rotate = 'rotate',
  classify = 'classify',

  puzzle = 'puzzle', // 拼图
  passport = 'passport', // 证件照
  matting = 'matting', // 智能抠图
  photoTemplate = 'photoTemplate', // 写真模版
}

// 字体列表
export const fontFamilyList = [
  {
    name: '宋体',
    value: '宋体, SimSun, Songti SC',
  },
  {
    name: '微软雅黑',
    value: '微软雅黑, Microsoft YaHei',
  },
  {
    name: '楷体',
    value: '楷体, 楷体_GB2312, SimKai, STKaiti',
  },
  {
    name: '黑体',
    value: '黑体, SimHei, Heiti SC',
  },
  {
    name: '隶书',
    value: '隶书, SimLi',
  },
  {
    name: '仓耳与墨',
    value: '仓耳与墨',
  },
  {
    name: '思源宋体',
    value: '思源宋体',
  },
  // {
  //   name: '方正黑体',
  //   value: '方正黑体'
  // },
  // {
  //   name: '方正楷体',
  //   value: '方正楷体'
  // },
  // {
  //   name: '方正书宋简体',
  //   value: '方正书宋简体'
  // },
  // {
  //   name: '阿里妈妈刀隶体',
  //   value: '阿里妈妈刀隶体'
  // },
  // {
  //   name: '阿里妈妈东方大楷',
  //   value: '阿里妈妈东方大楷'
  // },
  // {
  //   name: '阿里妈妈方圆体',
  //   value: '阿里妈妈方圆体'
  // },
  // {
  //   name: '阿里妈妈灵动体',
  //   value: '阿里妈妈灵动体'
  // },
  {
    name: '阿里巴巴普惠体',
    value: '阿里巴巴普惠体',
  },
  {
    name: '阿里妈妈数黑体',
    value: '阿里妈妈数黑体',
  },

  // 202410
  {
    name: '包图小白体',
    value: '包图小白体',
  },
  {
    name: '钉钉进步体',
    value: '钉钉进步体',
  },
  {
    name: '抖音美好体',
    value: '抖音美好体',
  },
  {
    name: '荆南麦圆体',
    value: '荆南麦圆体',
  },
  {
    name: '优设标题黑',
    value: '优设标题黑',
  },

  // 英文
  {
    name: 'Andale Mono',
    value: 'andale mono',
  },
  {
    name: 'Arial',
    value: 'arial, helvetica, sans-serif',
  },
  {
    name: 'arialBlack',
    value: 'arial black, avant garde',
  },
  {
    name: 'Remem',
    value: 'Remem',
  },
  {
    name: 'Roboto',
    value: 'Roboto',
  },
  // {
  //   name: 'Comic Sans Ms',
  //   value: 'comic sans ms'
  // },
  // {
  //   name: 'Impact',
  //   value: 'impact, chicago'
  // },
  // {
  //   name: 'Times New Roman',
  //   value: 'times new roman'
  // },
  // {
  //   name: 'Sans-Serif',
  //   value: 'sans-serif'
  // },
  // {
  //   name: 'serif',
  //   value: 'serif'
  // }
]
