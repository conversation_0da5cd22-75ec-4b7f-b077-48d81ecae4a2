export const useDebugTool = () => {
  // 调试工具
  if (window.location.href.indexOf('mode=debug') > -1) {
    var script = document.createElement('script')
    script.src = `https://cdn.bootcdn.net/ajax/libs/eruda/3.0.0/eruda.min.js`
    // script.src = `/js/eruda.min.js`
    setTimeout(() => {
      document.body.appendChild(script)
      script.onload = function () {
        window.eruda && window.eruda.init()
      }
    }, 10)
  }
}

export const usePng = (canvas: any) => {
  try {
    const ctx = canvas.getContext('2d')
    const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height)
    const data = imageData.data

    // 快速失败机制：检查图像的四个角
    if (
      data[3] !== 255 || // 左上角
      data[data.length - 4] !== 255 || // 右下角
      data[canvas.width * 4] !== 255 || // 右上角
      data[(canvas.width * canvas.height - 1) * 4] !== 255 // 左下角
    ) {
      return true // 发现非全透明像素
    }

    // 如果四个角都是全透明的，但您仍然想检查整个图像，可以取消注释以下代码
    for (let i = 0; i < data.length; i += 4) {
      if (data[i + 3] !== 255) {
        return true // 发现非全透明像素
      }
    }

    return false // 所有检查的像素都是完全透明的
  } catch (error) {
    console.error('Error getting image data:', error)
    // 可以选择返回 false 或抛出错误，具体取决于您的应用逻辑
    return false
  }
}
