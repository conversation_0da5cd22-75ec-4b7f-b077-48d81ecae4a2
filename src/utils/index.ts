import { MAPDOMAIN } from './configData/config'

const loaded: any = {}
const loadJs = (url: string) => {
  const name = url && url.split('?')[0].split('/').reverse()[0]
  const id = 'js_' + name
  return new Promise<void>((resolve, reject) => {
    if (loaded[id]) {
      return resolve()
    }
    const script = document.createElement('script')
    script.type = 'text/javascript'
    script.async = true
    script.src = url
    script.id = id
    script.onload = () => {
      loaded[id] = true
      resolve()
    }
    script.onerror = () => {}
    document.body.appendChild(script)
  })
}

/**
 * 动态加载样式
 */
const loadCss = (url: string) => {
  const name = url && url.split('?')[0].split('/').reverse()[0]
  const id = 'css_' + name
  return new Promise<void>((resolve, reject) => {
    if (loaded[id]) {
      return resolve()
    }
    var link = document.createElement('link')
    link.type = 'text/css'
    link.rel = 'stylesheet'
    link.href = url
    link.id = id
    link.onload = () => {
      loaded[id] = true
      resolve()
    }
    link.onerror = () => {
      reject()
    }
    document.head.appendChild(link)
  })
}

/**
 * 跳转思维导图
 * isTemplate 是否查看模板
 */
const skipMindMap = ({
  id,
  isTemplate = false,
  mindLayout,
}: {
  id: any
  isTemplate?: boolean
  mindLayout?: string
}) => {
  if (isTemplate) {
    window.open(`${MAPDOMAIN}/mindmap/${id}?isTemplate=true`)
  } else {
    if (mindLayout) {
      window.open(`${MAPDOMAIN}/mindmap/${id}?mindLayout=${mindLayout}`)
    } else {
      window.open(`${MAPDOMAIN}/mindmap/${id}`)
    }
  }
}

/**
 * 跳转流程图
 * isTemplate 是否查看模板
 */
const skipFlowMap = ({
  id,
  isTemplate = false,
  layout,
  name,
  template,
}: {
  id: any
  isTemplate?: boolean
  layout?: string
  name?: string
  template?: string
}) => {
  let url = `${MAPDOMAIN}/diagraming/${id}`

  if (isTemplate) {
    window.open(
      `${url}?isTemplate=true&name=${name || ''}&layout=${layout || ''}`
    )
  } else {
    // if (template) {
    //   url = `${url}?template=${template}${name ? `&name=${name}` : ''}`
    // } else
    if (name) {
      url = `${url}?name=${name}`
    }

    window.open(url)
  }
}

/**
 * 未登录则去登录
 */
const needLogin = (userStore: any) => {
  if (!userStore.token) {
    const loginDom: any = document.querySelector('.loginButton')
    loginDom && loginDom.click()
    return true
  }
  return false
}

// 递归获取文件树列表，给文件夹列表使用
const getLabelTreeList = (folderList: any, result: Array<any>) => {
  folderList.map((_: any) => {
    const target: any = {
      label: _.name,
      id: _.id,
    }

    if (_.children && _.children.length) {
      target.children = []
      result.push(target)
      getLabelTreeList(_.children, target.children)
    } else {
      result.push(target)
    }
  })
}

// 递归获取文件树列表的键值对 {1: {}, 2: {}, ...}
const getFolderListKeyValue = (folderList: any, folderListKeyValue: Record<string | number, any>) => {
  folderList.map((_: any) => {
    folderListKeyValue[_.id] = _

    if (_.children && _.children.length) {
      getFolderListKeyValue(_.children, folderListKeyValue)
    }
  })
}

// 递归获取匹配的文件夹
const findFolderByList = (folderList: any, targetId: string | number) => {
  folderList.map((_: any) => {
    if (_.id === targetId) {
      return _
    }
    if (_.children && _.children.length) {
      findFolderByList(_.children, targetId)
    }
  })
}

/**
 * isVip
 * @param user
 * @returns true 会员 false 非会员
 */
const isVip = (user: any) => {
  return user.type !== 1 // 1 未付费
}
/**
 * isVipExpire
 * @param user
 * @param proxy
 * @returns true 已过期 false 未过期
 */
const isVipExpire = (user: any, proxy?: any) => {
  const expire = new Date() > new Date(user.vip_expire) && user.type !== 4 // 4 终身会员
  if (expire && proxy) {
    proxy.$message.error('会员已过期，请先续费 VIP！')
  }
  return expire
}

/**
 * 检测是否指定日期内，如双12
 * isDateInRange(Date.now(), '2023-12-11', '2023-12-12')
 * @param dateToCheck
 * @param startDate
 * @param endDate
 * @returns
 */
const isDateInRange = (dateToCheck: any, startDate: any, endDate: any) => {
  const checkDate = new Date(dateToCheck)
  const startRange = new Date(startDate)
  const endRange = new Date(endDate)

  // 将时间设置为午夜
  checkDate.setHours(0, 0, 0, 0)
  startRange.setHours(0, 0, 0, 0)
  endRange.setHours(23, 59, 59, 999)

  return checkDate >= startRange && checkDate <= endRange
}

function isMac() {
  const userAgent = navigator.userAgent
  return /Macintosh|Mac OS X|macOS/.test(userAgent)
}

export {
  loadJs,
  loadCss,
  skipMindMap,
  skipFlowMap,
  needLogin,
  getLabelTreeList,
  getFolderListKeyValue,
  findFolderByList,
  isVip,
  isVipExpire,
  isDateInRange,
  isMac,
}

export * from './indexExt'
export * from './image'
export * from './debounce'
