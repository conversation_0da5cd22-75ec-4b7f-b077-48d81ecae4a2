import { createRouter, createWebHistory } from 'vue-router'
import watermarkEdit from './modules/watermarkEdit'
import picture from './modules/picture'
import pictureIndex from './modules/pictureIndex'
import homeView from '../views/HomeView.vue'
import testView from '../views/TestView.vue'
// meta:
// icon:Element-icon图标
// svgIcon：自定义icon图标
// icon与svgIcon只能存在一个
// hidden:控制菜单栏是否显示
// affix： tag标签是否锁定
// keepAlive: 是否缓存
// justChild: 只显示所有子菜单

export const constantRoutes = [
  {
    path: '/404',
    component: () => import('@/views/ErrorPage/404.vue'),
    meta: {
      title: '404',
      hidden: true,
    },
  },
  {
    path: '/401',
    component: () => import('@/views/ErrorPage/401.vue'),
    meta: {
      title: '401',
      hidden: true,
    },
  },
  {
    path: '/login',
    component: () => import('@/views/login/index.vue'),
    meta: {
      title: '登录',
    },
  },
]

export const asyncRoutes = [
  {
    path: '/',
    // redirect: '/home',
    redirect: '/index', // TODO
  },
  {
    path: '/home',
    name: 'home',
    component: homeView,
  },
  {
    path: '/test',
    name: 'test',
    component: testView,
  },
  picture,
  pictureIndex,
  watermarkEdit,
]

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [...constantRoutes, ...asyncRoutes],
})

export default router
