import request from '@/utils/request'
import { DOMAIN } from "@/utils/configData/config";

export function GetWXQRCode() {
  // {
  //     "scene_id": 2329429066,//用来轮询登录信息接口
  //     "url": "https://mp.weixin.qq.com/cgi-bin/showqrcode?ticket=gQFH8DwAAAAAAAAAAS5odHRwOi8vd2VpeGluLnFxLmNvbS9xLzAyWVlfTXBTYXdjeEYxS3N1Tmh5Y0UAAgQcpKhiAwSAOgkA" //微信的二维码地址，直接用来显示
  // }
  return request({
    url: `${DOMAIN}/v1/user/getVxLoginQrcode`,
    method: 'get',
  })
}
export function GetLenovoLoginResult(token: number | string) {
  // header: lenovo_token,app_id,channel
  return request({
    url: `${DOMAIN}/v1/user/lenovoLogin`,
    method: 'get',
    headers: {
      lenovo_token: token || localStorage.lenovoToken || '',
    }
  })
}
export function GetWXQRLoginResult(scene_id: number | string) {
  // {
  //     "code": 0,
  //     "data": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoxMDAwMiwibmlja19uYW1lIjoiXHUwMDFlXHVmZmZkZiIsImV4cCI6MTY4Njc1NTc5OH0.eXFGdEUWmtAIwWXZiczjqCJtvez7NnInEkcum_pV31I" //返回的token，可以解出用户信息。请求其他的接口的时候也放在header里面
  // }
  return request({
    url: `${DOMAIN}/v1/user/checkScene?scene_id=${scene_id}`,
    method: 'get',
  })
}
export function GetUserInfo() {
  // "vip_expire": "0001-01-01T00:00:00Z",
  // "type": 1, // 是否VIP, 1: "未付费", 2: "季度会员", 3: "年费会员", 4: "终身会员", 5: "半年会员", 6: "月会员"
  return request({
    url: `${DOMAIN}/v1/user/info`,
    method: 'get',
  })
}
export function GetUserInfoAndToken() {
  return request({
    url: `${DOMAIN}/v1/user/info_with_token`,
    method: 'get',
  }).then(_ => {
    if (_.data.code === 0 && _.data.token) {
      localStorage.token = _.data.token
    } else {
      console.error('用户信息获取失败', _.data)
    }
    return _
  })
}

export function GetCustomer() {
  return request({
    url: `${DOMAIN}/v1/web_config`,
    method: 'get',
  })
}