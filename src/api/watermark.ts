import request from '@/utils/request'
const TESTDOMAIN = 'http://120.55.162.149:9991'
const CONVERTDOMAIN = '//picapi.miaoji66.com'

export function getCategoryList(
  params: any = {
    page: 1,
    size: 100,
    sort: undefined, // id,desc
  }
) {
  // "id": 6,
  // "name": "测试分类2",
  // "parent_id": 0,
  // "status": 1,
  // "sort": 0,
  // "sticky": -1,
  // "created_at": "2024-07-19T13:48:08+08:00",
  // "updated_at": "2024-07-19T13:48:13+08:00"
  return request({
    url: `${CONVERTDOMAIN}/v1/image_converter/icon_category/list`,
    method: 'get',
    params,
  })
}
export function getListAll() {
  return request({
    url: `${CONVERTDOMAIN}/v1/category/list_all`,
    method: 'get',
  })
}

// ==== TOC ====
export function getDetail(
  params: any = {
    id: undefined,
  }
) {
  return request({
    url: `${TESTDOMAIN}/v1/image_converter/template/detail`,
    method: 'get',
    params,
  })
}

export function saveDetail(data: any) {
  return request({
    url: `${TESTDOMAIN}/v1/image_converter/template/save`,
    method: 'post',
    data,
  })
}

/**
 * 上传图片到阿里云
 * @param data
 * @returns 
 */
export function uploadImage(
  data: any = {
    file: undefined,
  }
) {
  return request({
    url: `${TESTDOMAIN}/v1/upload/image`,
    method: 'post',
    data,
    transformRequest(data) {
      const formData = new FormData()
      for (const item in data) {
        formData.append(item, data[item])
      }
      return formData
    },
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
  })
}
