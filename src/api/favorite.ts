import request from '@/utils/request'

export function GetFavoriteList(params: any = {
  page: 1,
  page_size: 20,
}) {
  return request({
    url: '/v1/favorite/list',
    method: 'get',
    params
  })
}

export function Star(data: any) {
  return request({
    url: '/v1/favorite/star',
    method: 'post',
    data
  })
}

export function UnStar(data: any) {
  return request({
    url: '/v1/favorite/un_star',
    method: 'post',
    data
  })
}

