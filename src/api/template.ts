import request from '@/utils/request'

// 获取模板列表
// http://101.132.144.190:9992/v1/template/mind_list
export function GetTemplateList(params: any = {
  type: undefined, // 类型id
  category_id: undefined, // 场景id
  name: undefined,
  page: 1,
  page_size: 100,
}) {
  // [
  //   {
  //     "category_id": 0,
  //     "created_at": "string",
  //     "detail": "string",
  //     "id": 0,
  //     "name": "string",
  //     "status": 0,
  //     "thumbnail": "string",
  //     "type": 0,
  //     "updated_at": "string"
  //   }
  // ]
  return request({
    url: '/v1/template/mind_list',
    method: 'get',
    params: {
      ...params,
      type: params.type || '',
      template_type: 1 // 1 之前的模版，2 详情页模版。不传的话默认1和2的数据都返回
    }
  })
}

// 获取类型分类列表
export function GetTypeList() {
  return request({
    url: '/v1/template/type_list',
    method: 'get',
  })
}

// 获取场景分类列表
// http://101.132.144.190:9992/v1/category/list
export function GetCategoryList(params: any = {
  parentId: undefined,
}) {
  // [
  //   {
  //     "create_time": "string",
  //     "id": 0,
  //     "name": "string",
  //     "parent_id": 0,
  //     "status": 0,
  //     "update_time": "string"
  //   }
  // ]
  return request({
    url: '/v1/category/list',
    method: 'get',
    params
  })
}

// 导图复制
export function CopyTemplate(id: number) {
  return request({
    url: `/v1/template/use?template_id=${id}`,
    method: 'get',
  })
}