import request from '@/utils/request'

// 移入回收站
export function CreateRecycleFile(data: any) {
  return request({
    url: '/v1/recycle/move_in',
    method: 'post',
    data
  })
}

// 获取回收站列表
export function GetRecycleList(data: any = {
  page: 1,
  page_size: 20,
  userId: undefined,
}) {
  // [
  //   {
  //     "created_at": "string",
  //     "detail": 0,
  //     "expire_at": 0,
  //     "id": 0,
  //     "name": "string",
  //     "status": 0,
  //     "type": 0,
  //     "updated_at": "string",
  //     "user_id": 0
  //   }
  // ]
  return request({
    url: '/v1/recycle/list',
    method: 'get',
    data
  })
}

// 从回收站移除
export function RemoveRecycleFile(data: any) {
  return request({
    url: `/v1/recycle/remove`,
    method: 'post',
    data
  })
}

// 彻底删除 user_id
export function DeleteRecycleFile(data: any) {
  return request({
    url: `/v1/recycle/delete`,
    method: 'post',
    data
  })
}

// 清空回收站
export function ClearRecycleFile() {
  return request({
    url: `/v1/recycle/clear`,
    method: 'get',
  })
}
