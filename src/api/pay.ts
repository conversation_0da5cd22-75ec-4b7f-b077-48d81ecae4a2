import request from '@/utils/request'
import QRCode from 'qrcode'
import {
  // APPID,
  // CHANNEL,
  DOMAIN
} from '@/utils/configData/config'

export function GetSQBQRCode(
  params: any = {
    product_id: undefined,
  }
) {
  // if (window.isElectron && APPID === '60001' && CHANNEL.includes('360')) {
  if (window.isElectron && window.isWN && !window.isLenovo) { // TODO 临时方案，下版改回去
    // 360支付，需要客户端获取支付链接并生成二维码
    return Create360Order({
      product_id: Number(params.product_id),
    })
  } else {
    return request({
      url: `${DOMAIN}/v1/pay/sqbQrcode?product_id=${params.product_id}`,
      method: 'get',
    })
  }
}

// axios post请求，如何将post参数以?a=b这种形式传过去？答：自己转换下
export function Create360Order(data: any) {
  return new Promise((resolve, reject) => {
    request({
      url: `${DOMAIN}/v1/pay/create360Order`,
      method: 'post',
      data,
    })
      .then((_) => {
        if (_.data.code === 0) {
          const req = { ..._.data }

          window.ipcRendererApi?.send('get-360-pay-url', {
            data: JSON.stringify({
              orderId: req.data || '',
              price: String(data.product_id).slice(-2) || '',
              userId: JSON.parse(localStorage.user || '{}').id || '',
            }),
          })

          window.ipcRendererApi?.removeAllListeners('get-360-pay-url-callback')
          window.ipcRendererApi?.on(
            'get-360-pay-url-callback',
            (event: any, url: string) => {
              // 1、兼容：如果360支付二维码获取失败，则使用微信支付
              if (!url) {
                request({
                  url: `${DOMAIN}/v1/pay/sqbQrcode?product_id=${data.product_id}`,
                  method: 'get',
                }).then(_ => {
                  if (_.data.code === 0) {
                    resolve(_)
                  } else {
                    reject('兼容支付二维码获取失败')
                    console.error('兼容支付二维码获取失败')
                  }
                })
                return
              }

              // 2、360支付二维码获取成功
              const options = {
                width: 136,
                height: 136,
              }
              QRCode.toDataURL(
                url,
                options,
                function (error: any, base64Data: string) {
                  if (error) {
                    return reject(error)
                  }
                  _.data.data = base64Data.replace(
                    /data:image\/\w{2,5};base64,/,
                    ''
                  )
                  console.log('get360PayUrlCallback', url, _.data)
                  resolve(_)
                }
              )
            }
          )

          // window.parent.postMessage(
          //   {
          //     func: 'get360PayUrl',
          //     message: JSON.stringify({
          //       orderId: req.data || '',
          //       price: String(data.product_id).slice(-2) || '',
          //       userId: JSON.parse(localStorage.user || '{}').id || '',
          //     }),
          //   },
          //   '*'
          // )
          // window.get360PayUrlCallback = (url: string) => {
          //   const options = {
          //     width: 136,
          //     height: 136,
          //   }
          //   QRCode.toDataURL(
          //     url,
          //     options,
          //     function (error: any, base64Data: string) {
          //       if (error) {
          //         return reject(error)
          //       }
          //       _.data.data = base64Data.replace(
          //         /data:image\/\w{2,5};base64,/,
          //         ''
          //       )
          //       console.log('get360PayUrlCallback', url, _.data)
          //       resolve(_)
          //     }
          //   )
          // }
        } else {
          reject(_)
        }
      })
      .catch((err) => {
        reject(err)
      })
  })
}

export function GetLXQRCode(
  params: any = {
    product_id: undefined,
    lenovo_token: undefined,
  }
) {
  return request({
    url: `${DOMAIN}/v1/pay/lenovoQrcode?product_id=${params.product_id}&lenovo_token=${params.lenovo_token}`,
    method: 'get',
  })
}
