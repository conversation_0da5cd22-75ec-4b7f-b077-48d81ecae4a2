import request from '@/utils/request'

export function Create(data: any = {
  id: undefined,
}) {
  // {
  //   "share_id": 0,
  //   "share_url": "string"
  // }
  return request({
    url: '/v1/share/create',
    method: 'post',
    data
  })
}

export function Save(data: any = {
  id: undefined,
}) {
  return request({
    url: '/v1/share/save',
    method: 'post',
    data
  })
}

export function Visit(params: any = {
  shareId: undefined,
  password: undefined,
}) {
  // {
  //   "data": "string"
  // }
  return request({
    url: `/v1/share/visit`,
    method: 'get',
    params
  })
}
