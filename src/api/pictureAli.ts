import request from '@/utils/request'
// import { CONVERTDOMAIN } from '@/utils/configData/config'
// const TESTDOMAIN = 'http://120.55.162.149:9991'
const CONVERTDOMAIN = '//picapi.miaoji66.com'

// 接口文档：https://tower.im/teams/938213/repository_documents/2969/

/**
 * 图片放大
 * @param data { url, upscale_factor } // upscale_factor 1-4
 * @returns { code, data, message }
 */
export function superResolution(data: any) {
  data.upscale_factor = 4

  return request({
    url: `${CONVERTDOMAIN}/v1/image/super_resolution`,
    method: 'post',
    data,
  })
}

/**
 * 老照片上色
 * @param data { url }
 * @returns { code, data, message }
 */
export function colorize(data: any) {
  return request({
    url: `${CONVERTDOMAIN}/v1/image/colorize`,
    method: 'post',
    data,
  })
}

/**
 * 脸部修复
 * @param data { url }
 * @returns { code, data, message }
 */
export function face(data: any) {
  return request({
    url: `${CONVERTDOMAIN}/v1/image/enhance_face`,
    method: 'post',
    data,
  })
}

/**
 * 图片动漫化
 * @param data { url, algo_type }
 * @returns { code, data, message }
 */
export function humanAnime(data: any) {
  //anime：日漫风
  //3d：3D特效
  //handdrawn：手绘风
  //sketch：铅笔画
  //artstyle：艺术特效
  //claborate ：国画工笔画
  //hongkong ：港漫风
  //comic：漫画风格
  //animation3d：动画3D
  return request({
    url: `${CONVERTDOMAIN}/v1/image/human_anime`,
    method: 'post',
    data,
  })
}

/**
 * 图片素描化
 * @param data { url, return_type } // return_type full：全身照， head：大头照（默认）
 * @returns { code, data, message }
 */
export function humanSketch(data: any) {
  data.return_type = 'full'

  return request({
    url: `${CONVERTDOMAIN}/v1/image/human_sketch`,
    method: 'post',
    data,
  })
}
