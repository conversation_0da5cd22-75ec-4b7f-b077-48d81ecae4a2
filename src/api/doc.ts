import request from '@/utils/request'

// 创建目录
export function CreateFolder(data: any = {
  name: undefined,
  parent_id: undefined,
}) {
  return request({
    url: '/v1/folder/save',
    method: 'post',
    data
  })
}

// 获取目录树
export function GetDirectoryTree() {
  // "created_str": "string",
  // "detail": 0,
  // "id": 0,
  // "level": 0,
  // "name": "string",
  // "parent_id": 0,
  // "star": true,
  // "status": 0,
  // "type": 0,
  // "updated_str": "string",
  // "user_id": 0,
  // "visit_time_str": "string"
  return request({
    url: `/v1/folder/directory_tree`,
    method: 'get',
  })
}

// 打开目录并获取
export function GetOpenFolder(params: any = {
  folder_id: undefined,
  name: undefined,
  user_id: undefined,
  page: 1,
  page_size: 20,
}) {
  // "created_at": "string",
  // "detail": 0,
  // "id": 0,
  // "name": "string",
  // "parent_id": 0,
  // "star": true,
  // "status": 0,
  // "type": 0, // type 1=文件，2=目录
  // "updated_at": "string",
  // "user_id": 0,
  // "visit_time": 0
  return request({
    url: `/v1/folder/open`,
    method: 'get',
    params
  })
}

// =============File============

// 创建或更新文件
export function SaveFile(data: any = {
  created_at: '',
  detail: undefined,
  folder_id: undefined,
  id: undefined,
  name: undefined,
  status: undefined,
  type: undefined,
  updated_at: undefined,
  user_id: undefined,
  layout: undefined,
}) {
  return request({
    url: '/v1/file/save',
    method: 'post',
    data
  })
}

// 获取文件内容
export function GetFile(params: any = {
  file_id: undefined,
  user_id: undefined,
}) {
  // {
  //   "created_at": "string",
  //   "detail": "string",
  //   "folder_id": 0,
  //   "id": 0,
  //   "name": "string",
  //   "status": 0,
  //   "type": 0,
  //   "updated_at": "string",
  //   "user_id": 0
  // }
  return request({
    url: `/v1/file/get`,
    method: 'get',
    params
  })
}

// 获取导图数量
export function GetTotal(params?: any) {
  return request({
    url: `/v1/file/total`,
    method: 'get',
    params
  })
}

// ===========================

// 批量移动到
export function BatchMove(data: any = [{
  type: undefined, // type ： 1文件，2目录
  detail: undefined, // 文件或者目录的ID
  folder_id: undefined // 移动到的目标目录ID
}]) {
  return request({
    url: '/v1/document/batch_move',
    method: 'post',
    data
  })
}
// 批量复制到
export function BatchCopy(data: any = [{
  type: undefined, // type ： 1文件，2目录
  detail: undefined, // 文件或者目录的ID
  folder_id: undefined // 复制到的目标目录ID
}]) {
  return request({
    url: '/v1/document/batch_copy',
    method: 'post',
    data
  })
}

// ==== 阿里云 ====
/**
 * 获取STS
 * STS 100QPS，超过流量会被限制
 * @returns
 */
export function GetSTS() {
  return request({
    url: `/v1/upload/get_sts`, // ?bucket=imageconverter`,
    method: 'get',
  })
}

/**
 * 预处理，获取上传的文件信息
 * @param {string} type image or mind
 * @returns
 */
export function PreUpload(type = 'mind', fileName = '') {
  return request({
    url: `/v1/upload/pre_upload?type=${type}&file_name=${fileName}`,
    method: 'get',
  })
}

/**
 * 获取阿里云文件
 * @param {string} url
 * @returns
 */
export function GetOSSFile(url) {
  return request({
    url,
    method: 'get',
  })
}

// 上传图片
export function UploadImage(data: any = {
  path: undefined,
  file_id: undefined,
}) {
  return request({
    url: '/v1/upload/image',
    method: 'post',
    data
  })
}

/**
 * 获取图片
 * @param {Object} data images array[string]
 * @returns
 */
export function GetImage(data: any = {
  images: undefined, // array[string] 非必须
  file_id: undefined
}) {
  return request({
    url: '/v1/file/get_image',
    method: 'post',
    data: {
      ...data,
      file_id: Number(data.file_id)
    }
  })
}
