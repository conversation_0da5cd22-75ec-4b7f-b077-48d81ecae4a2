#!/bin/bash
# /etc/nginx/nginx.conf # 测试环境，nginx -s reload (使用中)
# /etc/nginx/conf.d/www.conf # 线上环境，包含http和多个https2块配置

# server {
#     listen 80;
#     server_name www.mypicture365.com wn.bestpicture365.com a.bestpicture365.com bao.bestpicture365.com ba.bestpicture365.com pic.miaoji66.com;

#     gzip  on;
#     gzip_min_length 1k;
#     gzip_comp_level 5;
#     gzip_types text/plain application/javascript application/x-javascript text/javascript text/xml text/css;
#     gzip_disable "MSIE [1-6]\.";
#     gzip_vary on;

#     location / {
#         root /var/www/web/picture;
#         try_files $uri $uri/ /index.html;
#     }
# }

# 测试
# ssh work@***************
# ssh root@*************** # work连接后，1、输入：su root 2、输入密码：123abcZ,

# 线上
# ssh work@************
# ssh root@************ # work连接后，1、输入：su root 2、输入密码：123abcZ,

# cat /home/<USER>/.pm2/logs/log-out-1.log | grep -E "$(date '+%Y-%m-%d').*V"
# cat /home/<USER>/.pm2/logs/log-out-1.log | grep -E "$(date '+%Y-%m-%d').*convert"
# cat /home/<USER>/.pm2/logs/log-out-1.log | grep ".*convert|payV"
# cat /var/log/nginx/wnpic.miaoji66.com.access.log | grep -E $(LC_TIME=C date '+%d/%b/%Y') | grep "GET /picture/.* HTTP" | wc -l
# cat /var/log/nginx/pic.miaoji66.com.access.log | grep -E $(LC_TIME=C date '+%d/%b/%Y') | grep "GET /picture/.* HTTP" | wc -l

online=************
test=***************
opt=$1
target=$test
port=22

function initParams() {
    if [[ $opt == dev ]] || [[ $opt == devd ]];
    then
        target=$test
    elif [[ $opt == online ]] || [[ $opt == onlined ]];
    then
        target=$online
    fi
}

function build() {
    if [[ -z $opt ]] || [[ $opt == dev ]] || [[ $opt == online ]]
    then
        rm -rf picture.tar.gz dist
        yarn build
        rm -rf dist/fonts # 删除字体，增量部署，避免压缩包太大
        cp -r dist picture
        tar -czf picture.tar.gz picture
        rm -rf picture
    fi
}

function deploy() {
    scp -r -P ${port} picture.tar.gz work@${target}:/var/www/web

    echo 开始部署
    ssh -p ${port} work@${target}<<EOF
    echo 成功登录到远程服务器 ${target}
    cd /var/www/web/
    echo 解压 picture.tar.gz
    tar -xzf picture.tar.gz;
    rm -rf picture.tar.gz;
EOF
    echo 部署完成
}

function invokeDeploy() {
    # online
    if [[ $opt == online ]] || [[ $opt == onlined ]]; then
        read -r -p "Are You Sure? [Y/n] " input
        case $input in
        [yY][eE][sS] | [yY])
            echo "Yes"
            initParams
            build
            deploy
            exit 0
            ;;
        [nN][oO] | [nN])
            echo "No"
            exit 0
            ;;
        *)
            echo "Invalid input..."
            exit 1
            ;;
        esac
    fi
    # test
    initParams
    build
    deploy
    exit 0
}

invokeDeploy
