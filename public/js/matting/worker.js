(() => {
    var t = {
            392: (t, r, e) => {
                var n = e(419),
                    o = e(3353),
                    i = TypeError;
                t.exports = function (t) {
                    if (n(t)) return t;
                    throw i(o(t) + " is not a function")
                }
            },
            8248: (t, r, e) => {
                var n = e(419),
                    o = String,
                    i = TypeError;
                t.exports = function (t) {
                    if ("object" == typeof t || n(t)) return t;
                    throw i("Can't set " + o(t) + " as a prototype")
                }
            },
            7950: (t, r, e) => {
                var n = e(776),
                    o = String,
                    i = TypeError;
                t.exports = function (t) {
                    if (n(t)) return t;
                    throw i(o(t) + " is not an object")
                }
            },
            8295: t => {
                t.exports = "undefined" != typeof ArrayBuffer && "undefined" != typeof DataView
            },
            683: (t, r, e) => {
                "use strict";
                var n, o, i, a = e(8295),
                    u = e(9631),
                    c = e(7358),
                    s = e(419),
                    p = e(776),
                    f = e(7322),
                    l = e(5976),
                    y = e(3353),
                    d = e(1904),
                    h = e(3460),
                    v = e(9468),
                    g = e(7673),
                    b = e(4945),
                    m = e(6184),
                    w = e(854),
                    x = e(6862),
                    A = e(7624),
                    T = A.enforce,
                    S = A.get,
                    _ = c.Int8Array,
                    j = _ && _.prototype,
                    O = c.Uint8ClampedArray,
                    E = O && O.prototype,
                    P = _ && b(_),
                    U = j && b(j),
                    M = Object.prototype,
                    I = c.TypeError,
                    D = w("toStringTag"),
                    C = x("TYPED_ARRAY_TAG"),
                    F = "TypedArrayConstructor",
                    R = a && !!m && "Opera" !== l(c.opera),
                    L = !1,
                    H = {
                        Int8Array: 1,
                        Uint8Array: 1,
                        Uint8ClampedArray: 1,
                        Int16Array: 2,
                        Uint16Array: 2,
                        Int32Array: 4,
                        Uint32Array: 4,
                        Float32Array: 4,
                        Float64Array: 8
                    },
                    k = {
                        BigInt64Array: 8,
                        BigUint64Array: 8
                    },
                    z = function (t) {
                        if (!p(t)) return !1;
                        var r = l(t);
                        return "DataView" === r || f(H, r) || f(k, r)
                    },
                    B = function (t) {
                        var r = b(t);
                        if (p(r)) {
                            var e = S(r);
                            return e && f(e, F) ? e[F] : B(r)
                        }
                    },
                    N = function (t) {
                        if (!p(t)) return !1;
                        var r = l(t);
                        return f(H, r) || f(k, r)
                    },
                    W = function (t) {
                        if (N(t)) return t;
                        throw I("Target is not a typed array")
                    },
                    V = function (t) {
                        if (s(t) && (!m || g(P, t))) return t;
                        throw I(y(t) + " is not a typed array constructor")
                    },
                    G = function (t, r, e, n) {
                        if (u) {
                            if (e)
                                for (var o in H) {
                                    var i = c[o];
                                    if (i && f(i.prototype, t)) try {
                                        delete i.prototype[t]
                                    } catch (a) {
                                        try {
                                            i.prototype[t] = r
                                        } catch (s) {}
                                    }
                                }
                            U[t] && !e || h(U, t, e ? r : R && j[t] || r, n)
                        }
                    },
                    Y = function (t, r, e) {
                        var n, o;
                        if (u) {
                            if (m) {
                                if (e)
                                    for (n in H)
                                        if (o = c[n], o && f(o, t)) try {
                                            delete o[t]
                                        } catch (i) {}
                                if (P[t] && !e) return;
                                try {
                                    return h(P, t, e ? r : R && P[t] || r)
                                } catch (i) {}
                            }
                            for (n in H) o = c[n], !o || o[t] && !e || h(o, t, r)
                        }
                    };
                for (n in H) o = c[n], i = o && o.prototype, i ? T(i)[F] = o : R = !1;
                for (n in k) o = c[n], i = o && o.prototype, i && (T(i)[F] = o);
                if ((!R || !s(P) || P === Function.prototype) && (P = function () {
                        throw I("Incorrect invocation")
                    }, R))
                    for (n in H) c[n] && m(c[n], P);
                if ((!R || !U || U === M) && (U = P.prototype, R))
                    for (n in H) c[n] && m(c[n].prototype, U);
                if (R && b(E) !== U && m(E, U), u && !f(U, D))
                    for (n in L = !0, v(U, D, {
                            configurable: !0,
                            get: function () {
                                return p(this) ? this[C] : void 0
                            }
                        }), H) c[n] && d(c[n], C, n);
                t.exports = {
                    NATIVE_ARRAY_BUFFER_VIEWS: R,
                    TYPED_ARRAY_TAG: L && C,
                    aTypedArray: W,
                    aTypedArrayConstructor: V,
                    exportTypedArrayMethod: G,
                    exportTypedArrayStaticMethod: Y,
                    getTypedArrayConstructor: B,
                    isView: z,
                    isTypedArray: N,
                    TypedArray: P,
                    TypedArrayPrototype: U
                }
            },
            6008: (t, r, e) => {
                var n = e(6042);
                t.exports = function (t, r) {
                    var e = 0,
                        o = n(r),
                        i = new t(o);
                    while (o > e) i[e] = r[e++];
                    return i
                }
            },
            5416: (t, r, e) => {
                var n = e(422),
                    o = e(2985),
                    i = e(7475),
                    a = e(6042),
                    u = function (t) {
                        var r = 1 == t;
                        return function (e, u, c) {
                            var s, p, f = i(e),
                                l = o(f),
                                y = n(u, c),
                                d = a(l);
                            while (d-- > 0)
                                if (s = l[d], p = y(s, d, f), p) switch (t) {
                                    case 0:
                                        return s;
                                    case 1:
                                        return d
                                }
                            return r ? -1 : void 0
                        }
                    };
                t.exports = {
                    findLast: u(0),
                    findLastIndex: u(1)
                }
            },
            3180: (t, r, e) => {
                var n = e(6042);
                t.exports = function (t, r) {
                    for (var e = n(t), o = new r(e), i = 0; i < e; i++) o[i] = t[e - i - 1];
                    return o
                }
            },
            3663: (t, r, e) => {
                var n = e(6042),
                    o = e(1860),
                    i = RangeError;
                t.exports = function (t, r, e, a) {
                    var u = n(t),
                        c = o(e),
                        s = c < 0 ? u + c : c;
                    if (s >= u || s < 0) throw i("Incorrect index");
                    for (var p = new r(u), f = 0; f < u; f++) p[f] = f === s ? a : t[f];
                    return p
                }
            },
            5173: (t, r, e) => {
                var n = e(1890),
                    o = n({}.toString),
                    i = n("".slice);
                t.exports = function (t) {
                    return i(o(t), 8, -1)
                }
            },
            5976: (t, r, e) => {
                var n = e(5705),
                    o = e(419),
                    i = e(5173),
                    a = e(854),
                    u = a("toStringTag"),
                    c = Object,
                    s = "Arguments" == i(function () {
                        return arguments
                    }()),
                    p = function (t, r) {
                        try {
                            return t[r]
                        } catch (e) {}
                    };
                t.exports = n ? i : function (t) {
                    var r, e, n;
                    return void 0 === t ? "Undefined" : null === t ? "Null" : "string" == typeof (e = p(r =
                            c(t), u)) ? e : s ? i(r) : "Object" == (n = i(r)) && o(r.callee) ? "Arguments" :
                        n
                }
            },
            123: (t, r, e) => {
                var n = e(6400);
                t.exports = !n((function () {
                    function t() {}
                    return t.prototype.constructor = null, Object.getPrototypeOf(new t) !== t.prototype
                }))
            },
            1904: (t, r, e) => {
                var n = e(9631),
                    o = e(928),
                    i = e(5442);
                t.exports = n ? function (t, r, e) {
                    return o.f(t, r, i(1, e))
                } : function (t, r, e) {
                    return t[r] = e, t
                }
            },
            5442: t => {
                t.exports = function (t, r) {
                    return {
                        enumerable: !(1 & t),
                        configurable: !(2 & t),
                        writable: !(4 & t),
                        value: r
                    }
                }
            },
            9468: (t, r, e) => {
                var n = e(9843),
                    o = e(928);
                t.exports = function (t, r, e) {
                    return e.get && n(e.get, r, {
                        getter: !0
                    }), e.set && n(e.set, r, {
                        setter: !0
                    }), o.f(t, r, e)
                }
            },
            3460: (t, r, e) => {
                var n = e(419),
                    o = e(928),
                    i = e(9843),
                    a = e(1615);
                t.exports = function (t, r, e, u) {
                    u || (u = {});
                    var c = u.enumerable,
                        s = void 0 !== u.name ? u.name : r;
                    if (n(e) && i(e, s, u), u.global) c ? t[r] = e : a(r, e);
                    else {
                        try {
                            u.unsafe ? t[r] && (c = !0) : delete t[r]
                        } catch (p) {}
                        c ? t[r] = e : o.f(t, r, {
                            value: e,
                            enumerable: !1,
                            configurable: !u.nonConfigurable,
                            writable: !u.nonWritable
                        })
                    }
                    return t
                }
            },
            1615: (t, r, e) => {
                var n = e(7358),
                    o = Object.defineProperty;
                t.exports = function (t, r) {
                    try {
                        o(n, t, {
                            value: r,
                            configurable: !0,
                            writable: !0
                        })
                    } catch (e) {
                        n[t] = r
                    }
                    return r
                }
            },
            9631: (t, r, e) => {
                var n = e(6400);
                t.exports = !n((function () {
                    return 7 != Object.defineProperty({}, 1, {
                        get: function () {
                            return 7
                        }
                    })[1]
                }))
            },
            8465: t => {
                var r = "object" == typeof document && document.all,
                    e = "undefined" == typeof r && void 0 !== r;
                t.exports = {
                    all: r,
                    IS_HTMLDDA: e
                }
            },
            5354: (t, r, e) => {
                var n = e(7358),
                    o = e(776),
                    i = n.document,
                    a = o(i) && o(i.createElement);
                t.exports = function (t) {
                    return a ? i.createElement(t) : {}
                }
            },
            9173: t => {
                t.exports = "undefined" != typeof navigator && String(navigator.userAgent) || ""
            },
            5068: (t, r, e) => {
                var n, o, i = e(7358),
                    a = e(9173),
                    u = i.process,
                    c = i.Deno,
                    s = u && u.versions || c && c.version,
                    p = s && s.v8;
                p && (n = p.split("."), o = n[0] > 0 && n[0] < 4 ? 1 : +(n[0] + n[1])), !o && a && (n = a.match(
                        /Edge\/(\d+)/), (!n || n[1] >= 74) && (n = a.match(/Chrome\/(\d+)/), n && (o = +n[1]))),
                    t.exports = o
            },
            6400: t => {
                t.exports = function (t) {
                    try {
                        return !!t()
                    } catch (r) {
                        return !0
                    }
                }
            },
            422: (t, r, e) => {
                var n = e(5897),
                    o = e(392),
                    i = e(8427),
                    a = n(n.bind);
                t.exports = function (t, r) {
                    return o(t), void 0 === r ? t : i ? a(t, r) : function () {
                        return t.apply(r, arguments)
                    }
                }
            },
            8427: (t, r, e) => {
                var n = e(6400);
                t.exports = !n((function () {
                    var t = function () {}.bind();
                    return "function" != typeof t || t.hasOwnProperty("prototype")
                }))
            },
            3577: (t, r, e) => {
                var n = e(8427),
                    o = Function.prototype.call;
                t.exports = n ? o.bind(o) : function () {
                    return o.apply(o, arguments)
                }
            },
            7961: (t, r, e) => {
                var n = e(9631),
                    o = e(7322),
                    i = Function.prototype,
                    a = n && Object.getOwnPropertyDescriptor,
                    u = o(i, "name"),
                    c = u && "something" === function () {}.name,
                    s = u && (!n || n && a(i, "name").configurable);
                t.exports = {
                    EXISTS: u,
                    PROPER: c,
                    CONFIGURABLE: s
                }
            },
            743: (t, r, e) => {
                var n = e(1890),
                    o = e(392);
                t.exports = function (t, r, e) {
                    try {
                        return n(o(Object.getOwnPropertyDescriptor(t, r)[e]))
                    } catch (i) {}
                }
            },
            5897: (t, r, e) => {
                var n = e(5173),
                    o = e(1890);
                t.exports = function (t) {
                    if ("Function" === n(t)) return o(t)
                }
            },
            1890: (t, r, e) => {
                var n = e(8427),
                    o = Function.prototype,
                    i = o.call,
                    a = n && o.bind.bind(i, i);
                t.exports = n ? a : function (t) {
                    return function () {
                        return i.apply(t, arguments)
                    }
                }
            },
            9694: (t, r, e) => {
                var n = e(7358),
                    o = e(419),
                    i = function (t) {
                        return o(t) ? t : void 0
                    };
                t.exports = function (t, r) {
                    return arguments.length < 2 ? i(n[t]) : n[t] && n[t][r]
                }
            },
            2344: (t, r, e) => {
                var n = e(392),
                    o = e(8056);
                t.exports = function (t, r) {
                    var e = t[r];
                    return o(e) ? void 0 : n(e)
                }
            },
            7358: function (t, r, e) {
                var n = function (t) {
                    return t && t.Math == Math && t
                };
                t.exports = n("object" == typeof globalThis && globalThis) || n("object" == typeof window &&
                        window) || n("object" == typeof self && self) || n("object" == typeof e.g && e.g) ||
                    function () {
                        return this
                    }() || this || Function("return this")()
            },
            7322: (t, r, e) => {
                var n = e(1890),
                    o = e(7475),
                    i = n({}.hasOwnProperty);
                t.exports = Object.hasOwn || function (t, r) {
                    return i(o(t), r)
                }
            },
            600: t => {
                t.exports = {}
            },
            7021: (t, r, e) => {
                var n = e(9631),
                    o = e(6400),
                    i = e(5354);
                t.exports = !n && !o((function () {
                    return 7 != Object.defineProperty(i("div"), "a", {
                        get: function () {
                            return 7
                        }
                    }).a
                }))
            },
            2985: (t, r, e) => {
                var n = e(1890),
                    o = e(6400),
                    i = e(5173),
                    a = Object,
                    u = n("".split);
                t.exports = o((function () {
                    return !a("z").propertyIsEnumerable(0)
                })) ? function (t) {
                    return "String" == i(t) ? u(t, "") : a(t)
                } : a
            },
            3725: (t, r, e) => {
                var n = e(1890),
                    o = e(419),
                    i = e(1089),
                    a = n(Function.toString);
                o(i.inspectSource) || (i.inspectSource = function (t) {
                    return a(t)
                }), t.exports = i.inspectSource
            },
            7624: (t, r, e) => {
                var n, o, i, a = e(3260),
                    u = e(7358),
                    c = e(776),
                    s = e(1904),
                    p = e(7322),
                    f = e(1089),
                    l = e(203),
                    y = e(600),
                    d = "Object already initialized",
                    h = u.TypeError,
                    v = u.WeakMap,
                    g = function (t) {
                        return i(t) ? o(t) : n(t, {})
                    },
                    b = function (t) {
                        return function (r) {
                            var e;
                            if (!c(r) || (e = o(r)).type !== t) throw h("Incompatible receiver, " + t +
                                " required");
                            return e
                        }
                    };
                if (a || f.state) {
                    var m = f.state || (f.state = new v);
                    m.get = m.get, m.has = m.has, m.set = m.set, n = function (t, r) {
                        if (m.has(t)) throw h(d);
                        return r.facade = t, m.set(t, r), r
                    }, o = function (t) {
                        return m.get(t) || {}
                    }, i = function (t) {
                        return m.has(t)
                    }
                } else {
                    var w = l("state");
                    y[w] = !0, n = function (t, r) {
                        if (p(t, w)) throw h(d);
                        return r.facade = t, s(t, w, r), r
                    }, o = function (t) {
                        return p(t, w) ? t[w] : {}
                    }, i = function (t) {
                        return p(t, w)
                    }
                }
                t.exports = {
                    set: n,
                    get: o,
                    has: i,
                    enforce: g,
                    getterFor: b
                }
            },
            5503: (t, r, e) => {
                var n = e(5976);
                t.exports = function (t) {
                    var r = n(t);
                    return "BigInt64Array" == r || "BigUint64Array" == r
                }
            },
            419: (t, r, e) => {
                var n = e(8465),
                    o = n.all;
                t.exports = n.IS_HTMLDDA ? function (t) {
                    return "function" == typeof t || t === o
                } : function (t) {
                    return "function" == typeof t
                }
            },
            8056: t => {
                t.exports = function (t) {
                    return null === t || void 0 === t
                }
            },
            776: (t, r, e) => {
                var n = e(419),
                    o = e(8465),
                    i = o.all;
                t.exports = o.IS_HTMLDDA ? function (t) {
                    return "object" == typeof t ? null !== t : n(t) || t === i
                } : function (t) {
                    return "object" == typeof t ? null !== t : n(t)
                }
            },
            6692: t => {
                t.exports = !1
            },
            410: (t, r, e) => {
                var n = e(9694),
                    o = e(419),
                    i = e(7673),
                    a = e(8476),
                    u = Object;
                t.exports = a ? function (t) {
                    return "symbol" == typeof t
                } : function (t) {
                    var r = n("Symbol");
                    return o(r) && i(r.prototype, u(t))
                }
            },
            6042: (t, r, e) => {
                var n = e(4068);
                t.exports = function (t) {
                    return n(t.length)
                }
            },
            9843: (t, r, e) => {
                var n = e(1890),
                    o = e(6400),
                    i = e(419),
                    a = e(7322),
                    u = e(9631),
                    c = e(7961).CONFIGURABLE,
                    s = e(3725),
                    p = e(7624),
                    f = p.enforce,
                    l = p.get,
                    y = String,
                    d = Object.defineProperty,
                    h = n("".slice),
                    v = n("".replace),
                    g = n([].join),
                    b = u && !o((function () {
                        return 8 !== d((function () {}), "length", {
                            value: 8
                        }).length
                    })),
                    m = String(String).split("String"),
                    w = t.exports = function (t, r, e) {
                        "Symbol(" === h(y(r), 0, 7) && (r = "[" + v(y(r), /^Symbol\(([^)]*)\)/, "$1") + "]"), e &&
                            e.getter && (r = "get " + r), e && e.setter && (r = "set " + r), (!a(t, "name") ||
                                c && t.name !== r) && (u ? d(t, "name", {
                                value: r,
                                configurable: !0
                            }) : t.name = r), b && e && a(e, "arity") && t.length !== e.arity && d(t, "length", {
                                value: e.arity
                            });
                        try {
                            e && a(e, "constructor") && e.constructor ? u && d(t, "prototype", {
                                writable: !1
                            }) : t.prototype && (t.prototype = void 0)
                        } catch (o) {}
                        var n = f(t);
                        return a(n, "source") || (n.source = g(m, "string" == typeof r ? r : "")), t
                    };
                Function.prototype.toString = w((function () {
                    return i(this) && l(this).source || s(this)
                }), "toString")
            },
            1243: t => {
                var r = Math.ceil,
                    e = Math.floor;
                t.exports = Math.trunc || function (t) {
                    var n = +t;
                    return (n > 0 ? e : r)(n)
                }
            },
            928: (t, r, e) => {
                var n = e(9631),
                    o = e(7021),
                    i = e(5953),
                    a = e(7950),
                    u = e(8618),
                    c = TypeError,
                    s = Object.defineProperty,
                    p = Object.getOwnPropertyDescriptor,
                    f = "enumerable",
                    l = "configurable",
                    y = "writable";
                r.f = n ? i ? function (t, r, e) {
                    if (a(t), r = u(r), a(e), "function" === typeof t && "prototype" === r && "value" in e &&
                        y in e && !e[y]) {
                        var n = p(t, r);
                        n && n[y] && (t[r] = e.value, e = {
                            configurable: l in e ? e[l] : n[l],
                            enumerable: f in e ? e[f] : n[f],
                            writable: !1
                        })
                    }
                    return s(t, r, e)
                } : s : function (t, r, e) {
                    if (a(t), r = u(r), a(e), o) try {
                        return s(t, r, e)
                    } catch (n) {}
                    if ("get" in e || "set" in e) throw c("Accessors not supported");
                    return "value" in e && (t[r] = e.value), t
                }
            },
            4945: (t, r, e) => {
                var n = e(7322),
                    o = e(419),
                    i = e(7475),
                    a = e(203),
                    u = e(123),
                    c = a("IE_PROTO"),
                    s = Object,
                    p = s.prototype;
                t.exports = u ? s.getPrototypeOf : function (t) {
                    var r = i(t);
                    if (n(r, c)) return r[c];
                    var e = r.constructor;
                    return o(e) && r instanceof e ? e.prototype : r instanceof s ? p : null
                }
            },
            7673: (t, r, e) => {
                var n = e(1890);
                t.exports = n({}.isPrototypeOf)
            },
            6184: (t, r, e) => {
                var n = e(743),
                    o = e(7950),
                    i = e(8248);
                t.exports = Object.setPrototypeOf || ("__proto__" in {} ? function () {
                    var t, r = !1,
                        e = {};
                    try {
                        t = n(Object.prototype, "__proto__", "set"), t(e, []), r = e instanceof Array
                    } catch (a) {}
                    return function (e, n) {
                        return o(e), i(n), r ? t(e, n) : e.__proto__ = n, e
                    }
                }() : void 0)
            },
            9308: (t, r, e) => {
                var n = e(3577),
                    o = e(419),
                    i = e(776),
                    a = TypeError;
                t.exports = function (t, r) {
                    var e, u;
                    if ("string" === r && o(e = t.toString) && !i(u = n(e, t))) return u;
                    if (o(e = t.valueOf) && !i(u = n(e, t))) return u;
                    if ("string" !== r && o(e = t.toString) && !i(u = n(e, t))) return u;
                    throw a("Can't convert object to primitive value")
                }
            },
            7933: (t, r, e) => {
                var n = e(8056),
                    o = TypeError;
                t.exports = function (t) {
                    if (n(t)) throw o("Can't call method on " + t);
                    return t
                }
            },
            203: (t, r, e) => {
                var n = e(1586),
                    o = e(6862),
                    i = n("keys");
                t.exports = function (t) {
                    return i[t] || (i[t] = o(t))
                }
            },
            1089: (t, r, e) => {
                var n = e(7358),
                    o = e(1615),
                    i = "__core-js_shared__",
                    a = n[i] || o(i, {});
                t.exports = a
            },
            1586: (t, r, e) => {
                var n = e(6692),
                    o = e(1089);
                (t.exports = function (t, r) {
                    return o[t] || (o[t] = void 0 !== r ? r : {})
                })("versions", []).push({
                    version: "3.31.0",
                    mode: n ? "pure" : "global",
                    copyright: "© 2014-2023 Denis Pushkarev (zloirock.ru)",
                    license: "https://github.com/zloirock/core-js/blob/v3.31.0/LICENSE",
                    source: "https://github.com/zloirock/core-js"
                })
            },
            387: (t, r, e) => {
                var n = e(5068),
                    o = e(6400),
                    i = e(7358),
                    a = i.String;
                t.exports = !!Object.getOwnPropertySymbols && !o((function () {
                    var t = Symbol();
                    return !a(t) || !(Object(t) instanceof Symbol) || !Symbol.sham && n && n < 41
                }))
            },
            5492: (t, r, e) => {
                var n = e(2181),
                    o = TypeError;
                t.exports = function (t) {
                    var r = n(t, "number");
                    if ("number" == typeof r) throw o("Can't convert number to bigint");
                    return BigInt(r)
                }
            },
            1860: (t, r, e) => {
                var n = e(1243);
                t.exports = function (t) {
                    var r = +t;
                    return r !== r || 0 === r ? 0 : n(r)
                }
            },
            4068: (t, r, e) => {
                var n = e(1860),
                    o = Math.min;
                t.exports = function (t) {
                    return t > 0 ? o(n(t), 9007199254740991) : 0
                }
            },
            7475: (t, r, e) => {
                var n = e(7933),
                    o = Object;
                t.exports = function (t) {
                    return o(n(t))
                }
            },
            2181: (t, r, e) => {
                var n = e(3577),
                    o = e(776),
                    i = e(410),
                    a = e(2344),
                    u = e(9308),
                    c = e(854),
                    s = TypeError,
                    p = c("toPrimitive");
                t.exports = function (t, r) {
                    if (!o(t) || i(t)) return t;
                    var e, c = a(t, p);
                    if (c) {
                        if (void 0 === r && (r = "default"), e = n(c, t, r), !o(e) || i(e)) return e;
                        throw s("Can't convert object to primitive value")
                    }
                    return void 0 === r && (r = "number"), u(t, r)
                }
            },
            8618: (t, r, e) => {
                var n = e(2181),
                    o = e(410);
                t.exports = function (t) {
                    var r = n(t, "string");
                    return o(r) ? r : r + ""
                }
            },
            5705: (t, r, e) => {
                var n = e(854),
                    o = n("toStringTag"),
                    i = {};
                i[o] = "z", t.exports = "[object z]" === String(i)
            },
            3353: t => {
                var r = String;
                t.exports = function (t) {
                    try {
                        return r(t)
                    } catch (e) {
                        return "Object"
                    }
                }
            },
            6862: (t, r, e) => {
                var n = e(1890),
                    o = 0,
                    i = Math.random(),
                    a = n(1..toString);
                t.exports = function (t) {
                    return "Symbol(" + (void 0 === t ? "" : t) + ")_" + a(++o + i, 36)
                }
            },
            8476: (t, r, e) => {
                var n = e(387);
                t.exports = n && !Symbol.sham && "symbol" == typeof Symbol.iterator
            },
            5953: (t, r, e) => {
                var n = e(9631),
                    o = e(6400);
                t.exports = n && o((function () {
                    return 42 != Object.defineProperty((function () {}), "prototype", {
                        value: 42,
                        writable: !1
                    }).prototype
                }))
            },
            3260: (t, r, e) => {
                var n = e(7358),
                    o = e(419),
                    i = n.WeakMap;
                t.exports = o(i) && /native code/.test(String(i))
            },
            854: (t, r, e) => {
                var n = e(7358),
                    o = e(1586),
                    i = e(7322),
                    a = e(6862),
                    u = e(387),
                    c = e(8476),
                    s = n.Symbol,
                    p = o("wks"),
                    f = c ? s["for"] || s : s && s.withoutSetter || a;
                t.exports = function (t) {
                    return i(p, t) || (p[t] = u && i(s, t) ? s[t] : f("Symbol." + t)), p[t]
                }
            },
            5123: (t, r, e) => {
                "use strict";
                var n = e(683),
                    o = e(6042),
                    i = e(1860),
                    a = n.aTypedArray,
                    u = n.exportTypedArrayMethod;
                u("at", (function (t) {
                    var r = a(this),
                        e = o(r),
                        n = i(t),
                        u = n >= 0 ? n : e + n;
                    return u < 0 || u >= e ? void 0 : r[u]
                }))
            },
            7648: (t, r, e) => {
                "use strict";
                var n = e(683),
                    o = e(5416).findLastIndex,
                    i = n.aTypedArray,
                    a = n.exportTypedArrayMethod;
                a("findLastIndex", (function (t) {
                    return o(i(this), t, arguments.length > 1 ? arguments[1] : void 0)
                }))
            },
            4331: (t, r, e) => {
                "use strict";
                var n = e(683),
                    o = e(5416).findLast,
                    i = n.aTypedArray,
                    a = n.exportTypedArrayMethod;
                a("findLast", (function (t) {
                    return o(i(this), t, arguments.length > 1 ? arguments[1] : void 0)
                }))
            },
            3556: (t, r, e) => {
                "use strict";
                var n = e(3180),
                    o = e(683),
                    i = o.aTypedArray,
                    a = o.exportTypedArrayMethod,
                    u = o.getTypedArrayConstructor;
                a("toReversed", (function () {
                    return n(i(this), u(this))
                }))
            },
            3261: (t, r, e) => {
                "use strict";
                var n = e(683),
                    o = e(1890),
                    i = e(392),
                    a = e(6008),
                    u = n.aTypedArray,
                    c = n.getTypedArrayConstructor,
                    s = n.exportTypedArrayMethod,
                    p = o(n.TypedArrayPrototype.sort);
                s("toSorted", (function (t) {
                    void 0 !== t && i(t);
                    var r = u(this),
                        e = a(c(r), r);
                    return p(e, t)
                }))
            },
            7824: (t, r, e) => {
                "use strict";
                var n = e(3663),
                    o = e(683),
                    i = e(5503),
                    a = e(1860),
                    u = e(5492),
                    c = o.aTypedArray,
                    s = o.getTypedArrayConstructor,
                    p = o.exportTypedArrayMethod,
                    f = !! function () {
                        try {
                            new Int8Array(1)["with"](2, {
                                valueOf: function () {
                                    throw 8
                                }
                            })
                        } catch (t) {
                            return 8 === t
                        }
                    }();
                p("with", {
                    with: function (t, r) {
                        var e = c(this),
                            o = a(t),
                            p = i(e) ? u(r) : +r;
                        return n(e, s(e), o, p)
                    }
                } ["with"], !f)
            },
            4067: (t, r, e) => {
                "use strict";
                var n = e(9631),
                    o = e(1890),
                    i = e(9468),
                    a = URLSearchParams.prototype,
                    u = o(a.forEach);
                n && !("size" in a) && i(a, "size", {
                    get: function () {
                        var t = 0;
                        return u(this, (function () {
                            t++
                        })), t
                    },
                    configurable: !0,
                    enumerable: !0
                })
            }
        },
        r = {};

    function e(n) {
        var o = r[n];
        if (void 0 !== o) return o.exports;
        var i = r[n] = {
            exports: {}
        };
        return t[n].call(i.exports, i, i.exports, e), i.exports
    }(() => {
        e.g = function () {
            if ("object" === typeof globalThis) return globalThis;
            try {
                return this || new Function("return this")()
            } catch (t) {
                if ("object" === typeof window) return window
            }
        }()
    })(), (() => {
        e.p = "/"
    })();
    (() => {
        "use strict";
        e(5123), e(4331), e(7648), e(3556), e(3261), e(7824), e(4067);
        const t = e.p + "js/matting/one.wasm",
            r = e.p + "js/matting/two.wasm",
            n = e.p + "js/matting/three.wasm",
            o = e.p + "js/matting/four.wasm",
            i = e.p + "js/matting/one.js",
            a = e.p + "js/matting/two.js",
            u = e.p + "js/matting/three.js",
            c = WebAssembly.validate(new Uint8Array([0, 97, 115, 109, 1, 0, 0, 0, 1, 5, 1, 96, 0, 1, 123, 3, 2,
                1, 0, 10, 10, 1, 8, 0, 65, 0, 253, 15, 253, 98, 11]));
        let s = !1;
        try {
            s = crossOriginIsolated
        } catch (I) {}
        const p = WebAssembly.validate(new Uint8Array([0, 97, 115, 109, 1, 0, 0, 0, 1, 4, 1, 96, 0, 0, 3, 2, 1,
            0, 5, 4, 1, 3, 1, 1, 10, 11, 1, 9, 0, 65, 0, 254, 16, 2, 0, 26, 11])) && s;

        function f(t) {
            return t
        }
        const l = f(t),
            y = f(r),
            d = f(n),
            h = f(o),
            v = i,
            g = a,
            b = u;
        let m = g;

        function w(t) {
            return new Promise(((r, e) => {
                var n = new XMLHttpRequest;
                n.open("GET", t, !0), n.responseType = "blob", n.onload = function (t) {
                    if (200 == this.status) {
                        var n = this.response;
                        r(URL.createObjectURL(n))
                    } else e()
                }, n.onerror = function () {
                    e()
                }, n.send()
            }))
        }
        var x = {};
        x["WasmFileUrl"] = c && p ? d : c ? y : p ? h : l;
        for (let e in x) x[e] = x[e].substring(x[e].lastIndexOf("/") + 1);
        p && (m = v, Promise.all([w(m), w(b)]).then((t => {
            x["WasmMainScript"] = t[0], x["WasmThreadsWorker"] = t[1], location.bgsubModule = x,
                importScripts(t[0])
        }))), p || (location.bgsubModule = x, importScripts(m));
        var A = {},
            T = {};
        let S = -1;

        function _(t) {
            t > S && (self.postMessage({
                cmd: "progressCB",
                val: t,
                maxVal: 1657
            }), S = t)
        }
        let j;

        function O(t, r) {
            let e = new Uint8Array(t),
                n = x.__malloc(e.length);
            x.HEAPU8.set(e, n), r.setParams(n, e.length), x.__free(n)
        }

        function E(t) {
            self.postMessage({
                cmd: "error",
                msg: t.msg,
                code: t.code
            })
        }

        function P(t) {
            let r = new Uint8Array(t.imgData),
                e = x.__malloc(r.length);
            x.HEAPU8.set(r, e);
            let n = new Uint8Array(t.maskImgData),
                o = x.__malloc(n.length);
            x.HEAPU8.set(n, o);
            Number(new Date);
            x._EgdeEnhance(e, o, e, t.width, t.height);
            Number(new Date);
            let i = x.HEAPU8.subarray(e, e + t.width * t.height * 4).slice(0).buffer,
                a = new Uint8Array(i);
            for (let u = 0; u < t.width * t.height * 4; u++) r[u] = a[u];
            x.__free(e), x.__free(o), self.postMessage({
                cmd: "edgeEnhanceFinish",
                width: t.width,
                height: t.height,
                data: t.imgData
            }, [t.imgData])
        }

        function U(t) {
            O(t.params, T);
            let r = new Uint8Array(t.imgData),
                e = x.__malloc(r.length);
            x.HEAPU8.set(r, e);
            let n = new Uint8Array(t.maskImgData),
                o = x.__malloc(n.length);
            x.HEAPU8.set(n, o);
            Number(new Date);
            T.predict(e, o, e, t.width, t.height);
            Number(new Date);
            let i = x.HEAPU8.subarray(e, e + t.width * t.height * 4).slice(0).buffer,
                a = new Uint8Array(i);
            for (let u = 0; u < t.width * t.height * 4; u++) r[u] = a[u];
            x.__free(e), x.__free(o), self.postMessage({
                cmd: "predictHarmFinish",
                width: t.width,
                height: t.height,
                data: t.imgData
            }, [t.imgData])
        }

        function M(t) {
            let r = t;
            O(r.params, A), A.setProgressCallback(j), S = -1;
            let e = new Uint8Array(r.data),
                n = x.__malloc(e.length);
            x.HEAPU8.set(e, n);
            Number(new Date);
            r.width * r.height <= 3e5 && (t.Authorization = void 0), self.postMessage({
                cmd: "progressCB",
                val: 0,
                maxVal: 1
            });
            const o = A.predict(n, n, r.width, r.height);
            Number(new Date);
            let i = x.HEAPU8.subarray(n, n + o.width * o.height * 4).slice(0).buffer,
                a = new Uint8Array(i);
            for (let u = 0; u < o.width * o.height * 4; u++) e[u] = a[u];
            self.postMessage({
                cmd: "predictFinish",
                width: o.width,
                height: o.height,
                data: r.data,
                useSimd: c,
                useThreads: p
            }, [r.data])
        }
        x.onRuntimeInitialized = function () {
            A = new x.Rmbg, T = new x.SSAM, j = x.addFunction(_, "vi"), self.postMessage({
                cmd: "Init",
                useSimd: c,
                useThreads: p
            })
        }, addEventListener("message", (function (t) {
            let r = t.data,
                e = {
                    predict: M,
                    predictHarm: U,
                    edgeEnhance: P
                };
            try {
                e[r.cmd] && e[r.cmd](r)
            } catch (I) {
                if (console.log("data.cmd:", r.cmd, "error"), !(I.message.indexOf(
                        "Cannot enlarge memory") >= 0)) throw I;
                E({
                    msg: "The image is too large, please adjust the image size",
                    code: -1
                })
            }
        }), !1)
    })()
})();