var wasmModule, ba, f = "undefined" !== typeof Module ? Module : location.bgsubModule ? location.bgsubModule : {},
    aa = {};
for (ba in f) f.hasOwnProperty(ba) && (aa[ba] = f[ba]);
var ca = "./this.program";

function da(e, n) {
    throw n
}
var ea = "object" === typeof window,
    fa = "function" === typeof importScripts,
    p = "object" === typeof process && "object" === typeof process.versions && "string" === typeof process.versions.node,
    q = f.ENVIRONMENT_IS_PTHREAD || !1,
    _scriptDir = "undefined" !== typeof document && document.currentScript ? document.currentScript.src : void 0;
fa ? _scriptDir = self.location.href : p && (_scriptDir = __filename);
var ia, ja, ma, z, A, u = "";

function ha(e) {
    return f.locateFile ? f.locateFile(e, u) : u + e
}
if (p) {
    var pa;
    u = fa ? require("path").dirname(u) + "/" : __dirname + "/", ia = function (e, n) {
            return z || (z = require("fs")), A || (A = require("path")), e = A.normalize(e), z.readFileSync(e, n ? null :
                "utf8")
        }, ma = function (e) {
            return e = ia(e, !0), e.buffer || (e = new Uint8Array(e)), assert(e.buffer), e
        }, ja = function (e, n, t) {
            z || (z = require("fs")), A || (A = require("path")), e = A.normalize(e), z.readFile(e, (function (e, r) {
                e ? t(e) : n(r.buffer)
            }))
        }, 1 < process.argv.length && (ca = process.argv[1].replace(/\\/g, "/")), process.argv.slice(2), "undefined" !==
        typeof module && (module.exports = f), process.on("uncaughtException", (function (e) {
            if (!(e instanceof na)) throw e
        })), process.on("unhandledRejection", B), da = function (e, n) {
            if (oa()) throw process.exitCode = e, n;
            process.exit(e)
        }, f.inspect = function () {
            return "[Emscripten Module object]"
        };
    try {
        pa = require("worker_threads")
    } catch (a) {
        throw console.error(
            'The "worker_threads" module is not supported in this node.js build - perhaps a newer version is needed?'
        ), a
    }
    global.Worker = pa.Worker
} else(ea || fa) && (fa ? u = self.location.href : "undefined" !== typeof document && document.currentScript && (u =
    document.currentScript.src), u = 0 !== u.indexOf("blob:") ? u.substr(0, u.lastIndexOf("/") + 1) : "", p ? (
    ia = function (e, n) {
        return z || (z = require("fs")), A || (A = require("path")), e = A.normalize(e), z.readFileSync(e, n ?
            null : "utf8")
    }, ma = function (e) {
        return e = ia(e, !0), e.buffer || (e = new Uint8Array(e)), assert(e.buffer), e
    }, ja = function (e, n, t) {
        z || (z = require("fs")), A || (A = require("path")), e = A.normalize(e), z.readFile(e, (function (e, r) {
            e ? t(e) : n(r.buffer)
        }))
    }) : (ia = function (e) {
    var n = new XMLHttpRequest;
    return n.open("GET", e, !1), n.send(null), n.responseText
}, fa && (ma = function (e) {
    var n = new XMLHttpRequest;
    return n.open("GET", e, !1), n.responseType = "arraybuffer", n.send(null), new Uint8Array(n.response)
}), ja = function (e, n, t) {
    var r = new XMLHttpRequest;
    r.open("GET", e, !0), r.responseType = "arraybuffer", r.onload = function () {
        200 == r.status || 0 == r.status && r.response ? n(r.response) : t()
    }, r.onerror = t, r.send(null)
}));
p && "undefined" === typeof performance && (global.performance = require("perf_hooks").performance);
var qa = f.print || console.log.bind(console),
    C = f.printErr || console.warn.bind(console);
for (ba in aa) aa.hasOwnProperty(ba) && (f[ba] = aa[ba]);
aa = null, f.thisProgram && (ca = f.thisProgram), f.quit && (da = f.quit);
var ra, ta, ua, sa = [];
f.wasmBinary && (ua = f.wasmBinary);
var noExitRuntime = f.noExitRuntime || !0;
"object" !== typeof WebAssembly && B("no native wasm support detected");
var E, xa, ya = !1;

function assert(e, n) {
    e || B("Assertion failed: " + n)
}

function za(e) {
    var n = new TextDecoder(e);
    this.decode = function (e) {
        return e.buffer instanceof SharedArrayBuffer && (e = new Uint8Array(e)), n.decode.call(n, e)
    }
}
var Aa = "undefined" !== typeof TextDecoder ? new za("utf8") : void 0;

function Ba(e, n, t) {
    var r = n + t;
    for (t = n; e[t] && !(t >= r);) ++t;
    if (16 < t - n && e.subarray && Aa) return Aa.decode(e.subarray(n, t));
    for (r = ""; n < t;) {
        var a = e[n++];
        if (128 & a) {
            var i = 63 & e[n++];
            if (192 == (224 & a)) r += String.fromCharCode((31 & a) << 6 | i);
            else {
                var o = 63 & e[n++];
                a = 224 == (240 & a) ? (15 & a) << 12 | i << 6 | o : (7 & a) << 18 | i << 12 | o << 6 | 63 & e[n++],
                    65536 > a ? r += String.fromCharCode(a) : (a -= 65536, r += String.fromCharCode(55296 | a >> 10,
                        56320 | 1023 & a))
            }
        } else r += String.fromCharCode(a)
    }
    return r
}

function Ca(e, n) {
    return e ? Ba(F, e, n) : ""
}

function Da(e, n, t, r) {
    if (!(0 < r)) return 0;
    var a = t;
    r = t + r - 1;
    for (var i = 0; i < e.length; ++i) {
        var o = e.charCodeAt(i);
        if (55296 <= o && 57343 >= o) {
            var u = e.charCodeAt(++i);
            o = 65536 + ((1023 & o) << 10) | 1023 & u
        }
        if (127 >= o) {
            if (t >= r) break;
            n[t++] = o
        } else {
            if (2047 >= o) {
                if (t + 1 >= r) break;
                n[t++] = 192 | o >> 6
            } else {
                if (65535 >= o) {
                    if (t + 2 >= r) break;
                    n[t++] = 224 | o >> 12
                } else {
                    if (t + 3 >= r) break;
                    n[t++] = 240 | o >> 18, n[t++] = 128 | o >> 12 & 63
                }
                n[t++] = 128 | o >> 6 & 63
            }
            n[t++] = 128 | 63 & o
        }
    }
    return n[t] = 0, t - a
}

function Ea(e) {
    for (var n = 0, t = 0; t < e.length; ++t) {
        var r = e.charCodeAt(t);
        55296 <= r && 57343 >= r && (r = 65536 + ((1023 & r) << 10) | 1023 & e.charCodeAt(++t)), 127 >= r ? ++n : n =
            2047 >= r ? n + 2 : 65535 >= r ? n + 3 : n + 4
    }
    return n
}
var Oa, H, F, Ia, Ha, G, J, Pa, Qa, Fa = "undefined" !== typeof TextDecoder ? new za("utf-16le") : void 0;

function Ga(e, n) {
    for (var t = e >> 1, r = t + n / 2; !(t >= r) && Ha[t];) ++t;
    if (t <<= 1, 32 < t - e && Fa) return Fa.decode(F.subarray(e, t));
    for (t = "", r = 0; !(r >= n / 2); ++r) {
        var a = Ia[e + 2 * r >> 1];
        if (0 == a) break;
        t += String.fromCharCode(a)
    }
    return t
}

function Ja(e, n, t) {
    if (void 0 === t && (t = 2147483647), 2 > t) return 0;
    t -= 2;
    var r = n;
    t = t < 2 * e.length ? t / 2 : e.length;
    for (var a = 0; a < t; ++a) Ia[n >> 1] = e.charCodeAt(a), n += 2;
    return Ia[n >> 1] = 0, n - r
}

function Ka(e) {
    return 2 * e.length
}

function La(e, n) {
    for (var t = 0, r = ""; !(t >= n / 4);) {
        var a = G[e + 4 * t >> 2];
        if (0 == a) break;
        ++t, 65536 <= a ? (a -= 65536, r += String.fromCharCode(55296 | a >> 10, 56320 | 1023 & a)) : r += String.fromCharCode(
            a)
    }
    return r
}

function Ma(e, n, t) {
    if (void 0 === t && (t = 2147483647), 4 > t) return 0;
    var r = n;
    t = r + t - 4;
    for (var a = 0; a < e.length; ++a) {
        var i = e.charCodeAt(a);
        if (55296 <= i && 57343 >= i) {
            var o = e.charCodeAt(++a);
            i = 65536 + ((1023 & i) << 10) | 1023 & o
        }
        if (G[n >> 2] = i, n += 4, n + 4 > t) break
    }
    return G[n >> 2] = 0, n - r
}

function Na(e) {
    for (var n = 0, t = 0; t < e.length; ++t) {
        var r = e.charCodeAt(t);
        55296 <= r && 57343 >= r && ++t, n += 4
    }
    return n
}
q && (Oa = f.buffer);
var Ra = f.INITIAL_MEMORY || 671088640;
if (q) E = f.wasmMemory, Oa = f.buffer;
else if (f.wasmMemory) E = f.wasmMemory;
else if (E = new WebAssembly.Memory({
        initial: Ra / 65536,
        maximum: Ra / 65536,
        shared: !0
    }), !(E.buffer instanceof SharedArrayBuffer)) throw C(
    "requested a shared WebAssembly.Memory but the returned buffer is not a SharedArrayBuffer, indicating that while the browser has SharedArrayBuffer it does not have WebAssembly threads support - you may need to set a flag"
), p && console.log(
    "(on node you may need: --experimental-wasm-threads --experimental-wasm-bulk-memory and also use a recent version)"
), Error("bad memory");
E && (Oa = E.buffer), Ra = Oa.byteLength;
var K = Oa;
Oa = K, f.HEAP8 = H = new Int8Array(K), f.HEAP16 = Ia = new Int16Array(K), f.HEAP32 = G = new Int32Array(K), f.HEAPU8 =
    F = new Uint8Array(K), f.HEAPU16 = Ha = new Uint16Array(K), f.HEAPU32 = J = new Uint32Array(K), f.HEAPF32 = Pa =
    new Float32Array(K), f.HEAPF64 = Qa = new Float64Array(K);
var L, Sa = [],
    Ta = [],
    Ua = [],
    Va = 0;

function oa() {
    return noExitRuntime || 0 < Va
}

function Wa() {
    q || (f.noFSInit || Xa || (Xa = !0, Ya(), f.stdin = f.stdin, f.stdout = f.stdout, f.stderr = f.stderr, f.stdin ? Za(
            "stdin", f.stdin) : $a("/dev/tty", "/dev/stdin"), f.stdout ? Za("stdout", null, f.stdout) : $a(
            "/dev/tty", "/dev/stdout"), f.stderr ? Za("stderr", null, f.stderr) : $a("/dev/tty1", "/dev/stderr"),
        ab("/dev/stdin", 0), ab("/dev/stdout", 1), ab("/dev/stderr", 1)), bb = !1, cb(Ta))
}

function db() {
    var e = f.preRun.shift();
    Sa.unshift(e)
}
var M, eb = 0,
    fb = null,
    gb = null;

function B(e) {
    throw f.onAbort && f.onAbort(e), q && console.error("Pthread aborting at " + Error().stack), C(e), ya = !0, new WebAssembly
        .RuntimeError("abort(" + e + "). Build with -s ASSERTIONS=1 for more info.")
}

function hb() {
    return M.startsWith("data:application/octet-stream;base64,")
}

function ib() {
    var e = M;
    try {
        if (e == M && ua) return new Uint8Array(ua);
        if (ma) return ma(e);
        throw "both async and sync fetching of the wasm failed"
    } catch (n) {
        B(n)
    }
}

function jb() {
    if (!ua && (ea || fa)) {
        if ("function" === typeof fetch && !M.startsWith("file://")) return fetch(M, {
            credentials: "same-origin"
        }).then((function (e) {
            if (!e.ok) throw "failed to load wasm binary file at '" + M + "'";
            return e.arrayBuffer()
        })).catch((function () {
            return ib()
        }));
        if (ja) return new Promise((function (e, n) {
            ja(M, (function (n) {
                e(new Uint8Array(n))
            }), n)
        }))
    }
    return Promise.resolve().then((function () {
        return ib()
    }))
}
f.preloadedImages = {}, f.preloadedAudios = {}, M = f.WasmFileUrl, hb() || (M = ha(M));
var kb, lb, nb = {
    54532: function () {
        throw "Canceled!"
    },
    54550: function (e, n) {
        setTimeout((function () {
            mb(e, n)
        }), 0)
    }
};

function cb(e) {
    for (; 0 < e.length;) {
        var n = e.shift();
        if ("function" == typeof n) n(f);
        else {
            var t = n.hd;
            "number" === typeof t ? void 0 === n.hb ? L.get(t)() : L.get(t)(n.hb) : t(void 0 === n.hb ? null : n.hb)
        }
    }
}

function ob(e, n) {
    if (0 >= e || e > H.length || 1 & e || 0 > n) return -28;
    if (0 == n) return 0;
    2147483647 <= n && (n = 1 / 0);
    var t = Atomics.load(G, pb >> 2),
        r = 0;
    if (t == e && Atomics.compareExchange(G, pb >> 2, t, 0) == t && (--n, r = 1, 0 >= n)) return 1;
    if (e = Atomics.notify(G, e >> 2, n), 0 <= e) return e + r;
    throw "Atomics.notify returned an unexpected value " + e
}
f._emscripten_futex_wake = ob;
var Ab, N = {
    Ta: [],
    Ya: [],
    Kb: [],
    qc: function () {
        for (var e = 0; 4 > e; ++e) N.Mb()
    },
    rc: function () {
        for (var e = O(228), n = 0; 57 > n; ++n) J[e / 4 + n] = 0;
        G[e + 12 >> 2] = e, n = e + 152, G[n >> 2] = n;
        var t = O(512);
        for (n = 0; 128 > n; ++n) J[t / 4 + n] = 0;
        Atomics.store(J, e + 100 >> 2, t), Atomics.store(J, e + 40 >> 2, e), qb(e, !fa, 1), rb(e)
    },
    sc: function () {
        N.receiveObjectTransfer = N.Cc, N.threadInit = N.Wc, N.threadCancel = N.Uc, N.threadExit = N.Vc, N.setExitStatus =
            N.Ic
    },
    Qa: {},
    Jb: [],
    Fc: function () {
        for (; 0 < N.Jb.length;) N.Jb.pop()();
        sb()
    },
    $b: function (e, n) {
        Atomics.store(J, e + 56 >> 2, 1), Atomics.store(J, e + 60 >> 2, 0), N.Fc(), Atomics.store(J, e + 4 >> 2,
            n), Atomics.store(J, e + 0 >> 2, 1), ob(e + 0, 2147483647), qb(0, 0, 0)
    },
    Ic: function () {},
    Vc: function (e) {
        var n = tb();
        n && (N.$b(n, e), q && postMessage({
            cmd: "exit"
        }))
    },
    Uc: function () {
        N.$b(tb(), -1), postMessage({
            cmd: "cancelDone"
        })
    },
    Tc: function () {
        for (var e in N.Qa) {
            var n = N.Qa[e];
            n && n.worker && N.xb(n.worker)
        }
        for (N.Qa = {}, e = 0; e < N.Ta.length; ++e) {
            var t = N.Ta[e];
            t.terminate()
        }
        for (N.Ta = [], e = 0; e < N.Ya.length; ++e) t = N.Ya[e], n = t.Ka, N.Db(n), t.terminate();
        N.Ya = []
    },
    Db: function (e) {
        if (e) {
            if (e.Sa) {
                var n = G[e.Sa + 100 >> 2];
                G[e.Sa + 100 >> 2] = 0, P(n), P(e.Sa)
            }
            e.Sa = 0, e.Cb && e.ab && P(e.ab), e.ab = 0, e.worker && (e.worker.Ka = null)
        }
    },
    xb: function (e) {
        N.Gc((function () {
            delete N.Qa[e.Ka.Sa], N.Ta.push(e), N.Ya.splice(N.Ya.indexOf(e), 1), N.Db(e.Ka), e.Ka =
                void 0
        }))
    },
    Gc: function (e) {
        G[ub >> 2] = 0;
        try {
            e()
        } finally {
            G[ub >> 2] = 1
        }
    },
    Cc: function () {},
    Wc: function () {
        for (var e in N.Kb) N.Kb[e]()
    },
    Wb: function (e, n) {
        e.onmessage = function (t) {
            var r = t.data,
                a = r.cmd;
            if (e.Ka && (N.cc = e.Ka.Sa), r.targetThread && r.targetThread != tb()) {
                var i = N.Qa[r.rd];
                i ? i.worker.postMessage(t.data, r.transferList) : console.error(
                    'Internal error! Worker sent a message "' + a + '" to target pthread ' + r.targetThread +
                    ", but that thread no longer exists!")
            } else if ("processQueuedMainThreadWork" === a) vb();
            else if ("spawnThread" === a) wb(t.data);
            else if ("cleanupThread" === a) {
                if (t = r.thread, q) throw "Internal Error! cleanupThread() can only ever be called from main application thread!";
                if (!t) throw "Internal Error! Null pthread_ptr in cleanupThread!";
                (r = N.Qa[t]) && (G[t + 12 >> 2] = 0, N.xb(r.worker))
            } else if ("killThread" === a) {
                if (t = r.thread, q) throw "Internal Error! killThread() can only ever be called from main application thread!";
                if (!t) throw "Internal Error! Null pthread_ptr in killThread!";
                G[t + 12 >> 2] = 0, t = N.Qa[t], t.worker.terminate(), N.Db(t), N.Ya.splice(N.Ya.indexOf(t.worker),
                    1), t.worker.Ka = void 0
            } else if ("cancelThread" === a) {
                if (t = r.thread, q) throw "Internal Error! cancelThread() can only ever be called from main application thread!";
                if (!t) throw "Internal Error! Null pthread_ptr in cancelThread!";
                N.Qa[t].worker.postMessage({
                    cmd: "cancel"
                })
            } else if ("loaded" === a) e.loaded = !0, n && n(e), e.jb && (e.jb(), delete e.jb);
            else if ("print" === a) qa("Thread " + r.threadId + ": " + r.text);
            else if ("printErr" === a) C("Thread " + r.threadId + ": " + r.text);
            else if ("alert" === a) alert("Thread " + r.threadId + ": " + r.text);
            else if ("exit" === a) e.Ka && Atomics.load(J, e.Ka.Sa + 64 >> 2) && N.xb(e);
            else if ("exitProcess" === a) try {
                xb(r.returnCode)
            } catch (o) {
                if (o instanceof na) return;
                throw o
            } else "cancelDone" === a ? N.xb(e) : "objectTransfer" !== a && ("setimmediate" === t.data.target ?
                e.postMessage(t.data) : C("worker sent an unknown command " + a));
            N.cc = void 0
        }, e.onerror = function (e) {
            C("pthread sent an error! " + e.filename + ":" + e.lineno + ": " + e.message)
        }, p && (e.on("message", (function (n) {
            e.onmessage({
                data: n
            })
        })), e.on("error", (function (n) {
            e.onerror(n)
        })), e.on("exit", (function () {}))), e.postMessage({
            cmd: "load",
            urlOrBlob: f.WasmMainScript || _scriptDir,
            wasmMemory: E,
            WasmFileUrl: f.WasmFileUrl,
            wasmModule
        })
    },
    Mb: function () {
        var e = f.WasmThreadsWorker;
        N.Ta.push(new Worker(e))
    },
    ic: function () {
        return 0 == N.Ta.length && (N.Mb(), N.Wb(N.Ta[0])), N.Ta.pop()
    },
    cd: function (e) {
        for (e = performance.now() + e; performance.now() < e;);
    }
};

function Bb(e) {
    this.Ea = e - 16, this.Nc = function (e) {
        G[this.Ea + 4 >> 2] = e
    }, this.Kc = function (e) {
        G[this.Ea + 8 >> 2] = e
    }, this.Lc = function () {
        G[this.Ea >> 2] = 0
    }, this.Jc = function () {
        H[this.Ea + 12 >> 0] = 0
    }, this.Mc = function () {
        H[this.Ea + 13 >> 0] = 0
    }, this.oc = function (e, n) {
        this.Nc(e), this.Kc(n), this.Lc(), this.Jc(), this.Mc()
    }
}
f.establishStackSpace = function (e, n) {
    yb(e, n), zb(e)
}, f.invokeEntryPoint = function (e, n) {
    return L.get(e)(n)
}, Ab = p ? function () {
    var e = process.hrtime();
    return 1e3 * e[0] + e[1] / 1e6
} : q ? function () {
    return performance.now() - f.__performance_now_clock_drift
} : function () {
    return performance.now()
};
var Cb = 0;

function Db(e, n) {
    for (var t = 0, r = e.length - 1; 0 <= r; r--) {
        var a = e[r];
        "." === a ? e.splice(r, 1) : ".." === a ? (e.splice(r, 1), t++) : t && (e.splice(r, 1), t--)
    }
    if (n)
        for (; t; t--) e.unshift("..");
    return e
}

function Eb(e) {
    var n = "/" === e.charAt(0),
        t = "/" === e.substr(-1);
    return (e = Db(e.split("/").filter((function (e) {
        return !!e
    })), !n).join("/")) || n || (e = "."), e && t && (e += "/"), (n ? "/" : "") + e
}

function Fb(e) {
    var n = /^(\/?|)([\s\S]*?)((?:\.{1,2}|[^\/]+?|)(\.[^.\/]*|))(?:[\/]*)$/.exec(e).slice(1);
    return e = n[0], n = n[1], e || n ? (n && (n = n.substr(0, n.length - 1)), e + n) : "."
}

function Gb(e) {
    if ("/" === e) return "/";
    e = Eb(e), e = e.replace(/\/$/, "");
    var n = e.lastIndexOf("/");
    return -1 === n ? e : e.substr(n + 1)
}

function Hb() {
    if ("object" === typeof crypto && "function" === typeof crypto.getRandomValues) {
        var e = new Uint8Array(1);
        return function () {
            return crypto.getRandomValues(e), e[0]
        }
    }
    if (p) try {
        var n = require("crypto");
        return function () {
            return n.randomBytes(1)[0]
        }
    } catch (t) {}
    return function () {
        B("randomDevice")
    }
}

function Ib() {
    for (var e = "", n = !1, t = arguments.length - 1; - 1 <= t && !n; t--) {
        if (n = 0 <= t ? arguments[t] : "/", "string" !== typeof n) throw new TypeError(
            "Arguments to path.resolve must be strings");
        if (!n) return "";
        e = n + "/" + e, n = "/" === n.charAt(0)
    }
    return e = Db(e.split("/").filter((function (e) {
        return !!e
    })), !n).join("/"), (n ? "/" : "") + e || "."
}
var Jb = [];

function Kb(e, n) {
    Jb[e] = {
        input: [],
        output: [],
        eb: n
    }, Lb(e, Mb)
}
var Mb = {
        open: function (e) {
            var n = Jb[e.node.rdev];
            if (!n) throw new Q(43);
            e.tty = n, e.seekable = !1
        },
        close: function (e) {
            e.tty.eb.flush(e.tty)
        },
        flush: function (e) {
            e.tty.eb.flush(e.tty)
        },
        read: function (e, n, t, r) {
            if (!e.tty || !e.tty.eb.Vb) throw new Q(60);
            for (var a = 0, i = 0; i < r; i++) {
                try {
                    var o = e.tty.eb.Vb(e.tty)
                } catch (u) {
                    throw new Q(29)
                }
                if (void 0 === o && 0 === a) throw new Q(6);
                if (null === o || void 0 === o) break;
                a++, n[t + i] = o
            }
            return a && (e.node.timestamp = Date.now()), a
        },
        write: function (e, n, t, r) {
            if (!e.tty || !e.tty.eb.Gb) throw new Q(60);
            try {
                for (var a = 0; a < r; a++) e.tty.eb.Gb(e.tty, n[t + a])
            } catch (i) {
                throw new Q(29)
            }
            return r && (e.node.timestamp = Date.now()), a
        }
    },
    Ob = {
        Vb: function (e) {
            if (!e.input.length) {
                var n = null;
                if (p) {
                    var t = Buffer.alloc(256),
                        r = 0;
                    try {
                        r = z.readSync(process.stdin.fd, t, 0, 256, null)
                    } catch (a) {
                        if (!a.toString().includes("EOF")) throw a;
                        r = 0
                    }
                    n = 0 < r ? t.slice(0, r).toString("utf-8") : null
                } else "undefined" != typeof window && "function" == typeof window.prompt ? (n = window.prompt(
                    "Input: "), null !== n && (n += "\n")) : "function" == typeof readline && (n = readline(),
                    null !== n && (n += "\n"));
                if (!n) return null;
                e.input = Nb(n, !0)
            }
            return e.input.shift()
        },
        Gb: function (e, n) {
            null === n || 10 === n ? (qa(Ba(e.output, 0)), e.output = []) : 0 != n && e.output.push(n)
        },
        flush: function (e) {
            e.output && 0 < e.output.length && (qa(Ba(e.output, 0)), e.output = [])
        }
    },
    Pb = {
        Gb: function (e, n) {
            null === n || 10 === n ? (C(Ba(e.output, 0)), e.output = []) : 0 != n && e.output.push(n)
        },
        flush: function (e) {
            e.output && 0 < e.output.length && (C(Ba(e.output, 0)), e.output = [])
        }
    };

function Qb(e) {
    e = 65536 * Math.ceil(e / 65536);
    var n = Rb(65536, e);
    return n ? (F.fill(0, n, n + e), n) : 0
}
var R = {
        Oa: null,
        Ua: function () {
            return R.createNode(null, "/", 16895, 0)
        },
        createNode: function (e, n, t, r) {
            if (24576 === (61440 & t) || 4096 === (61440 & t)) throw new Q(63);
            return R.Oa || (R.Oa = {
                    dir: {
                        node: {
                            Xa: R.Da.Xa,
                            Ra: R.Da.Ra,
                            lookup: R.Da.lookup,
                            sb: R.Da.sb,
                            rename: R.Da.rename,
                            unlink: R.Da.unlink,
                            rmdir: R.Da.rmdir,
                            readdir: R.Da.readdir,
                            symlink: R.Da.symlink
                        },
                        stream: {
                            $a: R.Ba.$a
                        }
                    },
                    file: {
                        node: {
                            Xa: R.Da.Xa,
                            Ra: R.Da.Ra
                        },
                        stream: {
                            $a: R.Ba.$a,
                            read: R.Ba.read,
                            write: R.Ba.write,
                            Lb: R.Ba.Lb,
                            tb: R.Ba.tb,
                            vb: R.Ba.vb
                        }
                    },
                    link: {
                        node: {
                            Xa: R.Da.Xa,
                            Ra: R.Da.Ra,
                            readlink: R.Da.readlink
                        },
                        stream: {}
                    },
                    Nb: {
                        node: {
                            Xa: R.Da.Xa,
                            Ra: R.Da.Ra
                        },
                        stream: Sb
                    }
                }), t = Tb(e, n, t, r), 16384 === (61440 & t.mode) ? (t.Da = R.Oa.dir.node, t.Ba = R.Oa.dir.stream,
                    t.Ca = {}) : 32768 === (61440 & t.mode) ? (t.Da = R.Oa.file.node, t.Ba = R.Oa.file.stream, t.Ga =
                    0, t.Ca = null) : 40960 === (61440 & t.mode) ? (t.Da = R.Oa.link.node, t.Ba = R.Oa.link.stream) :
                8192 === (61440 & t.mode) && (t.Da = R.Oa.Nb.node, t.Ba = R.Oa.Nb.stream), t.timestamp = Date.now(),
                e && (e.Ca[n] = t, e.timestamp = t.timestamp), t
        },
        jd: function (e) {
            return e.Ca ? e.Ca.subarray ? e.Ca.subarray(0, e.Ga) : new Uint8Array(e.Ca) : new Uint8Array(0)
        },
        Qb: function (e, n) {
            var t = e.Ca ? e.Ca.length : 0;
            t >= n || (n = Math.max(n, t * (1048576 > t ? 2 : 1.125) >>> 0), 0 != t && (n = Math.max(n, 256)), t =
                e.Ca, e.Ca = new Uint8Array(n), 0 < e.Ga && e.Ca.set(t.subarray(0, e.Ga), 0))
        },
        Ec: function (e, n) {
            if (e.Ga != n)
                if (0 == n) e.Ca = null, e.Ga = 0;
                else {
                    var t = e.Ca;
                    e.Ca = new Uint8Array(n), t && e.Ca.set(t.subarray(0, Math.min(n, e.Ga))), e.Ga = n
                }
        },
        Da: {
            Xa: function (e) {
                var n = {};
                return n.dev = 8192 === (61440 & e.mode) ? e.id : 1, n.ino = e.id, n.mode = e.mode, n.nlink = 1, n.uid =
                    0, n.gid = 0, n.rdev = e.rdev, 16384 === (61440 & e.mode) ? n.size = 4096 : 32768 === (61440 &
                        e.mode) ? n.size = e.Ga : 40960 === (61440 & e.mode) ? n.size = e.link.length : n.size = 0,
                    n.atime = new Date(e.timestamp), n.mtime = new Date(e.timestamp), n.ctime = new Date(e.timestamp),
                    n.ac = 4096, n.blocks = Math.ceil(n.size / n.ac), n
            },
            Ra: function (e, n) {
                void 0 !== n.mode && (e.mode = n.mode), void 0 !== n.timestamp && (e.timestamp = n.timestamp), void 0 !==
                    n.size && R.Ec(e, n.size)
            },
            lookup: function () {
                throw Ub[44]
            },
            sb: function (e, n, t, r) {
                return R.createNode(e, n, t, r)
            },
            rename: function (e, n, t) {
                if (16384 === (61440 & e.mode)) {
                    try {
                        var r = Vb(n, t)
                    } catch (i) {}
                    if (r)
                        for (var a in r.Ca) throw new Q(55)
                }
                delete e.parent.Ca[e.name], e.parent.timestamp = Date.now(), e.name = t, n.Ca[t] = e, n.timestamp =
                    e.parent.timestamp, e.parent = n
            },
            unlink: function (e, n) {
                delete e.Ca[n], e.timestamp = Date.now()
            },
            rmdir: function (e, n) {
                var t, r = Vb(e, n);
                for (t in r.Ca) throw new Q(55);
                delete e.Ca[n], e.timestamp = Date.now()
            },
            readdir: function (e) {
                var n, t = [".", ".."];
                for (n in e.Ca) e.Ca.hasOwnProperty(n) && t.push(n);
                return t
            },
            symlink: function (e, n, t) {
                return e = R.createNode(e, n, 41471, 0), e.link = t, e
            },
            readlink: function (e) {
                if (40960 !== (61440 & e.mode)) throw new Q(28);
                return e.link
            }
        },
        Ba: {
            read: function (e, n, t, r, a) {
                var i = e.node.Ca;
                if (a >= e.node.Ga) return 0;
                if (e = Math.min(e.node.Ga - a, r), 8 < e && i.subarray) n.set(i.subarray(a, a + e), t);
                else
                    for (r = 0; r < e; r++) n[t + r] = i[a + r];
                return e
            },
            write: function (e, n, t, r, a, i) {
                if (!r) return 0;
                if (e = e.node, e.timestamp = Date.now(), n.subarray && (!e.Ca || e.Ca.subarray)) {
                    if (i) return e.Ca = n.subarray(t, t + r), e.Ga = r;
                    if (0 === e.Ga && 0 === a) return e.Ca = n.slice(t, t + r), e.Ga = r;
                    if (a + r <= e.Ga) return e.Ca.set(n.subarray(t, t + r), a), r
                }
                if (R.Qb(e, a + r), e.Ca.subarray && n.subarray) e.Ca.set(n.subarray(t, t + r), a);
                else
                    for (i = 0; i < r; i++) e.Ca[a + i] = n[t + i];
                return e.Ga = Math.max(e.Ga, a + r), r
            },
            $a: function (e, n, t) {
                if (1 === t ? n += e.position : 2 === t && 32768 === (61440 & e.node.mode) && (n += e.node.Ga), 0 >
                    n) throw new Q(28);
                return n
            },
            Lb: function (e, n, t) {
                R.Qb(e.node, n + t), e.node.Ga = Math.max(e.node.Ga, n + t)
            },
            tb: function (e, n, t, r, a, i) {
                if (0 !== n) throw new Q(28);
                if (32768 !== (61440 & e.node.mode)) throw new Q(43);
                if (e = e.node.Ca, 2 & i || e.buffer !== Oa) {
                    if ((0 < r || r + t < e.length) && (e = e.subarray ? e.subarray(r, r + t) : Array.prototype.slice
                            .call(e, r, r + t)), r = !0, t = Qb(t), !t) throw new Q(48);
                    H.set(e, t)
                } else r = !1, t = e.byteOffset;
                return {
                    Ea: t,
                    Bb: r
                }
            },
            vb: function (e, n, t, r, a) {
                if (32768 !== (61440 & e.node.mode)) throw new Q(43);
                return 2 & a || R.Ba.write(e, n, 0, r, t, !1), 0
            }
        }
    },
    Wb = null,
    Xb = {},
    Yb = [],
    Zb = 1,
    $b = null,
    bb = !0,
    ac = {},
    Q = null,
    Ub = {};

function bc(e, n) {
    if (e = Ib("/", e), n = n || {}, !e) return {
        path: "",
        node: null
    };
    var t, r = {
        Tb: !0,
        Ib: 0
    };
    for (t in r) void 0 === n[t] && (n[t] = r[t]);
    if (8 < n.Ib) throw new Q(32);
    e = Db(e.split("/").filter((function (e) {
        return !!e
    })), !1);
    var a = Wb;
    for (r = "/", t = 0; t < e.length; t++) {
        var i = t === e.length - 1;
        if (i && n.parent) break;
        if (a = Vb(a, e[t]), r = Eb(r + "/" + e[t]), a.ub && (!i || i && n.Tb) && (a = a.ub.root), !i || n.Sb)
            for (i = 0; 40960 === (61440 & a.mode);)
                if (a = cc(r), r = Ib(Fb(r), a), a = bc(r, {
                        Ib: n.Ib
                    }).node, 40 < i++) throw new Q(32)
    }
    return {
        path: r,
        node: a
    }
}

function dc(e) {
    for (var n;;) {
        if (e === e.parent) return e = e.Ua.Xb, n ? "/" !== e[e.length - 1] ? e + "/" + n : e + n : e;
        n = n ? e.name + "/" + n : e.name, e = e.parent
    }
}

function ec(e, n) {
    for (var t = 0, r = 0; r < n.length; r++) t = (t << 5) - t + n.charCodeAt(r) | 0;
    return (e + t >>> 0) % $b.length
}

function Vb(e, n) {
    var t;
    if (t = (t = fc(e, "x")) ? t : e.Da.lookup ? 0 : 2) throw new Q(t, e);
    for (t = $b[ec(e.id, n)]; t; t = t.xc) {
        var r = t.name;
        if (t.parent.id === e.id && r === n) return t
    }
    return e.Da.lookup(e, n)
}

function Tb(e, n, t, r) {
    return e = new gc(e, n, t, r), n = ec(e.parent.id, e.name), e.xc = $b[n], $b[n] = e
}
var hc = {
    r: 0,
    "r+": 2,
    w: 577,
    "w+": 578,
    a: 1089,
    "a+": 1090
};

function ic(e) {
    var n = ["r", "w", "rw"][3 & e];
    return 512 & e && (n += "w"), n
}

function fc(e, n) {
    return bb ? 0 : !n.includes("r") || 292 & e.mode ? n.includes("w") && !(146 & e.mode) || n.includes("x") && !(73 &
        e.mode) ? 2 : 0 : 2
}

function jc(e, n) {
    try {
        return Vb(e, n), 20
    } catch (t) {}
    return fc(e, "wx")
}

function kc() {
    for (var e = 4096, n = 0; n <= e; n++)
        if (!Yb[n]) return n;
    throw new Q(33)
}

function lc(e) {
    mc || (mc = function () {}, mc.prototype = {});
    var n, t = new mc;
    for (n in e) t[n] = e[n];
    return e = t, t = kc(), e.fd = t, Yb[t] = e
}
var Xa, Sb = {
    open: function (e) {
        e.Ba = Xb[e.node.rdev].Ba, e.Ba.open && e.Ba.open(e)
    },
    $a: function () {
        throw new Q(70)
    }
};

function Lb(e, n) {
    Xb[e] = {
        Ba: n
    }
}

function nc(e, n) {
    var t = "/" === n,
        r = !n;
    if (t && Wb) throw new Q(10);
    if (!t && !r) {
        var a = bc(n, {
            Tb: !1
        });
        if (n = a.path, a = a.node, a.ub) throw new Q(10);
        if (16384 !== (61440 & a.mode)) throw new Q(54)
    }
    n = {
        type: e,
        od: {},
        Xb: n,
        wc: []
    }, e = e.Ua(n), e.Ua = n, n.root = e, t ? Wb = e : a && (a.ub = n, a.Ua && a.Ua.wc.push(n))
}

function oc(e, n, t) {
    var r = bc(e, {
        parent: !0
    }).node;
    if (e = Gb(e), !e || "." === e || ".." === e) throw new Q(28);
    var a = jc(r, e);
    if (a) throw new Q(a);
    if (!r.Da.sb) throw new Q(63);
    return r.Da.sb(r, e, n, t)
}

function S(e) {
    return oc(e, 16895, 0)
}

function pc(e, n, t) {
    "undefined" === typeof t && (t = n, n = 438), oc(e, 8192 | n, t)
}

function $a(e, n) {
    if (!Ib(e)) throw new Q(44);
    var t = bc(n, {
        parent: !0
    }).node;
    if (!t) throw new Q(44);
    n = Gb(n);
    var r = jc(t, n);
    if (r) throw new Q(r);
    if (!t.Da.symlink) throw new Q(63);
    t.Da.symlink(t, n, e)
}

function cc(e) {
    if (e = bc(e).node, !e) throw new Q(44);
    if (!e.Da.readlink) throw new Q(28);
    return Ib(dc(e.parent), e.Da.readlink(e))
}

function ab(e, n) {
    if ("" === e) throw new Q(44);
    if ("string" === typeof n) {
        var t = hc[n];
        if ("undefined" === typeof t) throw Error("Unknown file open mode: " + n);
        n = t
    }
    var r = 64 & n ? 4095 & ("undefined" === typeof r ? 438 : r) | 32768 : 0;
    if ("object" === typeof e) var a = e;
    else {
        e = Eb(e);
        try {
            a = bc(e, {
                Sb: !(131072 & n)
            }).node
        } catch (i) {}
    }
    if (t = !1, 64 & n)
        if (a) {
            if (128 & n) throw new Q(20)
        } else a = oc(e, r, 0), t = !0;
    if (!a) throw new Q(44);
    if (8192 === (61440 & a.mode) && (n &= -513), 65536 & n && 16384 !== (61440 & a.mode)) throw new Q(54);
    if (!t && (r = a ? 40960 === (61440 & a.mode) ? 32 : 16384 === (61440 & a.mode) && ("r" !== ic(n) || 512 & n) ? 31 :
            fc(a, ic(n)) : 44)) throw new Q(r);
    if (512 & n) {
        if (r = a, r = "string" === typeof r ? bc(r, {
                Sb: !0
            }).node : r, !r.Da.Ra) throw new Q(63);
        if (16384 === (61440 & r.mode)) throw new Q(31);
        if (32768 !== (61440 & r.mode)) throw new Q(28);
        if (t = fc(r, "w")) throw new Q(t);
        r.Da.Ra(r, {
            size: 0,
            timestamp: Date.now()
        })
    }
    n &= -131713, a = lc({
        node: a,
        path: dc(a),
        flags: n,
        seekable: !0,
        position: 0,
        Ba: a.Ba,
        bd: [],
        error: !1
    }), a.Ba.open && a.Ba.open(a), !f.logReadFiles || 1 & n || (qc || (qc = {}), e in qc || (qc[e] = 1, C(
        "FS.trackingDelegate error on read file: " + e)));
    try {
        ac.onOpenFile && (a = 0, 1 !== (2097155 & n) && (a |= 1), 0 !== (2097155 & n) && (a |= 2), ac.onOpenFile(e, a))
    } catch (i) {
        C("FS.trackingDelegate['onOpenFile']('" + e + "', flags) threw an exception: " + i.message)
    }
}

function rc(e, n, t) {
    if (null === e.fd) throw new Q(8);
    if (!e.seekable || !e.Ba.$a) throw new Q(70);
    if (0 != t && 1 != t && 2 != t) throw new Q(28);
    e.position = e.Ba.$a(e, n, t), e.bd = []
}

function Ya() {
    Q || (Q = function (e, n) {
        this.node = n, this.Hc = function (e) {
            this.Wa = e
        }, this.Hc(e), this.message = "FS error"
    }, Q.prototype = Error(), Q.prototype.constructor = Q, [44].forEach((function (e) {
        Ub[e] = new Q(e), Ub[e].stack = "<generic error, no stack>"
    })))
}

function sc(e, n) {
    var t = 0;
    return e && (t |= 365), n && (t |= 146), t
}

function Za(e, n, t) {
    e = Eb("/dev/" + e);
    var r = sc(!!n, !!t);
    tc || (tc = 64);
    var a = tc++ << 8 | 0;
    Lb(a, {
        open: function (e) {
            e.seekable = !1
        },
        close: function () {
            t && t.buffer && t.buffer.length && t(10)
        },
        read: function (e, t, r, a) {
            for (var i = 0, o = 0; o < a; o++) {
                try {
                    var u = n()
                } catch (c) {
                    throw new Q(29)
                }
                if (void 0 === u && 0 === i) throw new Q(6);
                if (null === u || void 0 === u) break;
                i++, t[r + o] = u
            }
            return i && (e.node.timestamp = Date.now()), i
        },
        write: function (e, n, r, a) {
            for (var i = 0; i < a; i++) try {
                t(n[r + i])
            } catch (o) {
                throw new Q(29)
            }
            return a && (e.node.timestamp = Date.now()), i
        }
    }), pc(e, r, a)
}
var tc, mc, qc, uc = {},
    vc = {};

function wc(e) {
    if (e = Yb[e], !e) throw new Q(8);
    return e
}

function xc(e, n, t, r, a, i) {
    if (q) return T(2, 1, e, n, t, r, a, i);
    try {
        e: {
            i <<= 12;
            var o = !1;
            if (0 !== (16 & r) && 0 !== e % 65536) var u = -28;
            else {
                if (0 !== (32 & r)) {
                    var c = Qb(n);
                    if (!c) {
                        u = -48;
                        break e
                    }
                    o = !0
                } else {
                    var f = Yb[a];
                    if (!f) {
                        u = -8;
                        break e
                    }
                    var s = i;
                    if (0 !== (2 & t) && 0 === (2 & r) && 2 !== (2097155 & f.flags)) throw new Q(2);
                    if (1 === (2097155 & f.flags)) throw new Q(2);
                    if (!f.Ba.tb) throw new Q(43);
                    var l = f.Ba.tb(f, e, n, s, t, r);
                    c = l.Ea, o = l.Bb
                }
                vc[c] = {
                    vc: c,
                    tc: n,
                    Bb: o,
                    fd: a,
                    zc: t,
                    flags: r,
                    offset: i
                }, u = c
            }
        }
        return u
    }
    catch (d) {
        return "undefined" !== typeof uc && d instanceof Q || B(d), -d.Wa
    }
}

function yc(e, n) {
    if (q) return T(3, 1, e, n);
    try {
        var t = vc[e];
        if (0 !== n && t) {
            if (n === t.tc) {
                var r = Yb[t.fd];
                if (r && 2 & t.zc) {
                    var a = t.flags,
                        i = t.offset,
                        o = F.slice(e, e + n);
                    r && r.Ba.vb && r.Ba.vb(r, o, i, n, a)
                }
                vc[e] = null, t.Bb && P(t.vc)
            }
            var u = 0
        } else u = -28;
        return u
    } catch (c) {
        return "undefined" !== typeof uc && c instanceof Q || B(c), -c.Wa
    }
}
var zc = {};

function Ac(e) {
    for (; e.length;) {
        var n = e.pop();
        e.pop()(n)
    }
}

function Bc(e) {
    return this.fromWireType(J[e >> 2])
}
var Cc = {},
    Dc = {},
    Ec = {};

function Fc(e) {
    if (void 0 === e) return "_unknown";
    e = e.replace(/[^a-zA-Z0-9_]/g, "$");
    var n = e.charCodeAt(0);
    return 48 <= n && 57 >= n ? "_" + e : e
}

function Gc(e, n) {
    return e = Fc(e), new Function("body", "return function " + e +
        '() {\n    "use strict";    return body.apply(this, arguments);\n};\n')(n)
}

function Hc(e) {
    var n = Error,
        t = Gc(e, (function (n) {
            this.name = e, this.message = n, n = Error(n).stack, void 0 !== n && (this.stack = this.toString() +
                "\n" + n.replace(/^Error(:[^\n]*)?\n/, ""))
        }));
    return t.prototype = Object.create(n.prototype), t.prototype.constructor = t, t.prototype.toString = function () {
        return void 0 === this.message ? this.name : this.name + ": " + this.message
    }, t
}
var Ic = void 0;

function Jc(e) {
    throw new Ic(e)
}

function Kc(e, n, t) {
    function r(n) {
        n = t(n), n.length !== e.length && Jc("Mismatched type converter count");
        for (var r = 0; r < e.length; ++r) U(e[r], n[r])
    }
    e.forEach((function (e) {
        Ec[e] = n
    }));
    var a = Array(n.length),
        i = [],
        o = 0;
    n.forEach((function (e, n) {
        Dc.hasOwnProperty(e) ? a[n] = Dc[e] : (i.push(e), Cc.hasOwnProperty(e) || (Cc[e] = []), Cc[e].push(
            (function () {
                a[n] = Dc[e], ++o, o === i.length && r(a)
            })))
    })), 0 === i.length && r(a)
}

function Lc(e) {
    switch (e) {
        case 1:
            return 0;
        case 2:
            return 1;
        case 4:
            return 2;
        case 8:
            return 3;
        default:
            throw new TypeError("Unknown type size: " + e)
    }
}
var Mc = void 0;

function V(e) {
    for (var n = ""; F[e];) n += Mc[F[e++]];
    return n
}
var Nc = void 0;

function W(e) {
    throw new Nc(e)
}

function U(e, n, t) {
    if (t = t || {}, !("argPackAdvance" in n)) throw new TypeError(
        "registerType registeredInstance requires argPackAdvance");
    var r = n.name;
    if (e || W('type "' + r + '" must have a positive integer typeid pointer'), Dc.hasOwnProperty(e)) {
        if (t.nc) return;
        W("Cannot register type '" + r + "' twice")
    }
    Dc[e] = n, delete Ec[e], Cc.hasOwnProperty(e) && (n = Cc[e], delete Cc[e], n.forEach((function (e) {
        e()
    })))
}

function Oc(e) {
    W(e.Aa.Ha.Fa.name + " instance already deleted")
}
var Pc = !1;

function Qc() {}

function Rc(e) {
    --e.count.value, 0 === e.count.value && (e.Ja ? e.La.Va(e.Ja) : e.Ha.Fa.Va(e.Ea))
}

function Sc(e) {
    return "undefined" === typeof FinalizationGroup ? (Sc = function (e) {
        return e
    }, e) : (Pc = new FinalizationGroup((function (e) {
        for (var n = e.next(); !n.done; n = e.next()) n = n.value, n.Ea ? Rc(n) : console.warn(
            "object already deleted: " + n.Ea)
    })), Sc = function (e) {
        return Pc.register(e, e.Aa, e.Aa), e
    }, Qc = function (e) {
        Pc.unregister(e.Aa)
    }, Sc(e))
}
var Tc = void 0,
    Uc = [];

function Vc() {
    for (; Uc.length;) {
        var e = Uc.pop();
        e.Aa.bb = !1, e["delete"]()
    }
}

function Wc() {}
var Xc = {};

function Yc(e, n, t) {
    if (void 0 === e[n].Pa) {
        var r = e[n];
        e[n] = function () {
            return e[n].Pa.hasOwnProperty(arguments.length) || W("Function '" + t +
                "' called with an invalid number of arguments (" + arguments.length + ") - expects one of (" +
                e[n].Pa + ")!"), e[n].Pa[arguments.length].apply(this, arguments)
        }, e[n].Pa = [], e[n].Pa[r.ob] = r
    }
}

function Zc(e, n) {
    f.hasOwnProperty(e) ? (W("Cannot register public name '" + e + "' twice"), Yc(f, e, e), f.hasOwnProperty(void 0) &&
        W("Cannot register multiple overloads of a function with the same number of arguments (undefined)!"), f[e].Pa[
            void 0] = n) : f[e] = n
}

function $c(e, n, t, r, a, i, o, u) {
    this.name = e, this.constructor = n, this.cb = t, this.Va = r, this.Ma = a, this.hc = i, this.lb = o, this.dc = u,
        this.Ac = []
}

function ad(e, n, t) {
    for (; n !== t;) n.lb || W("Expected null or instance of " + t.name + ", got an instance of " + n.name), e = n.lb(e),
        n = n.Ma;
    return e
}

function bd(e, n) {
    return null === n ? (this.Fb && W("null is not a valid " + this.name), 0) : (n.Aa || W('Cannot pass "' + cd(n) +
            '" as a ' + this.name), n.Aa.Ea || W("Cannot pass deleted object as a pointer of type " + this.name),
        ad(n.Aa.Ea, n.Aa.Ha.Fa, this.Fa))
}

function dd(e, n) {
    if (null === n) {
        if (this.Fb && W("null is not a valid " + this.name), this.rb) {
            var t = this.Hb();
            return null !== e && e.push(this.Va, t), t
        }
        return 0
    }
    if (n.Aa || W('Cannot pass "' + cd(n) + '" as a ' + this.name), n.Aa.Ea || W(
            "Cannot pass deleted object as a pointer of type " + this.name), !this.qb && n.Aa.Ha.qb && W(
            "Cannot convert argument of type " + (n.Aa.La ? n.Aa.La.name : n.Aa.Ha.name) + " to parameter type " + this
            .name), t = ad(n.Aa.Ea, n.Aa.Ha.Fa, this.Fa), this.rb) switch (void 0 === n.Aa.Ja && W(
        "Passing raw pointer to smart pointer is illegal"), this.Rc) {
        case 0:
            n.Aa.La === this ? t = n.Aa.Ja : W("Cannot convert argument of type " + (n.Aa.La ? n.Aa.La.name : n.Aa.Ha
                .name) + " to parameter type " + this.name);
            break;
        case 1:
            t = n.Aa.Ja;
            break;
        case 2:
            if (n.Aa.La === this) t = n.Aa.Ja;
            else {
                var r = n.clone();
                t = this.Bc(t, ed((function () {
                    r["delete"]()
                }))), null !== e && e.push(this.Va, t)
            }
            break;
        default:
            W("Unsupporting sharing policy")
    }
    return t
}

function fd(e, n) {
    return null === n ? (this.Fb && W("null is not a valid " + this.name), 0) : (n.Aa || W('Cannot pass "' + cd(n) +
            '" as a ' + this.name), n.Aa.Ea || W("Cannot pass deleted object as a pointer of type " + this.name), n
        .Aa.Ha.qb && W("Cannot convert argument of type " + n.Aa.Ha.name + " to parameter type " + this.name), ad(n
            .Aa.Ea, n.Aa.Ha.Fa, this.Fa))
}

function gd(e, n, t) {
    return n === t ? e : void 0 === t.Ma ? null : (e = gd(e, n, t.Ma), null === e ? null : t.dc(e))
}
var hd = {};

function jd(e, n) {
    for (void 0 === n && W("ptr should not be undefined"); e.Ma;) n = e.lb(n), e = e.Ma;
    return hd[n]
}

function kd(e, n) {
    return n.Ha && n.Ea || Jc("makeClassHandle requires ptr and ptrType"), !!n.La !== !!n.Ja && Jc(
        "Both smartPtrType and smartPtr must be specified"), n.count = {
        value: 1
    }, Sc(Object.create(e, {
        Aa: {
            value: n
        }
    }))
}

function X(e, n, t, r) {
    this.name = e, this.Fa = n, this.Fb = t, this.qb = r, this.rb = !1, this.Va = this.Bc = this.Hb = this.Zb = this.Rc =
        this.yc = void 0, void 0 !== n.Ma ? this.toWireType = dd : (this.toWireType = r ? bd : fd, this.Na = null)
}

function ld(e, n) {
    f.hasOwnProperty(e) || Jc("Replacing nonexistant public symbol"), f[e] = n, f[e].ob = void 0
}

function md(e, n) {
    var t = [];
    return function () {
        t.length = arguments.length;
        for (var r = 0; r < arguments.length; r++) t[r] = arguments[r];
        return e.includes("j") ? (r = f["dynCall_" + e], r = t && t.length ? r.apply(null, [n].concat(t)) : r.call(
            null, n)) : r = L.get(n).apply(null, t), r
    }
}

function Y(e, n) {
    e = V(e);
    var t = e.includes("j") ? md(e, n) : L.get(n);
    return "function" !== typeof t && W("unknown function pointer with signature " + e + ": " + n), t
}
var nd = void 0;

function od(e) {
    e = rd(e);
    var n = V(e);
    return P(e), n
}

function sd(e, n) {
    function t(e) {
        a[e] || Dc[e] || (Ec[e] ? Ec[e].forEach(t) : (r.push(e), a[e] = !0))
    }
    var r = [],
        a = {};
    throw n.forEach(t), new nd(e + ": " + r.map(od).join([", "]))
}

function td(e, n) {
    for (var t = [], r = 0; r < e; r++) t.push(G[(n >> 2) + r]);
    return t
}

function ud(e) {
    var n = Function;
    if (!(n instanceof Function)) throw new TypeError("new_ called with constructor type " + typeof n +
        " which is not a function");
    var t = Gc(n.name || "unknownFunctionName", (function () {}));
    return t.prototype = n.prototype, t = new t, e = n.apply(t, e), e instanceof Object ? e : t
}
var vd = [],
    Z = [{}, {
        value: void 0
    }, {
        value: null
    }, {
        value: !0
    }, {
        value: !1
    }];

function ed(e) {
    switch (e) {
        case void 0:
            return 1;
        case null:
            return 2;
        case !0:
            return 3;
        case !1:
            return 4;
        default:
            var n = vd.length ? vd.pop() : Z.length;
            return Z[n] = {
                Dc: 1,
                value: e
            }, n
    }
}

function cd(e) {
    if (null === e) return "null";
    var n = typeof e;
    return "object" === n || "array" === n || "function" === n ? e.toString() : "" + e
}

function wd(e, n) {
    switch (n) {
        case 2:
            return function (e) {
                return this.fromWireType(Pa[e >> 2])
            };
        case 3:
            return function (e) {
                return this.fromWireType(Qa[e >> 3])
            };
        default:
            throw new TypeError("Unknown float type: " + e)
    }
}

function xd(e, n, t) {
    switch (n) {
        case 0:
            return t ? function (e) {
                return H[e]
            } : function (e) {
                return F[e]
            };
        case 1:
            return t ? function (e) {
                return Ia[e >> 1]
            } : function (e) {
                return Ha[e >> 1]
            };
        case 2:
            return t ? function (e) {
                return G[e >> 2]
            } : function (e) {
                return J[e >> 2]
            };
        default:
            throw new TypeError("Unknown integer type: " + e)
    }
}
var yd = [];

function T(e, n) {
    for (var t = arguments.length - 2, r = zd(), a = Ad(8 * t), i = a >> 3, o = 0; o < t; o++) Qa[i + o] = arguments[2 +
        o];
    return t = Bd(e, t, a, n), zb(r), t
}
var Cd = [],
    Dd = [0, "undefined" !== typeof document ? document : 0, "undefined" !== typeof window ? window : 0];

function Ed(e) {
    return e = 2 < e ? Ca(e) : e, Dd[e] || ("undefined" !== typeof document ? document.querySelector(e) : void 0)
}

function Fd(e, n, t) {
    var r = Ed(e);
    if (!r) return -4;
    if (r.pb && (G[r.pb >> 2] = n, G[r.pb + 4 >> 2] = t), !r.Yb && r.ed) {
        if (r.pb) {
            r = G[r.pb + 8 >> 2], e = e ? Ca(e) : "";
            var a = zd(),
                i = Ad(12),
                o = 0;
            if (e) {
                o = Ea(e) + 1;
                var u = O(o);
                Da(e, F, u, o), o = u
            }
            return G[i >> 2] = o, G[i + 4 >> 2] = n, G[i + 8 >> 2] = t, Gd(0, r, 657457152, 0, o, i), zb(a), 1
        }
        return -4
    }
    return r.Yb && (r = r.Yb), e = !1, r.nb && r.nb.mb && (e = r.nb.mb.getParameter(2978), e = 0 === e[0] && 0 === e[1] &&
        e[2] === r.width && e[3] === r.height), r.width = n, r.height = t, e && r.nb.mb.viewport(0, 0, n, t), 0
}

function Hd(e, n, t) {
    return q ? T(4, 1, e, n, t) : Fd(e, n, t)
}

function Id(e) {
    var n = e.getExtension("ANGLE_instanced_arrays");
    n && (e.vertexAttribDivisor = function (e, t) {
        n.vertexAttribDivisorANGLE(e, t)
    }, e.drawArraysInstanced = function (e, t, r, a) {
        n.drawArraysInstancedANGLE(e, t, r, a)
    }, e.drawElementsInstanced = function (e, t, r, a, i) {
        n.drawElementsInstancedANGLE(e, t, r, a, i)
    })
}

function Jd(e) {
    var n = e.getExtension("OES_vertex_array_object");
    n && (e.createVertexArray = function () {
        return n.createVertexArrayOES()
    }, e.deleteVertexArray = function (e) {
        n.deleteVertexArrayOES(e)
    }, e.bindVertexArray = function (e) {
        n.bindVertexArrayOES(e)
    }, e.isVertexArray = function (e) {
        return n.isVertexArrayOES(e)
    })
}

function Kd(e) {
    var n = e.getExtension("WEBGL_draw_buffers");
    n && (e.drawBuffers = function (e, t) {
        n.drawBuffersWEBGL(e, t)
    })
}

function Ld(e, n) {
    e.Ub || (e.Ub = e.getContext, e.getContext = function (n, t) {
        return t = e.Ub(n, t), "webgl" == n == t instanceof WebGLRenderingContext ? t : null
    });
    var t = e.getContext("webgl", n);
    return t ? Md(t, n) : 0
}

function Md(e, n) {
    var t = O(8);
    G[t + 4 >> 2] = tb();
    var r = {
        kd: t,
        attributes: n,
        version: n.uc,
        mb: e
    };
    return e.canvas && (e.canvas.nb = r), ("undefined" === typeof n.Pb || n.Pb) && Nd(r), t
}

function Nd(e) {
    if (e || (e = Od), !e.pc) {
        e.pc = !0;
        var n = e.mb;
        Id(n), Jd(n), Kd(n), n.gd = n.getExtension("EXT_disjoint_timer_query"), n.nd = n.getExtension(
            "WEBGL_multi_draw"), (n.getSupportedExtensions() || []).forEach((function (e) {
            e.includes("lose_context") || e.includes("debug") || n.getExtension(e)
        }))
    }
}
var Od, Sd, Pd = ["default", "low-power", "high-performance"],
    Qd = {};

function Rd() {
    if (!Sd) {
        var e, n = {
            USER: "web_user",
            LOGNAME: "web_user",
            PATH: "/",
            PWD: "/",
            HOME: "/home/<USER>",
            LANG: ("object" === typeof navigator && navigator.languages && navigator.languages[0] || "C").replace(
                "-", "_") + ".UTF-8",
            _: ca || "./this.program"
        };
        for (e in Qd) void 0 === Qd[e] ? delete n[e] : n[e] = Qd[e];
        var t = [];
        for (e in n) t.push(e + "=" + n[e]);
        Sd = t
    }
    return Sd
}

function Td(e, n) {
    if (q) return T(5, 1, e, n);
    try {
        var t = 0;
        return Rd().forEach((function (r, a) {
            var i = n + t;
            for (a = G[e + 4 * a >> 2] = i, i = 0; i < r.length; ++i) H[a++ >> 0] = r.charCodeAt(i);
            H[a >> 0] = 0, t += r.length + 1
        })), 0
    } catch (r) {
        return "undefined" !== typeof uc && r instanceof Q || B(r), r.Wa
    }
}

function Ud(e, n) {
    if (q) return T(6, 1, e, n);
    try {
        var t = Rd();
        G[e >> 2] = t.length;
        var r = 0;
        return t.forEach((function (e) {
            r += e.length + 1
        })), G[n >> 2] = r, 0
    } catch (a) {
        return "undefined" !== typeof uc && a instanceof Q || B(a), a.Wa
    }
}

function Vd(e) {
    if (q) return T(7, 1, e);
    try {
        var n = wc(e);
        if (null === n.fd) throw new Q(8);
        n.Eb && (n.Eb = null);
        try {
            n.Ba.close && n.Ba.close(n)
        } catch (t) {
            throw t
        } finally {
            Yb[n.fd] = null
        }
        return n.fd = null, 0
    } catch (t) {
        return "undefined" !== typeof uc && t instanceof Q || B(t), t.Wa
    }
}

function Wd(e, n, t, r) {
    if (q) return T(8, 1, e, n, t, r);
    try {
        e: {
            for (var a = wc(e), i = e = 0; i < t; i++) {
                var o = G[n + (8 * i + 4) >> 2],
                    u = a,
                    c = G[n + 8 * i >> 2],
                    f = o,
                    s = void 0;
                if (0 > f || 0 > s) throw new Q(28);
                if (null === u.fd) throw new Q(8);
                if (1 === (2097155 & u.flags)) throw new Q(8);
                if (16384 === (61440 & u.node.mode)) throw new Q(31);
                if (!u.Ba.read) throw new Q(28);
                var l = "undefined" !== typeof s;
                if (l) {
                    if (!u.seekable) throw new Q(70)
                } else s = u.position;
                var d = u.Ba.read(u, H, c, f, s);
                l || (u.position += d);
                var h = d;
                if (0 > h) {
                    var p = -1;
                    break e
                }
                if (e += h, h < o) break
            }
            p = e
        }
        return G[r >> 2] = p,
        0
    }
    catch (b) {
        return "undefined" !== typeof uc && b instanceof Q || B(b), b.Wa
    }
}

function Xd(e, n, t, r, a) {
    if (q) return T(9, 1, e, n, t, r, a);
    try {
        var i = wc(e);
        return e = 4294967296 * t + (n >>> 0), -9007199254740992 >= e || 9007199254740992 <= e ? -61 : (rc(i, e, r), lb = [
                i.position >>> 0, (kb = i.position, 1 <= +Math.abs(kb) ? 0 < kb ? (0 | Math.min(+Math.floor(kb /
                    4294967296), 4294967295)) >>> 0 : ~~+Math.ceil((kb - +(~~kb >>> 0)) / 4294967296) >>> 0 : 0)],
            G[a >> 2] = lb[0], G[a + 4 >> 2] = lb[1], i.Eb && 0 === e && 0 === r && (i.Eb = null), 0)
    } catch (o) {
        return "undefined" !== typeof uc && o instanceof Q || B(o), o.Wa
    }
}

function Yd(e, n, t, r) {
    if (q) return T(10, 1, e, n, t, r);
    try {
        e: {
            for (var a = wc(e), i = e = 0; i < t; i++) {
                var o = a,
                    u = G[n + 8 * i >> 2],
                    c = G[n + (8 * i + 4) >> 2],
                    f = void 0;
                if (0 > c || 0 > f) throw new Q(28);
                if (null === o.fd) throw new Q(8);
                if (0 === (2097155 & o.flags)) throw new Q(8);
                if (16384 === (61440 & o.node.mode)) throw new Q(31);
                if (!o.Ba.write) throw new Q(28);
                o.seekable && 1024 & o.flags && rc(o, 0, 2);
                var s = "undefined" !== typeof f;
                if (s) {
                    if (!o.seekable) throw new Q(70)
                } else f = o.position;
                var l = o.Ba.write(o, H, u, c, f, void 0);
                s || (o.position += l);
                try {
                    o.path && ac.onWriteToFile && ac.onWriteToFile(o.path)
                } catch (p) {
                    C("FS.trackingDelegate['onWriteToFile']('" + o.path + "') threw an exception: " + p.message)
                }
                var d = l;
                if (0 > d) {
                    var h = -1;
                    break e
                }
                e += d
            }
            h = e
        }
        return G[r >> 2] = h,
        0
    }
    catch (p) {
        return "undefined" !== typeof uc && p instanceof Q || B(p), p.Wa
    }
}

function wb(e) {
    if (q) throw "Internal Error! spawnThread() can only ever be called from main application thread!";
    var n = N.ic();
    if (!n) return 6;
    if (void 0 !== n.Ka) throw "Internal error!";
    if (!e.wb) throw "Internal error, no pthread ptr!";
    N.Ya.push(n);
    for (var t = O(512), r = 0; 128 > r; ++r) G[t + 4 * r >> 2] = 0;
    var a = e.ab + e.fb;
    r = N.Qa[e.wb] = {
        worker: n,
        ab: e.ab,
        fb: e.fb,
        Cb: e.Cb,
        Sa: e.wb
    };
    var i = r.Sa >> 2;
    Atomics.store(J, i + 16, e.detached), Atomics.store(J, i + 25, t), Atomics.store(J, i + 10, r.Sa), Atomics.store(J,
            i + 20, e.fb), Atomics.store(J, i + 19, a), Atomics.store(J, i + 26, e.fb), Atomics.store(J, i + 28, a),
        Atomics.store(J, i + 29, e.detached), t = Zd() + 40, Atomics.store(J, i + 43, t), n.Ka = r;
    var o = {
        cmd: "run",
        start_routine: e.Sc,
        arg: e.hb,
        threadInfoStruct: e.wb,
        stackBase: e.ab,
        stackSize: e.fb
    };
    return n.jb = function () {
        o.time = performance.now(), n.postMessage(o, e.ad)
    }, n.loaded && (n.jb(), delete n.jb), 0
}

function $d(e) {
    return 0 === e % 4 && (0 !== e % 100 || 0 === e % 400)
}

function ae(e, n) {
    for (var t = 0, r = 0; r <= n; t += e[r++]);
    return t
}
var be = [31, 29, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31],
    ce = [31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31];

function de(e, n) {
    for (e = new Date(e.getTime()); 0 < n;) {
        var t = e.getMonth(),
            r = ($d(e.getFullYear()) ? be : ce)[t];
        if (!(n > r - e.getDate())) {
            e.setDate(e.getDate() + n);
            break
        }
        n -= r - e.getDate() + 1, e.setDate(1), 11 > t ? e.setMonth(t + 1) : (e.setMonth(0), e.setFullYear(e.getFullYear() +
            1))
    }
    return e
}

function ee(e, n, t, r) {
    function a(e, n, t) {
        for (e = "number" === typeof e ? e.toString() : e || ""; e.length < n;) e = t[0] + e;
        return e
    }

    function i(e, n) {
        return a(e, n, "0")
    }

    function o(e, n) {
        function t(e) {
            return 0 > e ? -1 : 0 < e ? 1 : 0
        }
        var r;
        return 0 === (r = t(e.getFullYear() - n.getFullYear())) && 0 === (r = t(e.getMonth() - n.getMonth())) && (r = t(
            e.getDate() - n.getDate())), r
    }

    function u(e) {
        switch (e.getDay()) {
            case 0:
                return new Date(e.getFullYear() - 1, 11, 29);
            case 1:
                return e;
            case 2:
                return new Date(e.getFullYear(), 0, 3);
            case 3:
                return new Date(e.getFullYear(), 0, 2);
            case 4:
                return new Date(e.getFullYear(), 0, 1);
            case 5:
                return new Date(e.getFullYear() - 1, 11, 31);
            case 6:
                return new Date(e.getFullYear() - 1, 11, 30)
        }
    }

    function c(e) {
        e = de(new Date(e.Ia + 1900, 0, 1), e.Ab);
        var n = new Date(e.getFullYear() + 1, 0, 4),
            t = u(new Date(e.getFullYear(), 0, 4));
        return n = u(n), 0 >= o(t, e) ? 0 >= o(n, e) ? e.getFullYear() + 1 : e.getFullYear() : e.getFullYear() - 1
    }
    var f = G[r + 40 >> 2];
    for (var s in r = {
            Zc: G[r >> 2],
            Yc: G[r + 4 >> 2],
            yb: G[r + 8 >> 2],
            kb: G[r + 12 >> 2],
            gb: G[r + 16 >> 2],
            Ia: G[r + 20 >> 2],
            zb: G[r + 24 >> 2],
            Ab: G[r + 28 >> 2],
            sd: G[r + 32 >> 2],
            Xc: G[r + 36 >> 2],
            $c: f ? Ca(f) : ""
        }, t = Ca(t), f = {
            "%c": "%a %b %d %H:%M:%S %Y",
            "%D": "%m/%d/%y",
            "%F": "%Y-%m-%d",
            "%h": "%b",
            "%r": "%I:%M:%S %p",
            "%R": "%H:%M",
            "%T": "%H:%M:%S",
            "%x": "%m/%d/%y",
            "%X": "%H:%M:%S",
            "%Ec": "%c",
            "%EC": "%C",
            "%Ex": "%m/%d/%y",
            "%EX": "%H:%M:%S",
            "%Ey": "%y",
            "%EY": "%Y",
            "%Od": "%d",
            "%Oe": "%e",
            "%OH": "%H",
            "%OI": "%I",
            "%Om": "%m",
            "%OM": "%M",
            "%OS": "%S",
            "%Ou": "%u",
            "%OU": "%U",
            "%OV": "%V",
            "%Ow": "%w",
            "%OW": "%W",
            "%Oy": "%y"
        }, f) t = t.replace(new RegExp(s, "g"), f[s]);
    var l = "Sunday Monday Tuesday Wednesday Thursday Friday Saturday".split(" "),
        d = "January February March April May June July August September October November December".split(" ");
    for (s in f = {
            "%a": function (e) {
                return l[e.zb].substring(0, 3)
            },
            "%A": function (e) {
                return l[e.zb]
            },
            "%b": function (e) {
                return d[e.gb].substring(0, 3)
            },
            "%B": function (e) {
                return d[e.gb]
            },
            "%C": function (e) {
                return i((e.Ia + 1900) / 100 | 0, 2)
            },
            "%d": function (e) {
                return i(e.kb, 2)
            },
            "%e": function (e) {
                return a(e.kb, 2, " ")
            },
            "%g": function (e) {
                return c(e).toString().substring(2)
            },
            "%G": function (e) {
                return c(e)
            },
            "%H": function (e) {
                return i(e.yb, 2)
            },
            "%I": function (e) {
                return e = e.yb, 0 == e ? e = 12 : 12 < e && (e -= 12), i(e, 2)
            },
            "%j": function (e) {
                return i(e.kb + ae($d(e.Ia + 1900) ? be : ce, e.gb - 1), 3)
            },
            "%m": function (e) {
                return i(e.gb + 1, 2)
            },
            "%M": function (e) {
                return i(e.Yc, 2)
            },
            "%n": function () {
                return "\n"
            },
            "%p": function (e) {
                return 0 <= e.yb && 12 > e.yb ? "AM" : "PM"
            },
            "%S": function (e) {
                return i(e.Zc, 2)
            },
            "%t": function () {
                return "\t"
            },
            "%u": function (e) {
                return e.zb || 7
            },
            "%U": function (e) {
                var n = new Date(e.Ia + 1900, 0, 1),
                    t = 0 === n.getDay() ? n : de(n, 7 - n.getDay());
                return e = new Date(e.Ia + 1900, e.gb, e.kb), 0 > o(t, e) ? i(Math.ceil((31 - t.getDate() + (ae($d(
                        e.getFullYear()) ? be : ce, e.getMonth() - 1) - 31) + e.getDate()) / 7), 2) : 0 === o(t, n) ?
                    "01" : "00"
            },
            "%V": function (e) {
                var n = new Date(e.Ia + 1901, 0, 4),
                    t = u(new Date(e.Ia + 1900, 0, 4));
                n = u(n);
                var r = de(new Date(e.Ia + 1900, 0, 1), e.Ab);
                return 0 > o(r, t) ? "53" : 0 >= o(n, r) ? "01" : i(Math.ceil((t.getFullYear() < e.Ia + 1900 ? e.Ab +
                    32 - t.getDate() : e.Ab + 1 - t.getDate()) / 7), 2)
            },
            "%w": function (e) {
                return e.zb
            },
            "%W": function (e) {
                var n = new Date(e.Ia, 0, 1),
                    t = 1 === n.getDay() ? n : de(n, 0 === n.getDay() ? 1 : 7 - n.getDay() + 1);
                return e = new Date(e.Ia + 1900, e.gb, e.kb), 0 > o(t, e) ? i(Math.ceil((31 - t.getDate() + (ae($d(
                        e.getFullYear()) ? be : ce, e.getMonth() - 1) - 31) + e.getDate()) / 7), 2) : 0 === o(t, n) ?
                    "01" : "00"
            },
            "%y": function (e) {
                return (e.Ia + 1900).toString().substring(2)
            },
            "%Y": function (e) {
                return e.Ia + 1900
            },
            "%z": function (e) {
                e = e.Xc;
                var n = 0 <= e;
                return e = Math.abs(e) / 60, (n ? "+" : "-") + String("0000" + (e / 60 * 100 + e % 60)).slice(-4)
            },
            "%Z": function (e) {
                return e.$c
            },
            "%%": function () {
                return "%"
            }
        }, f) t.includes(s) && (t = t.replace(new RegExp(s, "g"), f[s](r)));
    return s = Nb(t, !1), s.length > n ? 0 : (H.set(s, e), s.length - 1)
}

function gc(e, n, t, r) {
    e || (e = this), this.parent = e, this.Ua = e.Ua, this.ub = null, this.id = Zb++, this.name = n, this.mode = t,
        this.Da = {}, this.Ba = {}, this.rdev = r
}
q || N.qc(), Object.defineProperties(gc.prototype, {
        read: {
            get: function () {
                return 365 === (365 & this.mode)
            },
            set: function (e) {
                e ? this.mode |= 365 : this.mode &= -366
            }
        },
        write: {
            get: function () {
                return 146 === (146 & this.mode)
            },
            set: function (e) {
                e ? this.mode |= 146 : this.mode &= -147
            }
        }
    }), Ya(), $b = Array(4096), nc(R, "/"), S("/tmp"), S("/home"), S("/home/<USER>"),
    function () {
        S("/dev"), Lb(259, {
            read: function () {
                return 0
            },
            write: function (e, n, t, r) {
                return r
            }
        }), pc("/dev/null", 259), Kb(1280, Ob), Kb(1536, Pb), pc("/dev/tty", 1280), pc("/dev/tty1", 1536);
        var e = Hb();
        Za("random", e), Za("urandom", e), S("/dev/shm"), S("/dev/shm/tmp")
    }(),
    function () {
        S("/proc");
        var e = S("/proc/self");
        S("/proc/self/fd"), nc({
            Ua: function () {
                var n = Tb(e, "fd", 16895, 73);
                return n.Da = {
                    lookup: function (e, n) {
                        var t = Yb[+n];
                        if (!t) throw new Q(8);
                        return e = {
                            parent: null,
                            Ua: {
                                Xb: "fake"
                            },
                            Da: {
                                readlink: function () {
                                    return t.path
                                }
                            }
                        }, e.parent = e
                    }
                }, n
            }
        }, "/proc/self/fd")
    }(), Ic = f.InternalError = Hc("InternalError");
for (var fe = Array(256), ge = 0; 256 > ge; ++ge) fe[ge] = String.fromCharCode(ge);
Mc = fe, Nc = f.BindingError = Hc("BindingError"), Wc.prototype.isAliasOf = function (e) {
    if (!(this instanceof Wc && e instanceof Wc)) return !1;
    var n = this.Aa.Ha.Fa,
        t = this.Aa.Ea,
        r = e.Aa.Ha.Fa;
    for (e = e.Aa.Ea; n.Ma;) t = n.lb(t), n = n.Ma;
    for (; r.Ma;) e = r.lb(e), r = r.Ma;
    return n === r && t === e
}, Wc.prototype.clone = function () {
    if (this.Aa.Ea || Oc(this), this.Aa.ib) return this.Aa.count.value += 1, this;
    var e = Sc,
        n = Object,
        t = n.create,
        r = Object.getPrototypeOf(this),
        a = this.Aa;
    return e = e(t.call(n, r, {
        Aa: {
            value: {
                count: a.count,
                bb: a.bb,
                ib: a.ib,
                Ea: a.Ea,
                Ha: a.Ha,
                Ja: a.Ja,
                La: a.La
            }
        }
    })), e.Aa.count.value += 1, e.Aa.bb = !1, e
}, Wc.prototype["delete"] = function () {
    this.Aa.Ea || Oc(this), this.Aa.bb && !this.Aa.ib && W("Object already scheduled for deletion"), Qc(this), Rc(
        this.Aa), this.Aa.ib || (this.Aa.Ja = void 0, this.Aa.Ea = void 0)
}, Wc.prototype.isDeleted = function () {
    return !this.Aa.Ea
}, Wc.prototype.deleteLater = function () {
    return this.Aa.Ea || Oc(this), this.Aa.bb && !this.Aa.ib && W("Object already scheduled for deletion"), Uc.push(
        this), 1 === Uc.length && Tc && Tc(Vc), this.Aa.bb = !0, this
}, X.prototype.jc = function (e) {
    return this.Zb && (e = this.Zb(e)), e
}, X.prototype.Ob = function (e) {
    this.Va && this.Va(e)
}, X.prototype.argPackAdvance = 8, X.prototype.readValueFromPointer = Bc, X.prototype.deleteObject = function (e) {
    null !== e && e["delete"]()
}, X.prototype.fromWireType = function (e) {
    function n() {
        return this.rb ? kd(this.Fa.cb, {
            Ha: this.yc,
            Ea: t,
            La: this,
            Ja: e
        }) : kd(this.Fa.cb, {
            Ha: this,
            Ea: e
        })
    }
    var t = this.jc(e);
    if (!t) return this.Ob(e), null;
    var r = jd(this.Fa, t);
    if (void 0 !== r) return 0 === r.Aa.count.value ? (r.Aa.Ea = t, r.Aa.Ja = e, r.clone()) : (r = r.clone(), this.Ob(
        e), r);
    if (r = this.Fa.hc(t), r = Xc[r], !r) return n.call(this);
    r = this.qb ? r.bc : r.pointerType;
    var a = gd(t, this.Fa, r.Fa);
    return null === a ? n.call(this) : this.rb ? kd(r.Fa.cb, {
        Ha: r,
        Ea: a,
        La: this,
        Ja: e
    }) : kd(r.Fa.cb, {
        Ha: r,
        Ea: a
    })
}, f.getInheritedInstanceCount = function () {
    return Object.keys(hd).length
}, f.getLiveInheritedInstances = function () {
    var e, n = [];
    for (e in hd) hd.hasOwnProperty(e) && n.push(hd[e]);
    return n
}, f.flushPendingDeletes = Vc, f.setDelayFunction = function (e) {
    Tc = e, Uc.length && Tc && Tc(Vc)
}, nd = f.UnboundTypeError = Hc("UnboundTypeError"), f.count_emval_handles = function () {
    for (var e = 0, n = 5; n < Z.length; ++n) void 0 !== Z[n] && ++e;
    return e
}, f.get_first_emval = function () {
    for (var e = 5; e < Z.length; ++e)
        if (void 0 !== Z[e]) return Z[e];
    return null
};
var he = [null, function (e, n) {
    if (q) return T(1, 1, e, n)
}, xc, yc, Hd, Td, Ud, Vd, Wd, Xd, Yd];

function Nb(e, n) {
    var t = Array(Ea(e) + 1);
    return e = Da(e, t, 0, t.length), n && (t.length = e), t
}
var je = {
    b: function (e, n, t, r) {
        B("Assertion failed: " + Ca(e) + ", at: " + [n ? Ca(n) : "unknown filename", t, r ? Ca(r) :
            "unknown function"])
    },
    m: function (e) {
        return O(e + 16) + 16
    },
    u: function (e, n) {
        N.Jb.push((function () {
            L.get(e)(n)
        }))
    },
    l: function (e, n, t) {
        throw new Bb(e).oc(n, t), Cb++, e
    },
    R: xc,
    Q: yc,
    y: function (e) {
        var n = zc[e];
        delete zc[e];
        var t = n.Hb,
            r = n.Va,
            a = n.Rb,
            i = a.map((function (e) {
                return e.mc
            })).concat(a.map((function (e) {
                return e.Pc
            })));
        Kc([e], i, (function (e) {
            var i = {};
            return a.forEach((function (n, t) {
                var r = e[t],
                    o = n.kc,
                    u = n.lc,
                    c = e[t + a.length],
                    f = n.Oc,
                    s = n.Qc;
                i[n.fc] = {
                    read: function (e) {
                        return r.fromWireType(o(u, e))
                    },
                    write: function (e, n) {
                        var t = [];
                        f(s, e, c.toWireType(t, n)), Ac(t)
                    }
                }
            })), [{
                name: n.name,
                fromWireType: function (e) {
                    var n, t = {};
                    for (n in i) t[n] = i[n].read(e);
                    return r(e), t
                },
                toWireType: function (e, n) {
                    for (var a in i)
                        if (!(a in n)) throw new TypeError('Missing field:  "' + a +
                            '"');
                    var o = t();
                    for (a in i) i[a].write(o, n[a]);
                    return null !== e && e.push(r, o), o
                },
                argPackAdvance: 8,
                readValueFromPointer: Bc,
                Na: r
            }]
        }))
    },
    A: function () {},
    V: function (e, n, t, r, a) {
        var i = Lc(t);
        n = V(n), U(e, {
            name: n,
            fromWireType: function (e) {
                return !!e
            },
            toWireType: function (e, n) {
                return n ? r : a
            },
            argPackAdvance: 8,
            readValueFromPointer: function (e) {
                if (1 === t) var r = H;
                else if (2 === t) r = Ia;
                else {
                    if (4 !== t) throw new TypeError("Unknown boolean type size: " + n);
                    r = G
                }
                return this.fromWireType(r[e >> i])
            },
            Na: null
        })
    },
    x: function (e, n, t, r, a, i, o, u, c, f, s, l, d) {
        s = V(s), i = Y(a, i), u && (u = Y(o, u)), f && (f = Y(c, f)), d = Y(l, d);
        var h = Fc(s);
        Zc(h, (function () {
            sd("Cannot construct " + s + " due to unbound types", [r])
        })), Kc([e, n, t], r ? [r] : [], (function (n) {
            if (n = n[0], r) var t = n.Fa,
                a = t.cb;
            else a = Wc.prototype;
            n = Gc(h, (function () {
                if (Object.getPrototypeOf(this) !== o) throw new Nc(
                    "Use 'new' to construct " + s);
                if (void 0 === c.Za) throw new Nc(s + " has no accessible constructor");
                var e = c.Za[arguments.length];
                if (void 0 === e) throw new Nc("Tried to invoke ctor of " + s +
                    " with invalid number of parameters (" + arguments.length +
                    ") - expected (" + Object.keys(c.Za).toString() +
                    ") parameters instead!");
                return e.apply(this, arguments)
            }));
            var o = Object.create(a, {
                constructor: {
                    value: n
                }
            });
            n.prototype = o;
            var c = new $c(s, n, o, d, t, i, u, f);
            t = new X(s, c, !0, !1), a = new X(s + "*", c, !1, !1);
            var l = new X(s + " const*", c, !1, !0);
            return Xc[e] = {
                pointerType: a,
                bc: l
            }, ld(h, n), [t, a, l]
        }))
    },
    w: function (e, n, t, r, a, i) {
        assert(0 < n);
        var o = td(n, t);
        a = Y(r, a);
        var u = [i],
            c = [];
        Kc([], [e], (function (e) {
            e = e[0];
            var t = "constructor " + e.name;
            if (void 0 === e.Fa.Za && (e.Fa.Za = []), void 0 !== e.Fa.Za[n - 1]) throw new Nc(
                "Cannot register multiple constructors with identical number of parameters (" +
                (n - 1) + ") for class '" + e.name +
                "'! Overload resolution is currently only performed using the parameter count, not actual type info!"
            );
            return e.Fa.Za[n - 1] = function () {
                sd("Cannot construct " + e.name + " due to unbound types", o)
            }, Kc([], o, (function (r) {
                return e.Fa.Za[n - 1] = function () {
                    arguments.length !== n - 1 && W(t + " called with " + arguments
                            .length + " arguments, expected " + (n - 1)), c.length =
                        0, u.length = n;
                    for (var e = 1; e < n; ++e) u[e] = r[e].toWireType(c, arguments[
                        e - 1]);
                    return e = a.apply(null, u), Ac(c), r[0].fromWireType(e)
                }, []
            })), []
        }))
    },
    h: function (e, n, t, r, a, i, o, u) {
        var c = td(t, r);
        n = V(n), i = Y(a, i), Kc([], [e], (function (e) {
            function r() {
                sd("Cannot call " + a + " due to unbound types", c)
            }
            e = e[0];
            var a = e.name + "." + n;
            n.startsWith("@@") && (n = Symbol[n.substring(2)]), u && e.Fa.Ac.push(n);
            var f = e.Fa.cb,
                s = f[n];
            return void 0 === s || void 0 === s.Pa && s.className !== e.name && s.ob === t - 2 ? (r
                    .ob = t - 2, r.className = e.name, f[n] = r) : (Yc(f, n, a), f[n].Pa[t - 2] = r),
                Kc([], c, (function (r) {
                    var u = a,
                        c = e,
                        s = i,
                        l = r.length;
                    2 > l && W(
                        "argTypes array size mismatch! Must at least get return value and 'this' types!"
                    );
                    var d = null !== r[1] && null !== c,
                        h = !1;
                    for (c = 1; c < r.length; ++c)
                        if (null !== r[c] && void 0 === r[c].Na) {
                            h = !0;
                            break
                        } var p = "void" !== r[0].name,
                        b = "",
                        m = "";
                    for (c = 0; c < l - 2; ++c) b += (0 !== c ? ", " : "") + "arg" + c, m +=
                        (0 !== c ? ", " : "") + "arg" + c + "Wired";
                    u = "return function " + Fc(u) + "(" + b +
                        ") {\nif (arguments.length !== " + (l - 2) +
                        ") {\nthrowBindingError('function " + u +
                        " called with ' + arguments.length + ' arguments, expected " + (l -
                            2) + " args!');\n}\n", h && (u += "var destructors = [];\n");
                    var v = h ? "destructors" : "null";
                    for (b =
                        "throwBindingError invoker fn runDestructors retType classParam".split(
                            " "), s = [W, s, o, Ac, r[0], r[1]], d && (u +=
                            "var thisWired = classParam.toWireType(" + v + ", this);\n"), c =
                        0; c < l - 2; ++c) u += "var arg" + c + "Wired = argType" + c +
                        ".toWireType(" + v + ", arg" + c + "); // " + r[c + 2].name + "\n",
                        b.push("argType" + c), s.push(r[c + 2]);
                    if (d && (m = "thisWired" + (0 < m.length ? ", " : "") + m), u += (p ?
                            "var rv = " : "") + "invoker(fn" + (0 < m.length ? ", " : "") +
                        m + ");\n", h) u += "runDestructors(destructors);\n";
                    else
                        for (c = d ? 1 : 2; c < r.length; ++c) l = 1 === c ? "thisWired" :
                            "arg" + (c - 2) + "Wired", null !== r[c].Na && (u += l +
                                "_dtor(" + l + "); // " + r[c].name + "\n", b.push(l +
                                    "_dtor"), s.push(r[c].Na));
                    return p && (u += "var ret = retType.fromWireType(rv);\nreturn ret;\n"),
                        b.push(u + "}\n"), r = ud(b).apply(null, s), void 0 === f[n].Pa ? (
                            r.ob = t - 2, f[n] = r) : f[n].Pa[t - 2] = r, []
                })), []
        }))
    },
    U: function (e, n) {
        n = V(n), U(e, {
            name: n,
            fromWireType: function (e) {
                var n = Z[e].value;
                return 4 < e && 0 === --Z[e].Dc && (Z[e] = void 0, vd.push(e)), n
            },
            toWireType: function (e, n) {
                return ed(n)
            },
            argPackAdvance: 8,
            readValueFromPointer: Bc,
            Na: null
        })
    },
    s: function (e, n, t) {
        t = Lc(t), n = V(n), U(e, {
            name: n,
            fromWireType: function (e) {
                return e
            },
            toWireType: function (e, n) {
                if ("number" !== typeof n && "boolean" !== typeof n) throw new TypeError(
                    'Cannot convert "' + cd(n) + '" to ' + this.name);
                return n
            },
            argPackAdvance: 8,
            readValueFromPointer: wd(n, t),
            Na: null
        })
    },
    e: function (e, n, t, r, a) {
        function i(e) {
            return e
        }
        n = V(n), -1 === a && (a = 4294967295);
        var o = Lc(t);
        if (0 === r) {
            var u = 32 - 8 * t;
            i = function (e) {
                return e << u >>> u
            }
        }
        var c = n.includes("unsigned");
        U(e, {
            name: n,
            fromWireType: i,
            toWireType: function (e, t) {
                if ("number" !== typeof t && "boolean" !== typeof t) throw new TypeError(
                    'Cannot convert "' + cd(t) + '" to ' + this.name);
                if (t < r || t > a) throw new TypeError('Passing a number "' + cd(t) +
                    '" from JS side to C/C++ side to an argument of type "' + n +
                    '", which is outside the valid range [' + r + ", " + a + "]!");
                return c ? t >>> 0 : 0 | t
            },
            argPackAdvance: 8,
            readValueFromPointer: xd(n, o, 0 !== r),
            Na: null
        })
    },
    d: function (e, n, t) {
        function r(e) {
            return e >>= 2, new a(Oa, J[e + 1], J[e])
        }
        var a = [Int8Array, Uint8Array, Int16Array, Uint16Array, Int32Array, Uint32Array, Float32Array,
            Float64Array][n];
        t = V(t), U(e, {
            name: t,
            fromWireType: r,
            argPackAdvance: 8,
            readValueFromPointer: r
        }, {
            nc: !0
        })
    },
    t: function (e, n) {
        n = V(n);
        var t = "std::string" === n;
        U(e, {
            name: n,
            fromWireType: function (e) {
                var n = J[e >> 2];
                if (t)
                    for (var r = e + 4, a = 0; a <= n; ++a) {
                        var i = e + 4 + a;
                        if (a == n || 0 == F[i]) {
                            if (r = Ca(r, i - r), void 0 === o) var o = r;
                            else o += String.fromCharCode(0), o += r;
                            r = i + 1
                        }
                    } else {
                        for (o = Array(n), a = 0; a < n; ++a) o[a] = String.fromCharCode(F[e + 4 +
                            a]);
                        o = o.join("")
                    }
                return P(e), o
            },
            toWireType: function (e, n) {
                n instanceof ArrayBuffer && (n = new Uint8Array(n));
                var r = "string" === typeof n;
                r || n instanceof Uint8Array || n instanceof Uint8ClampedArray || n instanceof Int8Array ||
                    W("Cannot pass non-string to std::string");
                var a = (t && r ? function () {
                        return Ea(n)
                    } : function () {
                        return n.length
                    })(),
                    i = O(4 + a + 1);
                if (J[i >> 2] = a, t && r) Da(n, F, i + 4, a + 1);
                else if (r)
                    for (r = 0; r < a; ++r) {
                        var o = n.charCodeAt(r);
                        255 < o && (P(i), W(
                            "String has UTF-16 code units that do not fit in 8 bits")), F[i + 4 +
                            r] = o
                    } else
                        for (r = 0; r < a; ++r) F[i + 4 + r] = n[r];
                return null !== e && e.push(P, i), i
            },
            argPackAdvance: 8,
            readValueFromPointer: Bc,
            Na: function (e) {
                P(e)
            }
        })
    },
    k: function (e, n, t) {
        if (t = V(t), 2 === n) var r = Ga,
            a = Ja,
            i = Ka,
            o = function () {
                return Ha
            },
            u = 1;
        else 4 === n && (r = La, a = Ma, i = Na, o = function () {
            return J
        }, u = 2);
        U(e, {
            name: t,
            fromWireType: function (e) {
                for (var t, a = J[e >> 2], i = o(), c = e + 4, f = 0; f <= a; ++f) {
                    var s = e + 4 + f * n;
                    f != a && 0 != i[s >> u] || (c = r(c, s - c), void 0 === t ? t = c : (t +=
                        String.fromCharCode(0), t += c), c = s + n)
                }
                return P(e), t
            },
            toWireType: function (e, r) {
                "string" !== typeof r && W("Cannot pass non-string to C++ string type " + t);
                var o = i(r),
                    c = O(4 + o + n);
                return J[c >> 2] = o >> u, a(r, c + 4, o + n), null !== e && e.push(P, c), c
            },
            argPackAdvance: 8,
            readValueFromPointer: Bc,
            Na: function (e) {
                P(e)
            }
        })
    },
    O: function (e, n, t, r, a, i) {
        zc[e] = {
            name: V(n),
            Hb: Y(t, r),
            Va: Y(a, i),
            Rb: []
        }
    },
    n: function (e, n, t, r, a, i, o, u, c, f) {
        zc[e].Rb.push({
            fc: V(n),
            mc: t,
            kc: Y(r, a),
            lc: i,
            Pc: o,
            Oc: Y(u, c),
            Qc: f
        })
    },
    W: function (e, n) {
        n = V(n), U(e, {
            ld: !0,
            name: n,
            argPackAdvance: 0,
            fromWireType: function () {},
            toWireType: function () {}
        })
    },
    N: function (e, n) {
        if (e == n) postMessage({
            cmd: "processQueuedMainThreadWork"
        });
        else if (q) postMessage({
            targetThread: e,
            cmd: "processThreadQueue"
        });
        else {
            if (e = (e = N.Qa[e]) && e.worker, !e) return;
            e.postMessage({
                cmd: "processThreadQueue"
            })
        }
        return 1
    },
    o: function () {
        B()
    },
    r: function (e, n, t) {
        var r;
        for (yd.length = 0, t >>= 2; r = F[n++];)(r = 105 > r) && 1 & t && t++, yd.push(r ? Qa[t++ >> 1] : G[t]),
            ++t;
        return nb[e].apply(null, yd)
    },
    F: function () {
        p || fa || (ra || (ra = {}), ra[
            "Blocking on the main thread is very dangerous, see https://emscripten.org/docs/porting/pthreads.html#blocking-on-the-main-browser-thread"
            ] || (ra[
            "Blocking on the main thread is very dangerous, see https://emscripten.org/docs/porting/pthreads.html#blocking-on-the-main-browser-thread"
            ] = 1, C(
            "Blocking on the main thread is very dangerous, see https://emscripten.org/docs/porting/pthreads.html#blocking-on-the-main-browser-thread"
        )))
    },
    q: function () {},
    g: function (e, n, t) {
        if (0 >= e || e > H.length || 1 & e) return -28;
        if (ea) {
            if (Atomics.load(G, e >> 2) != n) return -6;
            var r = performance.now();
            for (t = r + t, Atomics.exchange(G, pb >> 2, e);;) {
                if (r = performance.now(), r > t) return Atomics.exchange(G, pb >> 2, 0), -73;
                if (r = Atomics.exchange(G, pb >> 2, 0), 0 == r) break;
                if (vb(), Atomics.load(G, e >> 2) != n) return -6;
                Atomics.exchange(G, pb >> 2, e)
            }
            return 0
        }
        if (e = Atomics.wait(G, e >> 2, n, t), "timed-out" === e) return -73;
        if ("not-equal" === e) return -6;
        if ("ok" === e) return 0;
        throw "Atomics.wait returned an unexpected value " + e
    },
    f: ob,
    S: function () {
        return F.length
    },
    i: Ab,
    D: function (e, n, t) {
        F.copyWithin(e, n, n + t)
    },
    T: function () {
        return p ? require("os").cpus().length : navigator.hardwareConcurrency
    },
    K: function (e, n, t) {
        Cd.length = n, t >>= 3;
        for (var r = 0; r < n; r++) Cd[r] = Qa[t + r];
        return (0 > e ? nb[-e - 1] : he[e]).apply(null, Cd)
    },
    E: function () {
        B("OOM")
    },
    L: function (e, n, t) {
        return Ed(e) ? Fd(e, n, t) : Hd(e, n, t)
    },
    p: function () {},
    M: function (e, n) {
        return n >>= 2, n = {
            alpha: !!G[n],
            depth: !!G[n + 1],
            stencil: !!G[n + 2],
            antialias: !!G[n + 3],
            premultipliedAlpha: !!G[n + 4],
            preserveDrawingBuffer: !!G[n + 5],
            powerPreference: Pd[G[n + 6]],
            failIfMajorPerformanceCaveat: !!G[n + 7],
            uc: G[n + 8],
            md: G[n + 9],
            Pb: G[n + 10],
            ec: G[n + 11],
            pd: G[n + 12],
            qd: G[n + 13]
        }, e = Ed(e), !e || n.ec ? 0 : Ld(e, n)
    },
    H: Td,
    I: Ud,
    c: function (e) {
        xb(e)
    },
    P: Vd,
    J: Wd,
    z: Xd,
    j: Yd,
    C: function () {
        N.rc()
    },
    a: E,
    v: function (e, n, t, r) {
        if ("undefined" === typeof SharedArrayBuffer) return C(
            "Current environment does not support SharedArrayBuffer, pthreads are not available!"), 6;
        if (!e) return C("pthread_create called with a null thread pointer!"), 28;
        var a = [];
        if (q && 0 === a.length) return ie(687865856, e, n, t, r);
        var i = 0,
            o = 0;
        if (n && -1 != n) {
            var u = G[n >> 2];
            u += 81920, i = G[n + 8 >> 2], o = 0 !== G[n + 12 >> 2]
        } else u = 2097152;
        (n = 0 == i) ? i = Rb(16, u): (i -= u, assert(0 < i));
        for (var c = O(228), f = 0; 57 > f; ++f) J[(c >> 2) + f] = 0;
        return G[e >> 2] = c, G[c + 12 >> 2] = c, e = c + 152, G[e >> 2] = e, t = {
            ab: i,
            fb: u,
            Cb: n,
            detached: o,
            Sc: t,
            wb: c,
            hb: r,
            ad: a
        }, q ? (t.dd = "spawnThread", postMessage(t, a), 0) : wb(t)
    },
    B: function () {},
    G: function (e, n, t, r) {
        return ee(e, n, t, r)
    }
};
(function () {
    function e(e, n) {
        if (f.asm = e.exports, L = f.asm.ba, Ta.unshift(f.asm.X), wasmModule = n, N.Kb.push(f.asm.ca), xa = n, !q) {
            var t = N.Ta.length;
            N.Ta.forEach((function (e) {
                N.Wb(e, (function () {
                    if (!--t && (eb--, f.monitorRunDependencies && f.monitorRunDependencies(
                            eb), 0 == eb && (null !== fb && (clearInterval(fb), fb =
                            null), gb))) {
                        var e = gb;
                        gb = null, e()
                    }
                }))
            }))
        }
    }

    function n(n) {
        e(n.instance, n.module)
    }

    function t(e) {
        return jb().then((function (e) {
            return WebAssembly.instantiate(e, r)
        })).then(e, (function (e) {
            C("failed to asynchronously prepare wasm: " + e), B(e)
        }))
    }
    var r = {
        a: je
    };
    if (q || (eb++, f.monitorRunDependencies && f.monitorRunDependencies(eb)), f.instantiateWasm) try {
        return f.instantiateWasm(r, e)
    } catch (a) {
        return C("Module.instantiateWasm callback failed with error: " + a), !1
    }(function () {
        ua || "function" !== typeof WebAssembly.instantiateStreaming || hb() || M.startsWith("file://") ||
            "function" !== typeof fetch ? t(n) : fetch(M, {
                credentials: "same-origin"
            }).then((function (e) {
                return WebAssembly.instantiateStreaming(e, r).then(n, (function (e) {
                    return C("wasm streaming compile failed: " + e), C(
                        "falling back to ArrayBuffer instantiation"), t(n)
                }))
            }))
    })()
})(), f.___wasm_call_ctors = function () {
    return (f.___wasm_call_ctors = f.asm.X).apply(null, arguments)
}, f.__malloc = function () {
    return (f.__malloc = f.asm.Y).apply(null, arguments)
};
var O = f._malloc = function () {
    return (O = f._malloc = f.asm.Z).apply(null, arguments)
};
f.__free = function () {
    return (f.__free = f.asm._).apply(null, arguments)
};
var P = f._free = function () {
    return (P = f._free = f.asm.$).apply(null, arguments)
};
f._EgdeEnhance = function () {
    return (f._EgdeEnhance = f.asm.aa).apply(null, arguments)
}, f._emscripten_tls_init = function () {
    return (f._emscripten_tls_init = f.asm.ca).apply(null, arguments)
};
var rd = f.___getTypeName = function () {
    return (rd = f.___getTypeName = f.asm.da).apply(null, arguments)
};
f.___embind_register_native_and_builtin_types = function () {
    return (f.___embind_register_native_and_builtin_types = f.asm.ea).apply(null, arguments)
}, f._emscripten_current_thread_process_queued_calls = function () {
    return (f._emscripten_current_thread_process_queued_calls = f.asm.fa).apply(null, arguments)
};
var rb = f._emscripten_register_main_browser_thread_id = function () {
        return (rb = f._emscripten_register_main_browser_thread_id = f.asm.ga).apply(null, arguments)
    },
    mb = f.__emscripten_do_dispatch_to_thread = function () {
        return (mb = f.__emscripten_do_dispatch_to_thread = f.asm.ha).apply(null, arguments)
    },
    ie = f._emscripten_sync_run_in_main_thread_4 = function () {
        return (ie = f._emscripten_sync_run_in_main_thread_4 = f.asm.ia).apply(null, arguments)
    },
    vb = f._emscripten_main_thread_process_queued_calls = function () {
        return (vb = f._emscripten_main_thread_process_queued_calls = f.asm.ja).apply(null, arguments)
    },
    Bd = f._emscripten_run_in_main_runtime_thread_js = function () {
        return (Bd = f._emscripten_run_in_main_runtime_thread_js = f.asm.ka).apply(null, arguments)
    },
    Gd = f.__emscripten_call_on_thread = function () {
        return (Gd = f.__emscripten_call_on_thread = f.asm.la).apply(null, arguments)
    },
    qb = f.__emscripten_thread_init = function () {
        return (qb = f.__emscripten_thread_init = f.asm.ma).apply(null, arguments)
    },
    Zd = f._emscripten_get_global_libc = function () {
        return (Zd = f._emscripten_get_global_libc = f.asm.na).apply(null, arguments)
    },
    sb = f.___pthread_tsd_run_dtors = function () {
        return (sb = f.___pthread_tsd_run_dtors = f.asm.oa).apply(null, arguments)
    },
    tb = f._pthread_self = function () {
        return (tb = f._pthread_self = f.asm.pa).apply(null, arguments)
    },
    zd = f.stackSave = function () {
        return (zd = f.stackSave = f.asm.qa).apply(null, arguments)
    },
    zb = f.stackRestore = function () {
        return (zb = f.stackRestore = f.asm.ra).apply(null, arguments)
    },
    Ad = f.stackAlloc = function () {
        return (Ad = f.stackAlloc = f.asm.sa).apply(null, arguments)
    },
    yb = f._emscripten_stack_set_limits = function () {
        return (yb = f._emscripten_stack_set_limits = f.asm.ta).apply(null, arguments)
    },
    Rb = f._memalign = function () {
        return (Rb = f._memalign = f.asm.ua).apply(null, arguments)
    };
f.dynCall_viijii = function () {
    return (f.dynCall_viijii = f.asm.va).apply(null, arguments)
}, f.dynCall_jiji = function () {
    return (f.dynCall_jiji = f.asm.wa).apply(null, arguments)
}, f.dynCall_iiiiij = function () {
    return (f.dynCall_iiiiij = f.asm.xa).apply(null, arguments)
}, f.dynCall_iiiiijj = function () {
    return (f.dynCall_iiiiijj = f.asm.ya).apply(null, arguments)
}, f.dynCall_iiiiiijj = function () {
    return (f.dynCall_iiiiiijj = f.asm.za).apply(null, arguments)
};
var ke, ub = f.__emscripten_allow_main_runtime_queued_calls = 54380,
    pb = f.__emscripten_main_thread_futex = 59664;

function na(e) {
    this.name = "ExitStatus", this.message = "Program terminated with exit(" + e + ")", this.status = e
}

function me() {
    function e() {
        if (!ke && (ke = !0, f.calledRun = !0, !ya) && (Wa(), f.onRuntimeInitialized && f.onRuntimeInitialized(), !q)) {
            if (f.postRun)
                for ("function" == typeof f.postRun && (f.postRun = [f.postRun]); f.postRun.length;) {
                    var e = f.postRun.shift();
                    Ua.unshift(e)
                }
            cb(Ua)
        }
    }
    if (!(0 < eb))
        if (q) Wa(), postMessage({
            cmd: "loaded"
        });
        else {
            if (!q) {
                if (f.preRun)
                    for ("function" == typeof f.preRun && (f.preRun = [f.preRun]); f.preRun.length;) db();
                cb(Sa)
            }
            0 < eb || (f.setStatus ? (f.setStatus("Running..."), setTimeout((function () {
                setTimeout((function () {
                    f.setStatus("")
                }), 1), e()
            }), 1)) : e())
        }
}

function xb(e) {
    if (q) throw postMessage({
        cmd: "exitProcess",
        returnCode: e
    }), new na(e);
    oa() || (N.Tc(), f.onExit && f.onExit(e), ya = !0), da(e, new na(e))
}
if (f.addFunction = function (e, n) {
        if (!ta) {
            ta = new WeakMap;
            for (var t = 0; t < L.length; t++) {
                var r = L.get(t);
                r && ta.set(r, t)
            }
        }
        if (ta.has(e)) e = ta.get(e);
        else {
            if (sa.length) t = sa.pop();
            else {
                try {
                    L.grow(1)
                } catch (u) {
                    if (!(u instanceof RangeError)) throw u;
                    throw "Unable to grow wasm table. Set ALLOW_TABLE_GROWTH."
                }
                t = L.length - 1
            }
            try {
                L.set(t, e)
            } catch (u) {
                if (!(u instanceof TypeError)) throw u;
                if ("function" === typeof WebAssembly.Function) {
                    var a = {
                            i: "i32",
                            j: "i64",
                            f: "f32",
                            d: "f64"
                        },
                        i = {
                            parameters: [],
                            results: "v" == n[0] ? [] : [a[n[0]]]
                        };
                    for (r = 1; r < n.length; ++r) i.parameters.push(a[n[r]]);
                    n = new WebAssembly.Function(i, e)
                } else {
                    a = [1, 0, 1, 96], i = n.slice(0, 1), n = n.slice(1);
                    var o = {
                        i: 127,
                        j: 126,
                        f: 125,
                        d: 124
                    };
                    for (a.push(n.length), r = 0; r < n.length; ++r) a.push(o[n[r]]);
                    "v" == i ? a.push(0) : a = a.concat([1, o[i]]), a[1] = a.length - 2, n = new Uint8Array([0, 97, 115,
                            109, 1, 0, 0, 0].concat(a, [2, 7, 1, 1, 101, 1, 102, 0, 0, 7, 5, 1, 1, 102, 0, 0])), n =
                        new WebAssembly.Module(n), n = new WebAssembly.Instance(n, {
                            e: {
                                f: e
                            }
                        }).exports.f
                }
                L.set(t, n)
            }
            ta.set(e, t), e = t
        }
        return e
    }, f.keepRuntimeAlive = oa, f.PThread = N, f.PThread = N, f.wasmMemory = E, f.ExitStatus = na, gb = function e() {
        ke || me(), ke || (gb = e)
    }, f.run = me, f.preInit)
    for ("function" == typeof f.preInit && (f.preInit = [f.preInit]); 0 < f.preInit.length;) f.preInit.pop()();
q && (noExitRuntime = !1, N.sc()), me();