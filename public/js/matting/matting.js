// 图像处理类
const MattingProcessor = {
  worker: null,
  isBusy: false,
  params: null,
  waitingPromise: {
    resolve: null,
    reject: null,
  },
  isAutoFullSize: false,

  // 初始化Worker
  init() {
    console.log('Starting initialization...')
    if (this.worker) {
      console.log('Worker already exists')
      return Promise.resolve()
    }

    return new Promise((resolve, reject) => {
      try {
        // 创建新的Worker
        this.worker = new Worker('/js/matting/worker.js')
        this.worker.onmessage = this.onmessage.bind(this)
        this.worker.onerror = (e) => {
          console.error('Worker error:', e)
          this.errorHandle(e.message)
          reject(new Error(e.message))
        }

        // 设置初始化回调
        this.initPromise = [resolve, reject]
      } catch (error) {
        console.error('Failed to create worker:', error)
        this.errorHandle('Failed to initialize worker: ' + error.message)
        reject(error)
      }
    })
  },

  // 错误处理
  errorHandle(msg) {
    console.error('Error:', msg)
    if (this.onError) {
      this.onError(msg)
    }
  },

  // 设置自动全尺寸模式
  setAutoFullSize(value) {
    this.isAutoFullSize = value
  },

  // 获取参数下载进度
  getRmbgParamsDownPrecent() {
    // 在实际应用中，这里应该返回下载进度
    return isParamReady.value ? 100 : 0
  },

  // 下载抠图参数
  downRmbgParams() {
    return new Promise((resolve, reject) => {
      // 使用正确的参数文件路径
      const paramFilePath = '/js/matting/matting.tf'

      // 下载参数文件
      fetch(paramFilePath)
        .then((response) => {
          if (!response.ok) {
            throw new Error(
              `Failed to download parameters: ${response.status} ${response.statusText}`
            )
          }
          return response.arrayBuffer()
        })
        .then((buffer) => {
          console.log(`Downloaded parameters: ${buffer.byteLength} bytes`)
          this.params = buffer
          isParamReady.value = true
          this.testDataWarmupFinish()
          resolve()
        })
        .catch((error) => {
          console.error('Parameter download error:', error)
          this.errorHandle('Fail to download module: ' + error.message)
          reject(error)
        })
    })
  },

  // 测试数据预热完成
  testDataWarmupFinish() {
    if (this.onDataFinish && isParamReady.value && workerInit.value) {
      this.onDataFinish()
    }
  },

  // 获取图像数据
  getImageData(image) {
    const canvas = document.createElement('canvas')
    canvas.width = image.width
    canvas.height = image.height
    const ctx = canvas.getContext('2d')
    ctx.drawImage(image, 0, 0)
    return ctx.getImageData(0, 0, image.width, image.height)
  },

  // 预测图像
  predict(image, type) {
    if (!this.worker) {
      console.error('Worker not initialized')
      this.errorHandle('Worker not initialized')
      return
    }

    if (!this.params) {
      console.error('Parameters not loaded')
      this.errorHandle('Parameters not loaded')
      return
    }

    console.log('Predicting image:', {
      width: image.width,
      height: image.height,
      type: type,
      hasParams: !!this.params,
      paramsSize: this.params ? this.params.byteLength : 0,
    })

    try {
      const imageData = this.getImageData(image)
      let authorization = null

      // 检查是否需要授权
      if (
        ((type && type.includes('FullSize')) || this.isAutoFullSize) &&
        window.userConfirmed &&
        window.userConfirmed.value
      ) {
        authorization = this.getIdToken()
      }

      console.log('Sending data to worker:', {
        cmd: 'predict',
        width: image.width,
        height: image.height,
        dataSize: imageData.data.buffer.byteLength,
        paramsSize: this.params.byteLength,
        hasAuthorization: !!authorization,
      })

      // 发送消息给Worker
      this.worker.postMessage(
        {
          cmd: 'predict',
          width: image.width,
          height: image.height,
          data: imageData.data.buffer,
          params: this.params,
          Authorization: authorization,
        },
        [imageData.data.buffer]
      )

      // 更新进度
      if (this.onProgress) {
        this.onProgress(0, 1)
      }
    } catch (error) {
      console.error('Error in predict method:', error)
      this.errorHandle('Failed to process image: ' + error.message)
    }
  },

  // 获取ID令牌
  getIdToken() {
    // 在实际应用中，这里应该返回用户的ID令牌
    return null
  },

  // 预测URL
  predictURL(url, userData, type) {
    this.userData = userData
    this.init().then(() => {
      if (typeof url === 'string') {
        this._predictURL(url, type)
      } else {
        this.predict(url, type)
      }
    })
  },

  // 预测URL（内部方法）
  _predictURL(url, type) {
    console.log('Loading image from URL for prediction:', {
      url:
        typeof url === 'string'
          ? url.length > 50
            ? url.substring(0, 50) + '...'
            : url
          : 'Image object',
      type,
    })

    const img = new Image()
    img.crossOrigin = 'anonymous'

    img.onload = () => {
      console.log('Image loaded from URL successfully:', {
        width: img.width,
        height: img.height,
      })
      this.predict(img, type)
    }

    img.onerror = (error) => {
      console.error('Failed to load image from URL:', error)
      this.errorHandle(
        'Failed to load image: ' +
          (typeof url === 'string' ? url : 'provided image')
      )
    }

    if (typeof url === 'string') {
      img.src = url
    } else {
      // 如果传入的是一个已经加载的Image对象
      this.predict(url, type)
    }
  },

  // 停止处理
  stop() {
    console.log('Stopping worker and cleaning up resources...')
    if (this.worker) {
      try {
        // 终止worker
        this.worker.terminate()
        this.worker = null
      } catch (error) {
        console.error('Error terminating worker:', error)
      }

      // 重置状态
      this.isBusy = false
      this.waitingPromise.resolve = null
      this.waitingPromise.reject = null

      // 保留参数，不需要重新下载
      // this.params = null;
      // isParamReady.value = false;

      workerInit.value = false
    }
  },

  // 预热
  warmup() {
    console.log('Starting warmup...')

    // 如果已经有worker在运行，先停止
    if (this.worker) {
      console.log('Worker already exists, stopping it first...')
      try {
        this.worker.terminate()
        this.worker = null
      } catch (error) {
        console.error('Error terminating existing worker:', error)
      }
    }

    // 创建Worker
    try {
      console.log('Creating new worker...')
      this.worker = new Worker('/js/matting/worker.js')

      if (this.worker) {
        this.worker.onmessage = (e) => {
          this.onmessage(e.data)
        }
        this.worker.onerror = (e) => {
          console.error('Worker error during warmup:', e)
          this.errorHandle(e.message)
        }

        // 检查参数是否已经加载
        if (this.params && isParamReady.value) {
          console.log('Parameters already loaded, skipping download')
          // 参数已经加载，不需要重新下载
          setTimeout(() => {
            this.testDataWarmupFinish()
          }, 100)
        } else {
          // 需要下载参数
          console.log('Downloading parameters...')
          this.downRmbgParams()
            .then(() => {
              console.log('Parameters downloaded successfully during warmup')
            })
            .catch((error) => {
              console.error(
                'Failed to download parameters during warmup:',
                error
              )
            })
        }
      } else {
        this.errorHandle('Failed to create worker instance')
      }
    } catch (error) {
      console.error('Error during warmup:', error)
      this.errorHandle('Failed to initialize module: ' + error.message)
    }
  },

  // 处理Worker消息
  onmessage(data) {
    const { cmd } = data
    console.log('Worker message received:', cmd, data)

    switch (cmd) {
      case 'Init':
        console.log(
          'Worker initialized with capabilities:',
          data.useSimd ? 'SIMD' : 'No SIMD',
          data.useThreads ? 'Threads' : 'No Threads'
        )
        workerInit.value = true
        if (this.initPromise) {
          this.initPromise[0]()
          this.initPromise = null
        }
        this.testDataWarmupFinish()
        break

      case 'predictFinish':
        if (this.onCompleted) {
          console.log('Processing finished. Data received:', {
            width: data.width,
            height: data.height,
            dataSize: data.data ? data.data.byteLength : 'unknown',
            useSimd: data.useSimd,
            useThreads: data.useThreads,
          })

          try {
            // 创建一个canvas来处理图像数据
            const canvas = document.createElement('canvas')
            canvas.width = data.width
            canvas.height = data.height
            const ctx = canvas.getContext('2d')

            // 创建ImageData对象
            const imageData = new ImageData(
              new Uint8ClampedArray(data.data),
              data.width,
              data.height
            )

            // 将ImageData绘制到canvas
            ctx.putImageData(imageData, 0, 0)

            // 从canvas获取图像URL
            const url = canvas.toDataURL('image/png')
            console.log('Image URL created successfully')

            this.onCompleted({
              url,
              width: data.width,
              height: data.height,
              userData: this.userData,
            })
          } catch (error) {
            console.error('Error creating image from data:', error)
            this.errorHandle('Failed to create image: ' + error.message)
          }
        }
        break

      case 'progressCB':
        if (this.onProgress) {
          this.onProgress(data.val, data.maxVal)
        }
        break

      case 'error':
        console.error('Worker reported error:', data.msg, data.code)
        this.errorHandle(data.msg || 'Unknown error')
        break

      case 'edgeEnhanceFinish':
      case 'predictHarmFinish':
        if (this.waitingPromise.resolve) {
          try {
            // 创建一个canvas来处理图像数据
            const canvas = document.createElement('canvas')
            canvas.width = data.width
            canvas.height = data.height
            const ctx = canvas.getContext('2d')

            // 创建ImageData对象
            const imageData = new ImageData(
              new Uint8ClampedArray(data.data),
              data.width,
              data.height
            )

            // 将ImageData绘制到canvas
            ctx.putImageData(imageData, 0, 0)

            // 从canvas获取图像URL
            const url = canvas.toDataURL('image/png')

            this.waitingPromise.resolve({
              url,
              width: data.width,
              height: data.height,
            })
          } catch (error) {
            console.error('Error creating image from data:', error)
            if (this.waitingPromise.reject) {
              this.waitingPromise.reject(error)
            }
          } finally {
            this.waitingPromise.resolve = null
            this.waitingPromise.reject = null
          }
        }
        break

      default:
        console.log('Unhandled worker message:', cmd, data)
        break
    }
  },
}
