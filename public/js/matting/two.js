var wasmModule, n, g = "undefined" !== typeof Module ? Module : location.bgsubModule ? location.bgsubModule : {},
    aa = {};
for (n in g) g.hasOwnProperty(n) && (aa[n] = g[n]);
var ba = "./this.program";

function ca(n, t) {
    throw t
}
var ha, ia, ja, y, ka, da = "object" === typeof window,
    ea = "function" === typeof importScripts,
    fa = "object" === typeof process && "object" === typeof process.versions && "string" === typeof process.versions.node,
    w = "";
fa ? (w = ea ? require("path").dirname(w) + "/" : __dirname + "/", ha = function (n, t) {
        return y || (y = require("fs")), ka || (ka = require("path")), n = ka.normalize(n), y.readFileSync(n, t ?
            null : "utf8")
    }, ja = function (n) {
        return n = ha(n, !0), n.buffer || (n = new Uint8Array(n)), n.buffer || z("Assertion failed: undefined"), n
    }, ia = function (n, t, e) {
        y || (y = require("fs")), ka || (ka = require("path")), n = ka.normalize(n), y.readFile(n, (function (n, r) {
            n ? e(n) : t(r.buffer)
        }))
    }, 1 < process.argv.length && (ba = process.argv[1].replace(/\\/g, "/")), process.argv.slice(2), "undefined" !==
    typeof module && (module.exports = g), process.on("uncaughtException", (function (n) {
        if (!(n instanceof na)) throw n
    })), process.on("unhandledRejection", z), ca = function (n, t) {
        if (noExitRuntime || 0 < oa) throw process.exitCode = n, t;
        process.exit(n)
    }, g.inspect = function () {
        return "[Emscripten Module object]"
    }) : (da || ea) && (ea ? w = self.location.href : "undefined" !== typeof document && document.currentScript &&
    (w = document.currentScript.src), w = 0 !== w.indexOf("blob:") ? w.substr(0, w.lastIndexOf("/") + 1) : "", ha =
    function (n) {
        var t = new XMLHttpRequest;
        return t.open("GET", n, !1), t.send(null), t.responseText
    }, ea && (ja = function (n) {
        var t = new XMLHttpRequest;
        return t.open("GET", n, !1), t.responseType = "arraybuffer", t.send(null), new Uint8Array(t.response)
    }), ia = function (n, t, e) {
        var r = new XMLHttpRequest;
        r.open("GET", n, !0), r.responseType = "arraybuffer", r.onload = function () {
            200 == r.status || 0 == r.status && r.response ? t(r.response) : e()
        }, r.onerror = e, r.send(null)
    });
var pa = g.print || console.log.bind(console),
    B = g.printErr || console.warn.bind(console);
for (n in aa) aa.hasOwnProperty(n) && (g[n] = aa[n]);
aa = null, g.thisProgram && (ba = g.thisProgram), g.quit && (ca = g.quit);
var ra, sa, qa = [];
g.wasmBinary && (sa = g.wasmBinary);
var noExitRuntime = g.noExitRuntime || !0;
"object" !== typeof WebAssembly && z("no native wasm support detected");
var ta, ua = !1,
    va = "undefined" !== typeof TextDecoder ? new TextDecoder("utf8") : void 0;

function wa(n, t, e) {
    var r = t + e;
    for (e = t; n[e] && !(e >= r);) ++e;
    if (16 < e - t && n.subarray && va) return va.decode(n.subarray(t, e));
    for (r = ""; t < e;) {
        var a = n[t++];
        if (128 & a) {
            var i = 63 & n[t++];
            if (192 == (224 & a)) r += String.fromCharCode((31 & a) << 6 | i);
            else {
                var o = 63 & n[t++];
                a = 224 == (240 & a) ? (15 & a) << 12 | i << 6 | o : (7 & a) << 18 | i << 12 | o << 6 | 63 & n[t++],
                    65536 > a ? r += String.fromCharCode(a) : (a -= 65536, r += String.fromCharCode(55296 | a >> 10,
                        56320 | 1023 & a))
            }
        } else r += String.fromCharCode(a)
    }
    return r
}

function xa(n, t) {
    return n ? wa(C, n, t) : ""
}

function ya(n, t, e, r) {
    if (!(0 < r)) return 0;
    var a = e;
    r = e + r - 1;
    for (var i = 0; i < n.length; ++i) {
        var o = n.charCodeAt(i);
        if (55296 <= o && 57343 >= o) {
            var u = n.charCodeAt(++i);
            o = 65536 + ((1023 & o) << 10) | 1023 & u
        }
        if (127 >= o) {
            if (e >= r) break;
            t[e++] = o
        } else {
            if (2047 >= o) {
                if (e + 1 >= r) break;
                t[e++] = 192 | o >> 6
            } else {
                if (65535 >= o) {
                    if (e + 2 >= r) break;
                    t[e++] = 224 | o >> 12
                } else {
                    if (e + 3 >= r) break;
                    t[e++] = 240 | o >> 18, t[e++] = 128 | o >> 12 & 63
                }
                t[e++] = 128 | o >> 6 & 63
            }
            t[e++] = 128 | 63 & o
        }
    }
    return t[e] = 0, e - a
}

function za(n) {
    for (var t = 0, e = 0; e < n.length; ++e) {
        var r = n.charCodeAt(e);
        55296 <= r && 57343 >= r && (r = 65536 + ((1023 & r) << 10) | 1023 & n.charCodeAt(++e)), 127 >= r ? ++t : t =
            2047 >= r ? t + 2 : 65535 >= r ? t + 3 : t + 4
    }
    return t
}
var Aa = "undefined" !== typeof TextDecoder ? new TextDecoder("utf-16le") : void 0;

function Ba(n, t) {
    for (var e = n >> 1, r = e + t / 2; !(e >= r) && Ca[e];) ++e;
    if (e <<= 1, 32 < e - n && Aa) return Aa.decode(C.subarray(n, e));
    for (e = "", r = 0; !(r >= t / 2); ++r) {
        var a = Da[n + 2 * r >> 1];
        if (0 == a) break;
        e += String.fromCharCode(a)
    }
    return e
}

function Ea(n, t, e) {
    if (void 0 === e && (e = 2147483647), 2 > e) return 0;
    e -= 2;
    var r = t;
    e = e < 2 * n.length ? e / 2 : n.length;
    for (var a = 0; a < e; ++a) Da[t >> 1] = n.charCodeAt(a), t += 2;
    return Da[t >> 1] = 0, t - r
}

function Fa(n) {
    return 2 * n.length
}

function Ga(n, t) {
    for (var e = 0, r = ""; !(e >= t / 4);) {
        var a = D[n + 4 * e >> 2];
        if (0 == a) break;
        ++e, 65536 <= a ? (a -= 65536, r += String.fromCharCode(55296 | a >> 10, 56320 | 1023 & a)) : r += String.fromCharCode(
            a)
    }
    return r
}

function Ha(n, t, e) {
    if (void 0 === e && (e = 2147483647), 4 > e) return 0;
    var r = t;
    e = r + e - 4;
    for (var a = 0; a < n.length; ++a) {
        var i = n.charCodeAt(a);
        if (55296 <= i && 57343 >= i) {
            var o = n.charCodeAt(++a);
            i = 65536 + ((1023 & i) << 10) | 1023 & o
        }
        if (D[t >> 2] = i, t += 4, t + 4 > e) break
    }
    return D[t >> 2] = 0, t - r
}

function Ia(n) {
    for (var t = 0, e = 0; e < n.length; ++e) {
        var r = n.charCodeAt(e);
        55296 <= r && 57343 >= r && ++e, t += 4
    }
    return t
}
var Ja, F, C, Da, Ca, D, G, Ka, La, H, Ma = [],
    Na = [],
    Oa = [],
    oa = 0;

function Pa() {
    var n = g.preRun.shift();
    Ma.unshift(n)
}
var J, Wa, Xa, I = 0,
    Qa = null,
    Ra = null;

function z(n) {
    throw g.onAbort && g.onAbort(n), B(n), ua = !0, new WebAssembly.RuntimeError("abort(" + n +
        "). Build with -s ASSERTIONS=1 for more info.")
}

function Sa() {
    return J.startsWith("data:application/octet-stream;base64,")
}
if (g.preloadedImages = {}, g.preloadedAudios = {}, J = g.WasmFileUrl, !Sa()) {
    var Ta = J;
    J = g.locateFile ? g.locateFile(Ta, w) : w + Ta
}

function Ua() {
    var n = J;
    try {
        if (n == J && sa) return new Uint8Array(sa);
        if (ja) return ja(n);
        throw "both async and sync fetching of the wasm failed"
    } catch (t) {
        z(t)
    }
}

function Va() {
    if (!sa && (da || ea)) {
        if ("function" === typeof fetch && !J.startsWith("file://")) return fetch(J, {
            credentials: "same-origin"
        }).then((function (n) {
            if (!n.ok) throw "failed to load wasm binary file at '" + J + "'";
            return n.arrayBuffer()
        })).catch((function () {
            return Ua()
        }));
        if (ia) return new Promise((function (n, t) {
            ia(J, (function (t) {
                n(new Uint8Array(t))
            }), t)
        }))
    }
    return Promise.resolve().then((function () {
        return Ua()
    }))
}

function Ya(n) {
    for (; 0 < n.length;) {
        var t = n.shift();
        if ("function" == typeof t) t(g);
        else {
            var e = t.Lb;
            "number" === typeof e ? void 0 === t.La ? H.get(e)() : H.get(e)(t.La) : e(void 0 === t.La ? null : t.La)
        }
    }
}

function Za(n) {
    this.aa = n - 16, this.Bb = function (n) {
        D[this.aa + 4 >> 2] = n
    }, this.yb = function (n) {
        D[this.aa + 8 >> 2] = n
    }, this.zb = function () {
        D[this.aa >> 2] = 0
    }, this.xb = function () {
        F[this.aa + 12 >> 0] = 0
    }, this.Ab = function () {
        F[this.aa + 13 >> 0] = 0
    }, this.lb = function (n, t) {
        this.Bb(n), this.yb(t), this.zb(), this.xb(), this.Ab()
    }
}
var $a = 0;

function ab(n, t) {
    for (var e = 0, r = n.length - 1; 0 <= r; r--) {
        var a = n[r];
        "." === a ? n.splice(r, 1) : ".." === a ? (n.splice(r, 1), e++) : e && (n.splice(r, 1), e--)
    }
    if (t)
        for (; e; e--) n.unshift("..");
    return n
}

function bb(n) {
    var t = "/" === n.charAt(0),
        e = "/" === n.substr(-1);
    return (n = ab(n.split("/").filter((function (n) {
        return !!n
    })), !t).join("/")) || t || (n = "."), n && e && (n += "/"), (t ? "/" : "") + n
}

function cb(n) {
    var t = /^(\/?|)([\s\S]*?)((?:\.{1,2}|[^\/]+?|)(\.[^.\/]*|))(?:[\/]*)$/.exec(n).slice(1);
    return n = t[0], t = t[1], n || t ? (t && (t = t.substr(0, t.length - 1)), n + t) : "."
}

function db(n) {
    if ("/" === n) return "/";
    n = bb(n), n = n.replace(/\/$/, "");
    var t = n.lastIndexOf("/");
    return -1 === t ? n : n.substr(t + 1)
}

function eb() {
    if ("object" === typeof crypto && "function" === typeof crypto.getRandomValues) {
        var n = new Uint8Array(1);
        return function () {
            return crypto.getRandomValues(n), n[0]
        }
    }
    if (fa) try {
        var t = require("crypto");
        return function () {
            return t.randomBytes(1)[0]
        }
    } catch (e) {}
    return function () {
        z("randomDevice")
    }
}

function fb() {
    for (var n = "", t = !1, e = arguments.length - 1; - 1 <= e && !t; e--) {
        if (t = 0 <= e ? arguments[e] : "/", "string" !== typeof t) throw new TypeError(
            "Arguments to path.resolve must be strings");
        if (!t) return "";
        n = t + "/" + n, t = "/" === t.charAt(0)
    }
    return n = ab(n.split("/").filter((function (n) {
        return !!n
    })), !t).join("/"), (t ? "/" : "") + n || "."
}
var gb = [];

function hb(n, t) {
    gb[n] = {
        input: [],
        output: [],
        va: t
    }, ib(n, jb)
}
var jb = {
        open: function (n) {
            var t = gb[n.node.rdev];
            if (!t) throw new K(43);
            n.tty = t, n.seekable = !1
        },
        close: function (n) {
            n.tty.va.flush(n.tty)
        },
        flush: function (n) {
            n.tty.va.flush(n.tty)
        },
        read: function (n, t, e, r) {
            if (!n.tty || !n.tty.va.Ya) throw new K(60);
            for (var a = 0, i = 0; i < r; i++) {
                try {
                    var o = n.tty.va.Ya(n.tty)
                } catch (u) {
                    throw new K(29)
                }
                if (void 0 === o && 0 === a) throw new K(6);
                if (null === o || void 0 === o) break;
                a++, t[e + i] = o
            }
            return a && (n.node.timestamp = Date.now()), a
        },
        write: function (n, t, e, r) {
            if (!n.tty || !n.tty.va.Oa) throw new K(60);
            try {
                for (var a = 0; a < r; a++) n.tty.va.Oa(n.tty, t[e + a])
            } catch (i) {
                throw new K(29)
            }
            return r && (n.node.timestamp = Date.now()), a
        }
    },
    lb = {
        Ya: function (n) {
            if (!n.input.length) {
                var t = null;
                if (fa) {
                    var e = Buffer.alloc(256),
                        r = 0;
                    try {
                        r = y.readSync(process.stdin.fd, e, 0, 256, null)
                    } catch (a) {
                        if (!a.toString().includes("EOF")) throw a;
                        r = 0
                    }
                    t = 0 < r ? e.slice(0, r).toString("utf-8") : null
                } else "undefined" != typeof window && "function" == typeof window.prompt ? (t = window.prompt(
                    "Input: "), null !== t && (t += "\n")) : "function" == typeof readline && (t = readline(),
                    null !== t && (t += "\n"));
                if (!t) return null;
                n.input = kb(t, !0)
            }
            return n.input.shift()
        },
        Oa: function (n, t) {
            null === t || 10 === t ? (pa(wa(n.output, 0)), n.output = []) : 0 != t && n.output.push(t)
        },
        flush: function (n) {
            n.output && 0 < n.output.length && (pa(wa(n.output, 0)), n.output = [])
        }
    },
    mb = {
        Oa: function (n, t) {
            null === t || 10 === t ? (B(wa(n.output, 0)), n.output = []) : 0 != t && n.output.push(t)
        },
        flush: function (n) {
            n.output && 0 < n.output.length && (B(wa(n.output, 0)), n.output = [])
        }
    };

function nb(n) {
    n = 65536 * Math.ceil(n / 65536);
    var t = ob(65536, n);
    return t ? (C.fill(0, t, t + n), t) : 0
}
var L = {
        ka: null,
        na: function () {
            return L.createNode(null, "/", 16895, 0)
        },
        createNode: function (n, t, e, r) {
            if (24576 === (61440 & e) || 4096 === (61440 & e)) throw new K(63);
            return L.ka || (L.ka = {
                    dir: {
                        node: {
                            qa: L.$.qa,
                            ma: L.$.ma,
                            lookup: L.$.lookup,
                            Da: L.$.Da,
                            rename: L.$.rename,
                            unlink: L.$.unlink,
                            rmdir: L.$.rmdir,
                            readdir: L.$.readdir,
                            symlink: L.$.symlink
                        },
                        stream: {
                            sa: L.Y.sa
                        }
                    },
                    file: {
                        node: {
                            qa: L.$.qa,
                            ma: L.$.ma
                        },
                        stream: {
                            sa: L.Y.sa,
                            read: L.Y.read,
                            write: L.Y.write,
                            Ra: L.Y.Ra,
                            Ea: L.Y.Ea,
                            Ga: L.Y.Ga
                        }
                    },
                    link: {
                        node: {
                            qa: L.$.qa,
                            ma: L.$.ma,
                            readlink: L.$.readlink
                        },
                        stream: {}
                    },
                    Sa: {
                        node: {
                            qa: L.$.qa,
                            ma: L.$.ma
                        },
                        stream: pb
                    }
                }), e = qb(n, t, e, r), 16384 === (61440 & e.mode) ? (e.$ = L.ka.dir.node, e.Y = L.ka.dir.stream, e
                    .Z = {}) : 32768 === (61440 & e.mode) ? (e.$ = L.ka.file.node, e.Y = L.ka.file.stream, e.da = 0,
                    e.Z = null) : 40960 === (61440 & e.mode) ? (e.$ = L.ka.link.node, e.Y = L.ka.link.stream) :
                8192 === (61440 & e.mode) && (e.$ = L.ka.Sa.node, e.Y = L.ka.Sa.stream), e.timestamp = Date.now(),
                n && (n.Z[t] = e, n.timestamp = e.timestamp), e
        },
        Mb: function (n) {
            return n.Z ? n.Z.subarray ? n.Z.subarray(0, n.da) : new Uint8Array(n.Z) : new Uint8Array(0)
        },
        Ua: function (n, t) {
            var e = n.Z ? n.Z.length : 0;
            e >= t || (t = Math.max(t, e * (1048576 > e ? 2 : 1.125) >>> 0), 0 != e && (t = Math.max(t, 256)), e =
                n.Z, n.Z = new Uint8Array(t), 0 < n.da && n.Z.set(e.subarray(0, n.da), 0))
        },
        vb: function (n, t) {
            if (n.da != t)
                if (0 == t) n.Z = null, n.da = 0;
                else {
                    var e = n.Z;
                    n.Z = new Uint8Array(t), e && n.Z.set(e.subarray(0, Math.min(t, n.da))), n.da = t
                }
        },
        $: {
            qa: function (n) {
                var t = {};
                return t.dev = 8192 === (61440 & n.mode) ? n.id : 1, t.ino = n.id, t.mode = n.mode, t.nlink = 1, t.uid =
                    0, t.gid = 0, t.rdev = n.rdev, 16384 === (61440 & n.mode) ? t.size = 4096 : 32768 === (61440 &
                        n.mode) ? t.size = n.da : 40960 === (61440 & n.mode) ? t.size = n.link.length : t.size = 0,
                    t.atime = new Date(n.timestamp), t.mtime = new Date(n.timestamp), t.ctime = new Date(n.timestamp),
                    t.ab = 4096, t.blocks = Math.ceil(t.size / t.ab), t
            },
            ma: function (n, t) {
                void 0 !== t.mode && (n.mode = t.mode), void 0 !== t.timestamp && (n.timestamp = t.timestamp), void 0 !==
                    t.size && L.vb(n, t.size)
            },
            lookup: function () {
                throw rb[44]
            },
            Da: function (n, t, e, r) {
                return L.createNode(n, t, e, r)
            },
            rename: function (n, t, e) {
                if (16384 === (61440 & n.mode)) {
                    try {
                        var r = sb(t, e)
                    } catch (i) {}
                    if (r)
                        for (var a in r.Z) throw new K(55)
                }
                delete n.parent.Z[n.name], n.parent.timestamp = Date.now(), n.name = e, t.Z[e] = n, t.timestamp = n
                    .parent.timestamp, n.parent = t
            },
            unlink: function (n, t) {
                delete n.Z[t], n.timestamp = Date.now()
            },
            rmdir: function (n, t) {
                var e, r = sb(n, t);
                for (e in r.Z) throw new K(55);
                delete n.Z[t], n.timestamp = Date.now()
            },
            readdir: function (n) {
                var t, e = [".", ".."];
                for (t in n.Z) n.Z.hasOwnProperty(t) && e.push(t);
                return e
            },
            symlink: function (n, t, e) {
                return n = L.createNode(n, t, 41471, 0), n.link = e, n
            },
            readlink: function (n) {
                if (40960 !== (61440 & n.mode)) throw new K(28);
                return n.link
            }
        },
        Y: {
            read: function (n, t, e, r, a) {
                var i = n.node.Z;
                if (a >= n.node.da) return 0;
                if (n = Math.min(n.node.da - a, r), 8 < n && i.subarray) t.set(i.subarray(a, a + n), e);
                else
                    for (r = 0; r < n; r++) t[e + r] = i[a + r];
                return n
            },
            write: function (n, t, e, r, a, i) {
                if (!r) return 0;
                if (n = n.node, n.timestamp = Date.now(), t.subarray && (!n.Z || n.Z.subarray)) {
                    if (i) return n.Z = t.subarray(e, e + r), n.da = r;
                    if (0 === n.da && 0 === a) return n.Z = t.slice(e, e + r), n.da = r;
                    if (a + r <= n.da) return n.Z.set(t.subarray(e, e + r), a), r
                }
                if (L.Ua(n, a + r), n.Z.subarray && t.subarray) n.Z.set(t.subarray(e, e + r), a);
                else
                    for (i = 0; i < r; i++) n.Z[a + i] = t[e + i];
                return n.da = Math.max(n.da, a + r), r
            },
            sa: function (n, t, e) {
                if (1 === e ? t += n.position : 2 === e && 32768 === (61440 & n.node.mode) && (t += n.node.da), 0 >
                    t) throw new K(28);
                return t
            },
            Ra: function (n, t, e) {
                L.Ua(n.node, t + e), n.node.da = Math.max(n.node.da, t + e)
            },
            Ea: function (n, t, e, r, a, i) {
                if (0 !== t) throw new K(28);
                if (32768 !== (61440 & n.node.mode)) throw new K(43);
                if (n = n.node.Z, 2 & i || n.buffer !== Ja) {
                    if ((0 < r || r + e < n.length) && (n = n.subarray ? n.subarray(r, r + e) : Array.prototype.slice
                            .call(n, r, r + e)), r = !0, e = nb(e), !e) throw new K(48);
                    F.set(n, e)
                } else r = !1, e = n.byteOffset;
                return {
                    aa: e,
                    Ka: r
                }
            },
            Ga: function (n, t, e, r, a) {
                if (32768 !== (61440 & n.node.mode)) throw new K(43);
                return 2 & a || L.Y.write(n, t, 0, r, e, !1), 0
            }
        }
    },
    tb = null,
    ub = {},
    M = [],
    vb = 1,
    wb = null,
    xb = !0,
    yb = {},
    K = null,
    rb = {};

function N(n, t) {
    if (n = fb("/", n), t = t || {}, !n) return {
        path: "",
        node: null
    };
    var e, r = {
        Xa: !0,
        Qa: 0
    };
    for (e in r) void 0 === t[e] && (t[e] = r[e]);
    if (8 < t.Qa) throw new K(32);
    n = ab(n.split("/").filter((function (n) {
        return !!n
    })), !1);
    var a = tb;
    for (r = "/", e = 0; e < n.length; e++) {
        var i = e === n.length - 1;
        if (i && t.parent) break;
        if (a = sb(a, n[e]), r = bb(r + "/" + n[e]), a.Fa && (!i || i && t.Xa) && (a = a.Fa.root), !i || t.Wa)
            for (i = 0; 40960 === (61440 & a.mode);)
                if (a = zb(r), r = fb(cb(r), a), a = N(r, {
                        Qa: t.Qa
                    }).node, 40 < i++) throw new K(32)
    }
    return {
        path: r,
        node: a
    }
}

function Ab(n) {
    for (var t;;) {
        if (n === n.parent) return n = n.na.Za, t ? "/" !== n[n.length - 1] ? n + "/" + t : n + t : n;
        t = t ? n.name + "/" + t : n.name, n = n.parent
    }
}

function Bb(n, t) {
    for (var e = 0, r = 0; r < t.length; r++) e = (e << 5) - e + t.charCodeAt(r) | 0;
    return (n + e >>> 0) % wb.length
}

function sb(n, t) {
    var e;
    if (e = (e = Cb(n, "x")) ? e : n.$.lookup ? 0 : 2) throw new K(e, n);
    for (e = wb[Bb(n.id, t)]; e; e = e.pb) {
        var r = e.name;
        if (e.parent.id === n.id && r === t) return e
    }
    return n.$.lookup(n, t)
}

function qb(n, t, e, r) {
    return n = new Db(n, t, e, r), t = Bb(n.parent.id, n.name), n.pb = wb[t], wb[t] = n
}
var Eb = {
    r: 0,
    "r+": 2,
    w: 577,
    "w+": 578,
    a: 1089,
    "a+": 1090
};

function Fb(n) {
    var t = ["r", "w", "rw"][3 & n];
    return 512 & n && (t += "w"), t
}

function Cb(n, t) {
    return xb ? 0 : !t.includes("r") || 292 & n.mode ? t.includes("w") && !(146 & n.mode) || t.includes("x") && !(73 &
        n.mode) ? 2 : 0 : 2
}

function Gb(n, t) {
    try {
        return sb(n, t), 20
    } catch (e) {}
    return Cb(n, "wx")
}

function Hb() {
    for (var n = 4096, t = 0; t <= n; t++)
        if (!M[t]) return t;
    throw new K(33)
}

function Ib(n) {
    Jb || (Jb = function () {}, Jb.prototype = {});
    var t, e = new Jb;
    for (t in n) e[t] = n[t];
    return n = e, e = Hb(), n.fd = e, M[e] = n
}
var Sb, pb = {
    open: function (n) {
        n.Y = ub[n.node.rdev].Y, n.Y.open && n.Y.open(n)
    },
    sa: function () {
        throw new K(70)
    }
};

function ib(n, t) {
    ub[n] = {
        Y: t
    }
}

function Kb(n, t) {
    var e = "/" === t,
        r = !t;
    if (e && tb) throw new K(10);
    if (!e && !r) {
        var a = N(t, {
            Xa: !1
        });
        if (t = a.path, a = a.node, a.Fa) throw new K(10);
        if (16384 !== (61440 & a.mode)) throw new K(54)
    }
    t = {
        type: n,
        Ob: {},
        Za: t,
        ob: []
    }, n = n.na(t), n.na = t, t.root = n, e ? tb = n : a && (a.Fa = t, a.na && a.na.ob.push(t))
}

function Lb(n, t, e) {
    var r = N(n, {
        parent: !0
    }).node;
    if (n = db(n), !n || "." === n || ".." === n) throw new K(28);
    var a = Gb(r, n);
    if (a) throw new K(a);
    if (!r.$.Da) throw new K(63);
    return r.$.Da(r, n, t, e)
}

function O(n) {
    return Lb(n, 16895, 0)
}

function Mb(n, t, e) {
    "undefined" === typeof e && (e = t, t = 438), Lb(n, 8192 | t, e)
}

function Nb(n, t) {
    if (!fb(n)) throw new K(44);
    var e = N(t, {
        parent: !0
    }).node;
    if (!e) throw new K(44);
    t = db(t);
    var r = Gb(e, t);
    if (r) throw new K(r);
    if (!e.$.symlink) throw new K(63);
    e.$.symlink(e, t, n)
}

function zb(n) {
    if (n = N(n).node, !n) throw new K(44);
    if (!n.$.readlink) throw new K(28);
    return fb(Ab(n.parent), n.$.readlink(n))
}

function Ob(n, t) {
    if ("" === n) throw new K(44);
    if ("string" === typeof t) {
        var e = Eb[t];
        if ("undefined" === typeof e) throw Error("Unknown file open mode: " + t);
        t = e
    }
    var r = 64 & t ? 4095 & ("undefined" === typeof r ? 438 : r) | 32768 : 0;
    if ("object" === typeof n) var a = n;
    else {
        n = bb(n);
        try {
            a = N(n, {
                Wa: !(131072 & t)
            }).node
        } catch (i) {}
    }
    if (e = !1, 64 & t)
        if (a) {
            if (128 & t) throw new K(20)
        } else a = Lb(n, r, 0), e = !0;
    if (!a) throw new K(44);
    if (8192 === (61440 & a.mode) && (t &= -513), 65536 & t && 16384 !== (61440 & a.mode)) throw new K(54);
    if (!e && (r = a ? 40960 === (61440 & a.mode) ? 32 : 16384 === (61440 & a.mode) && ("r" !== Fb(t) || 512 & t) ? 31 :
            Cb(a, Fb(t)) : 44)) throw new K(r);
    if (512 & t) {
        if (r = a, r = "string" === typeof r ? N(r, {
                Wa: !0
            }).node : r, !r.$.ma) throw new K(63);
        if (16384 === (61440 & r.mode)) throw new K(31);
        if (32768 !== (61440 & r.mode)) throw new K(28);
        if (e = Cb(r, "w")) throw new K(e);
        r.$.ma(r, {
            size: 0,
            timestamp: Date.now()
        })
    }
    t &= -131713, a = Ib({
        node: a,
        path: Ab(a),
        flags: t,
        seekable: !0,
        position: 0,
        Y: a.Y,
        Kb: [],
        error: !1
    }), a.Y.open && a.Y.open(a), !g.logReadFiles || 1 & t || (Pb || (Pb = {}), n in Pb || (Pb[n] = 1, B(
        "FS.trackingDelegate error on read file: " + n)));
    try {
        yb.onOpenFile && (a = 0, 1 !== (2097155 & t) && (a |= 1), 0 !== (2097155 & t) && (a |= 2), yb.onOpenFile(n, a))
    } catch (i) {
        B("FS.trackingDelegate['onOpenFile']('" + n + "', flags) threw an exception: " + i.message)
    }
}

function Qb(n, t, e) {
    if (null === n.fd) throw new K(8);
    if (!n.seekable || !n.Y.sa) throw new K(70);
    if (0 != e && 1 != e && 2 != e) throw new K(28);
    n.position = n.Y.sa(n, t, e), n.Kb = []
}

function Rb() {
    K || (K = function (n, t) {
        this.node = t, this.wb = function (n) {
            this.pa = n
        }, this.wb(n), this.message = "FS error"
    }, K.prototype = Error(), K.prototype.constructor = K, [44].forEach((function (n) {
        rb[n] = new K(n), rb[n].stack = "<generic error, no stack>"
    })))
}

function Tb(n, t) {
    var e = 0;
    return n && (e |= 365), t && (e |= 146), e
}

function Ub(n, t, e) {
    n = bb("/dev/" + n);
    var r = Tb(!!t, !!e);
    Vb || (Vb = 64);
    var a = Vb++ << 8 | 0;
    ib(a, {
        open: function (n) {
            n.seekable = !1
        },
        close: function () {
            e && e.buffer && e.buffer.length && e(10)
        },
        read: function (n, e, r, a) {
            for (var i = 0, o = 0; o < a; o++) {
                try {
                    var u = t()
                } catch (c) {
                    throw new K(29)
                }
                if (void 0 === u && 0 === i) throw new K(6);
                if (null === u || void 0 === u) break;
                i++, e[r + o] = u
            }
            return i && (n.node.timestamp = Date.now()), i
        },
        write: function (n, t, r, a) {
            for (var i = 0; i < a; i++) try {
                e(t[r + i])
            } catch (o) {
                throw new K(29)
            }
            return a && (n.node.timestamp = Date.now()), i
        }
    }), Mb(n, r, a)
}
var Vb, Jb, Pb, P = {},
    Wb = {};

function Xb(n) {
    if (n = M[n], !n) throw new K(8);
    return n
}
var Yb = {};

function Zb(n) {
    for (; n.length;) {
        var t = n.pop();
        n.pop()(t)
    }
}

function $b(n) {
    return this.fromWireType(G[n >> 2])
}
var ac = {},
    bc = {},
    cc = {};

function dc(n) {
    if (void 0 === n) return "_unknown";
    n = n.replace(/[^a-zA-Z0-9_]/g, "$");
    var t = n.charCodeAt(0);
    return 48 <= t && 57 >= t ? "_" + n : n
}

function ec(n, t) {
    return n = dc(n), new Function("body", "return function " + n +
        '() {\n    "use strict";    return body.apply(this, arguments);\n};\n')(t)
}

function fc(n) {
    var t = Error,
        e = ec(n, (function (t) {
            this.name = n, this.message = t, t = Error(t).stack, void 0 !== t && (this.stack = this.toString() +
                "\n" + t.replace(/^Error(:[^\n]*)?\n/, ""))
        }));
    return e.prototype = Object.create(t.prototype), e.prototype.constructor = e, e.prototype.toString = function () {
        return void 0 === this.message ? this.name : this.name + ": " + this.message
    }, e
}
var gc = void 0;

function hc(n) {
    throw new gc(n)
}

function ic(n, t, e) {
    function r(t) {
        t = e(t), t.length !== n.length && hc("Mismatched type converter count");
        for (var r = 0; r < n.length; ++r) Q(n[r], t[r])
    }
    n.forEach((function (n) {
        cc[n] = t
    }));
    var a = Array(t.length),
        i = [],
        o = 0;
    t.forEach((function (n, t) {
        bc.hasOwnProperty(n) ? a[t] = bc[n] : (i.push(n), ac.hasOwnProperty(n) || (ac[n] = []), ac[n].push(
            (function () {
                a[t] = bc[n], ++o, o === i.length && r(a)
            })))
    })), 0 === i.length && r(a)
}

function jc(n) {
    switch (n) {
        case 1:
            return 0;
        case 2:
            return 1;
        case 4:
            return 2;
        case 8:
            return 3;
        default:
            throw new TypeError("Unknown type size: " + n)
    }
}
var kc = void 0;

function R(n) {
    for (var t = ""; C[n];) t += kc[C[n++]];
    return t
}
var lc = void 0;

function S(n) {
    throw new lc(n)
}

function Q(n, t, e) {
    if (e = e || {}, !("argPackAdvance" in t)) throw new TypeError(
        "registerType registeredInstance requires argPackAdvance");
    var r = t.name;
    if (n || S('type "' + r + '" must have a positive integer typeid pointer'), bc.hasOwnProperty(n)) {
        if (e.kb) return;
        S("Cannot register type '" + r + "' twice")
    }
    bc[n] = t, delete cc[n], ac.hasOwnProperty(n) && (t = ac[n], delete ac[n], t.forEach((function (n) {
        n()
    })))
}

function mc(n) {
    S(n.X.ea.ba.name + " instance already deleted")
}
var nc = !1;

function oc() {}

function pc(n) {
    --n.count.value, 0 === n.count.value && (n.ga ? n.ha.oa(n.ga) : n.ea.ba.oa(n.aa))
}

function qc(n) {
    return "undefined" === typeof FinalizationGroup ? (qc = function (n) {
        return n
    }, n) : (nc = new FinalizationGroup((function (n) {
        for (var t = n.next(); !t.done; t = n.next()) t = t.value, t.aa ? pc(t) : console.warn(
            "object already deleted: " + t.aa)
    })), qc = function (n) {
        return nc.register(n, n.X, n.X), n
    }, oc = function (n) {
        nc.unregister(n.X)
    }, qc(n))
}
var rc = void 0,
    sc = [];

function tc() {
    for (; sc.length;) {
        var n = sc.pop();
        n.X.ta = !1, n["delete"]()
    }
}

function T() {}
var uc = {};

function vc(n, t, e) {
    if (void 0 === n[t].la) {
        var r = n[t];
        n[t] = function () {
            return n[t].la.hasOwnProperty(arguments.length) || S("Function '" + e +
                "' called with an invalid number of arguments (" + arguments.length + ") - expects one of (" +
                n[t].la + ")!"), n[t].la[arguments.length].apply(this, arguments)
        }, n[t].la = [], n[t].la[r.Aa] = r
    }
}

function wc(n, t) {
    g.hasOwnProperty(n) ? (S("Cannot register public name '" + n + "' twice"), vc(g, n, n), g.hasOwnProperty(void 0) &&
        S("Cannot register multiple overloads of a function with the same number of arguments (undefined)!"), g[n].la[
            void 0] = t) : g[n] = t
}

function xc(n, t, e, r, a, i, o, u) {
    this.name = n, this.constructor = t, this.ua = e, this.oa = r, this.ia = a, this.fb = i, this.za = o, this.cb = u,
        this.sb = []
}

function Ac(n, t, e) {
    for (; t !== e;) t.za || S("Expected null or instance of " + e.name + ", got an instance of " + t.name), n = t.za(n),
        t = t.ia;
    return n
}

function Bc(n, t) {
    return null === t ? (this.Na && S("null is not a valid " + this.name), 0) : (t.X || S('Cannot pass "' + Cc(t) +
        '" as a ' + this.name), t.X.aa || S("Cannot pass deleted object as a pointer of type " + this.name), Ac(
        t.X.aa, t.X.ea.ba, this.ba))
}

function Dc(n, t) {
    if (null === t) {
        if (this.Na && S("null is not a valid " + this.name), this.Ca) {
            var e = this.Pa();
            return null !== n && n.push(this.oa, e), e
        }
        return 0
    }
    if (t.X || S('Cannot pass "' + Cc(t) + '" as a ' + this.name), t.X.aa || S(
            "Cannot pass deleted object as a pointer of type " + this.name), !this.Ba && t.X.ea.Ba && S(
            "Cannot convert argument of type " + (t.X.ha ? t.X.ha.name : t.X.ea.name) + " to parameter type " + this.name
        ), e = Ac(t.X.aa, t.X.ea.ba, this.ba), this.Ca) switch (void 0 === t.X.ga && S(
        "Passing raw pointer to smart pointer is illegal"), this.Fb) {
        case 0:
            t.X.ha === this ? e = t.X.ga : S("Cannot convert argument of type " + (t.X.ha ? t.X.ha.name : t.X.ea.name) +
                " to parameter type " + this.name);
            break;
        case 1:
            e = t.X.ga;
            break;
        case 2:
            if (t.X.ha === this) e = t.X.ga;
            else {
                var r = t.clone();
                e = this.tb(e, Ec((function () {
                    r["delete"]()
                }))), null !== n && n.push(this.oa, e)
            }
            break;
        default:
            S("Unsupporting sharing policy")
    }
    return e
}

function Fc(n, t) {
    return null === t ? (this.Na && S("null is not a valid " + this.name), 0) : (t.X || S('Cannot pass "' + Cc(t) +
            '" as a ' + this.name), t.X.aa || S("Cannot pass deleted object as a pointer of type " + this.name), t.X
        .ea.Ba && S("Cannot convert argument of type " + t.X.ea.name + " to parameter type " + this.name), Ac(t.X.aa,
            t.X.ea.ba, this.ba))
}

function Gc(n, t, e) {
    return t === e ? n : void 0 === e.ia ? null : (n = Gc(n, t, e.ia), null === n ? null : e.cb(n))
}
var Hc = {};

function Ic(n, t) {
    for (void 0 === t && S("ptr should not be undefined"); n.ia;) t = n.za(t), n = n.ia;
    return Hc[t]
}

function Jc(n, t) {
    return t.ea && t.aa || hc("makeClassHandle requires ptr and ptrType"), !!t.ha !== !!t.ga && hc(
        "Both smartPtrType and smartPtr must be specified"), t.count = {
        value: 1
    }, qc(Object.create(n, {
        X: {
            value: t
        }
    }))
}

function U(n, t, e, r) {
    this.name = n, this.ba = t, this.Na = e, this.Ba = r, this.Ca = !1, this.oa = this.tb = this.Pa = this.$a = this.Fb =
        this.qb = void 0, void 0 !== t.ia ? this.toWireType = Dc : (this.toWireType = r ? Bc : Fc, this.ja = null)
}

function Kc(n, t) {
    g.hasOwnProperty(n) || hc("Replacing nonexistant public symbol"), g[n] = t, g[n].Aa = void 0
}

function Lc(n, t) {
    var e = [];
    return function () {
        e.length = arguments.length;
        for (var r = 0; r < arguments.length; r++) e[r] = arguments[r];
        return n.includes("j") ? (r = g["dynCall_" + n], r = e && e.length ? r.apply(null, [t].concat(e)) : r.call(
            null, t)) : r = H.get(t).apply(null, e), r
    }
}

function V(n, t) {
    n = R(n);
    var e = n.includes("j") ? Lc(n, t) : H.get(t);
    return "function" !== typeof e && S("unknown function pointer with signature " + n + ": " + t), e
}
var Mc = void 0;

function Nc(n) {
    n = Oc(n);
    var t = R(n);
    return W(n), t
}

function Pc(n, t) {
    function e(n) {
        a[n] || bc[n] || (cc[n] ? cc[n].forEach(e) : (r.push(n), a[n] = !0))
    }
    var r = [],
        a = {};
    throw t.forEach(e), new Mc(n + ": " + r.map(Nc).join([", "]))
}

function Qc(n, t) {
    for (var e = [], r = 0; r < n; r++) e.push(D[(t >> 2) + r]);
    return e
}

function Rc(n) {
    var t = Function;
    if (!(t instanceof Function)) throw new TypeError("new_ called with constructor type " + typeof t +
        " which is not a function");
    var e = ec(t.name || "unknownFunctionName", (function () {}));
    return e.prototype = t.prototype, e = new e, n = t.apply(e, n), n instanceof Object ? n : e
}
var Sc = [],
    X = [{}, {
        value: void 0
    }, {
        value: null
    }, {
        value: !0
    }, {
        value: !1
    }];

function Ec(n) {
    switch (n) {
        case void 0:
            return 1;
        case null:
            return 2;
        case !0:
            return 3;
        case !1:
            return 4;
        default:
            var t = Sc.length ? Sc.pop() : X.length;
            return X[t] = {
                ub: 1,
                value: n
            }, t
    }
}

function Cc(n) {
    if (null === n) return "null";
    var t = typeof n;
    return "object" === t || "array" === t || "function" === t ? n.toString() : "" + n
}

function Tc(n, t) {
    switch (t) {
        case 2:
            return function (n) {
                return this.fromWireType(Ka[n >> 2])
            };
        case 3:
            return function (n) {
                return this.fromWireType(La[n >> 3])
            };
        default:
            throw new TypeError("Unknown float type: " + n)
    }
}

function Uc(n, t, e) {
    switch (t) {
        case 0:
            return e ? function (n) {
                return F[n]
            } : function (n) {
                return C[n]
            };
        case 1:
            return e ? function (n) {
                return Da[n >> 1]
            } : function (n) {
                return Ca[n >> 1]
            };
        case 2:
            return e ? function (n) {
                return D[n >> 2]
            } : function (n) {
                return G[n >> 2]
            };
        default:
            throw new TypeError("Unknown integer type: " + n)
    }
}
var Xc, Vc = {};

function Wc() {
    if (!Xc) {
        var n, t = {
            USER: "web_user",
            LOGNAME: "web_user",
            PATH: "/",
            PWD: "/",
            HOME: "/home/<USER>",
            LANG: ("object" === typeof navigator && navigator.languages && navigator.languages[0] || "C").replace(
                "-", "_") + ".UTF-8",
            _: ba || "./this.program"
        };
        for (n in Vc) void 0 === Vc[n] ? delete t[n] : t[n] = Vc[n];
        var e = [];
        for (n in t) e.push(n + "=" + t[n]);
        Xc = e
    }
    return Xc
}

function Yc(n) {
    return 0 === n % 4 && (0 !== n % 100 || 0 === n % 400)
}

function Zc(n, t) {
    for (var e = 0, r = 0; r <= t; e += n[r++]);
    return e
}
var $c = [31, 29, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31],
    ad = [31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31];

function bd(n, t) {
    for (n = new Date(n.getTime()); 0 < t;) {
        var e = n.getMonth(),
            r = (Yc(n.getFullYear()) ? $c : ad)[e];
        if (!(t > r - n.getDate())) {
            n.setDate(n.getDate() + t);
            break
        }
        t -= r - n.getDate() + 1, n.setDate(1), 11 > e ? n.setMonth(e + 1) : (n.setMonth(0), n.setFullYear(n.getFullYear() +
            1))
    }
    return n
}

function cd(n, t, e, r) {
    function a(n, t, e) {
        for (n = "number" === typeof n ? n.toString() : n || ""; n.length < t;) n = e[0] + n;
        return n
    }

    function i(n, t) {
        return a(n, t, "0")
    }

    function o(n, t) {
        function e(n) {
            return 0 > n ? -1 : 0 < n ? 1 : 0
        }
        var r;
        return 0 === (r = e(n.getFullYear() - t.getFullYear())) && 0 === (r = e(n.getMonth() - t.getMonth())) && (r = e(
            n.getDate() - t.getDate())), r
    }

    function u(n) {
        switch (n.getDay()) {
            case 0:
                return new Date(n.getFullYear() - 1, 11, 29);
            case 1:
                return n;
            case 2:
                return new Date(n.getFullYear(), 0, 3);
            case 3:
                return new Date(n.getFullYear(), 0, 2);
            case 4:
                return new Date(n.getFullYear(), 0, 1);
            case 5:
                return new Date(n.getFullYear() - 1, 11, 31);
            case 6:
                return new Date(n.getFullYear() - 1, 11, 30)
        }
    }

    function c(n) {
        n = bd(new Date(n.fa + 1900, 0, 1), n.Ja);
        var t = new Date(n.getFullYear() + 1, 0, 4),
            e = u(new Date(n.getFullYear(), 0, 4));
        return t = u(t), 0 >= o(e, n) ? 0 >= o(t, n) ? n.getFullYear() + 1 : n.getFullYear() : n.getFullYear() - 1
    }
    var f = D[r + 40 >> 2];
    for (var s in r = {
            Ib: D[r >> 2],
            Hb: D[r + 4 >> 2],
            Ha: D[r + 8 >> 2],
            ya: D[r + 12 >> 2],
            wa: D[r + 16 >> 2],
            fa: D[r + 20 >> 2],
            Ia: D[r + 24 >> 2],
            Ja: D[r + 28 >> 2],
            Pb: D[r + 32 >> 2],
            Gb: D[r + 36 >> 2],
            Jb: f ? xa(f) : ""
        }, e = xa(e), f = {
            "%c": "%a %b %d %H:%M:%S %Y",
            "%D": "%m/%d/%y",
            "%F": "%Y-%m-%d",
            "%h": "%b",
            "%r": "%I:%M:%S %p",
            "%R": "%H:%M",
            "%T": "%H:%M:%S",
            "%x": "%m/%d/%y",
            "%X": "%H:%M:%S",
            "%Ec": "%c",
            "%EC": "%C",
            "%Ex": "%m/%d/%y",
            "%EX": "%H:%M:%S",
            "%Ey": "%y",
            "%EY": "%Y",
            "%Od": "%d",
            "%Oe": "%e",
            "%OH": "%H",
            "%OI": "%I",
            "%Om": "%m",
            "%OM": "%M",
            "%OS": "%S",
            "%Ou": "%u",
            "%OU": "%U",
            "%OV": "%V",
            "%Ow": "%w",
            "%OW": "%W",
            "%Oy": "%y"
        }, f) e = e.replace(new RegExp(s, "g"), f[s]);
    var l = "Sunday Monday Tuesday Wednesday Thursday Friday Saturday".split(" "),
        d = "January February March April May June July August September October November December".split(" ");
    for (s in f = {
            "%a": function (n) {
                return l[n.Ia].substring(0, 3)
            },
            "%A": function (n) {
                return l[n.Ia]
            },
            "%b": function (n) {
                return d[n.wa].substring(0, 3)
            },
            "%B": function (n) {
                return d[n.wa]
            },
            "%C": function (n) {
                return i((n.fa + 1900) / 100 | 0, 2)
            },
            "%d": function (n) {
                return i(n.ya, 2)
            },
            "%e": function (n) {
                return a(n.ya, 2, " ")
            },
            "%g": function (n) {
                return c(n).toString().substring(2)
            },
            "%G": function (n) {
                return c(n)
            },
            "%H": function (n) {
                return i(n.Ha, 2)
            },
            "%I": function (n) {
                return n = n.Ha, 0 == n ? n = 12 : 12 < n && (n -= 12), i(n, 2)
            },
            "%j": function (n) {
                return i(n.ya + Zc(Yc(n.fa + 1900) ? $c : ad, n.wa - 1), 3)
            },
            "%m": function (n) {
                return i(n.wa + 1, 2)
            },
            "%M": function (n) {
                return i(n.Hb, 2)
            },
            "%n": function () {
                return "\n"
            },
            "%p": function (n) {
                return 0 <= n.Ha && 12 > n.Ha ? "AM" : "PM"
            },
            "%S": function (n) {
                return i(n.Ib, 2)
            },
            "%t": function () {
                return "\t"
            },
            "%u": function (n) {
                return n.Ia || 7
            },
            "%U": function (n) {
                var t = new Date(n.fa + 1900, 0, 1),
                    e = 0 === t.getDay() ? t : bd(t, 7 - t.getDay());
                return n = new Date(n.fa + 1900, n.wa, n.ya), 0 > o(e, n) ? i(Math.ceil((31 - e.getDate() + (Zc(Yc(
                        n.getFullYear()) ? $c : ad, n.getMonth() - 1) - 31) + n.getDate()) / 7), 2) : 0 === o(e, t) ?
                    "01" : "00"
            },
            "%V": function (n) {
                var t = new Date(n.fa + 1901, 0, 4),
                    e = u(new Date(n.fa + 1900, 0, 4));
                t = u(t);
                var r = bd(new Date(n.fa + 1900, 0, 1), n.Ja);
                return 0 > o(r, e) ? "53" : 0 >= o(t, r) ? "01" : i(Math.ceil((e.getFullYear() < n.fa + 1900 ? n.Ja +
                    32 - e.getDate() : n.Ja + 1 - e.getDate()) / 7), 2)
            },
            "%w": function (n) {
                return n.Ia
            },
            "%W": function (n) {
                var t = new Date(n.fa, 0, 1),
                    e = 1 === t.getDay() ? t : bd(t, 0 === t.getDay() ? 1 : 7 - t.getDay() + 1);
                return n = new Date(n.fa + 1900, n.wa, n.ya), 0 > o(e, n) ? i(Math.ceil((31 - e.getDate() + (Zc(Yc(
                        n.getFullYear()) ? $c : ad, n.getMonth() - 1) - 31) + n.getDate()) / 7), 2) : 0 === o(e, t) ?
                    "01" : "00"
            },
            "%y": function (n) {
                return (n.fa + 1900).toString().substring(2)
            },
            "%Y": function (n) {
                return n.fa + 1900
            },
            "%z": function (n) {
                n = n.Gb;
                var t = 0 <= n;
                return n = Math.abs(n) / 60, (t ? "+" : "-") + String("0000" + (n / 60 * 100 + n % 60)).slice(-4)
            },
            "%Z": function (n) {
                return n.Jb
            },
            "%%": function () {
                return "%"
            }
        }, f) e.includes(s) && (e = e.replace(new RegExp(s, "g"), f[s](r)));
    return s = kb(e, !1), s.length > t ? 0 : (F.set(s, n), s.length - 1)
}

function Db(n, t, e, r) {
    n || (n = this), this.parent = n, this.na = n.na, this.Fa = null, this.id = vb++, this.name = t, this.mode = e,
        this.$ = {}, this.Y = {}, this.rdev = r
}
Object.defineProperties(Db.prototype, {
        read: {
            get: function () {
                return 365 === (365 & this.mode)
            },
            set: function (n) {
                n ? this.mode |= 365 : this.mode &= -366
            }
        },
        write: {
            get: function () {
                return 146 === (146 & this.mode)
            },
            set: function (n) {
                n ? this.mode |= 146 : this.mode &= -147
            }
        }
    }), Rb(), wb = Array(4096), Kb(L, "/"), O("/tmp"), O("/home"), O("/home/<USER>"),
    function () {
        O("/dev"), ib(259, {
            read: function () {
                return 0
            },
            write: function (n, t, e, r) {
                return r
            }
        }), Mb("/dev/null", 259), hb(1280, lb), hb(1536, mb), Mb("/dev/tty", 1280), Mb("/dev/tty1", 1536);
        var n = eb();
        Ub("random", n), Ub("urandom", n), O("/dev/shm"), O("/dev/shm/tmp")
    }(),
    function () {
        O("/proc");
        var n = O("/proc/self");
        O("/proc/self/fd"), Kb({
            na: function () {
                var t = qb(n, "fd", 16895, 73);
                return t.$ = {
                    lookup: function (n, t) {
                        var e = M[+t];
                        if (!e) throw new K(8);
                        return n = {
                            parent: null,
                            na: {
                                Za: "fake"
                            },
                            $: {
                                readlink: function () {
                                    return e.path
                                }
                            }
                        }, n.parent = n
                    }
                }, t
            }
        }, "/proc/self/fd")
    }(), gc = g.InternalError = fc("InternalError");
for (var dd = Array(256), ed = 0; 256 > ed; ++ed) dd[ed] = String.fromCharCode(ed);

function kb(n, t) {
    var e = Array(za(n) + 1);
    return n = ya(n, e, 0, e.length), t && (e.length = n), e
}
kc = dd, lc = g.BindingError = fc("BindingError"), T.prototype.isAliasOf = function (n) {
    if (!(this instanceof T && n instanceof T)) return !1;
    var t = this.X.ea.ba,
        e = this.X.aa,
        r = n.X.ea.ba;
    for (n = n.X.aa; t.ia;) e = t.za(e), t = t.ia;
    for (; r.ia;) n = r.za(n), r = r.ia;
    return t === r && e === n
}, T.prototype.clone = function () {
    if (this.X.aa || mc(this), this.X.xa) return this.X.count.value += 1, this;
    var n = qc,
        t = Object,
        e = t.create,
        r = Object.getPrototypeOf(this),
        a = this.X;
    return n = n(e.call(t, r, {
        X: {
            value: {
                count: a.count,
                ta: a.ta,
                xa: a.xa,
                aa: a.aa,
                ea: a.ea,
                ga: a.ga,
                ha: a.ha
            }
        }
    })), n.X.count.value += 1, n.X.ta = !1, n
}, T.prototype["delete"] = function () {
    this.X.aa || mc(this), this.X.ta && !this.X.xa && S("Object already scheduled for deletion"), oc(this), pc(this
        .X), this.X.xa || (this.X.ga = void 0, this.X.aa = void 0)
}, T.prototype.isDeleted = function () {
    return !this.X.aa
}, T.prototype.deleteLater = function () {
    return this.X.aa || mc(this), this.X.ta && !this.X.xa && S("Object already scheduled for deletion"), sc.push(
        this), 1 === sc.length && rc && rc(tc), this.X.ta = !0, this
}, U.prototype.gb = function (n) {
    return this.$a && (n = this.$a(n)), n
}, U.prototype.Ta = function (n) {
    this.oa && this.oa(n)
}, U.prototype.argPackAdvance = 8, U.prototype.readValueFromPointer = $b, U.prototype.deleteObject = function (n) {
    null !== n && n["delete"]()
}, U.prototype.fromWireType = function (n) {
    function t() {
        return this.Ca ? Jc(this.ba.ua, {
            ea: this.qb,
            aa: e,
            ha: this,
            ga: n
        }) : Jc(this.ba.ua, {
            ea: this,
            aa: n
        })
    }
    var e = this.gb(n);
    if (!e) return this.Ta(n), null;
    var r = Ic(this.ba, e);
    if (void 0 !== r) return 0 === r.X.count.value ? (r.X.aa = e, r.X.ga = n, r.clone()) : (r = r.clone(), this.Ta(
        n), r);
    if (r = this.ba.fb(e), r = uc[r], !r) return t.call(this);
    r = this.Ba ? r.bb : r.pointerType;
    var a = Gc(e, this.ba, r.ba);
    return null === a ? t.call(this) : this.Ca ? Jc(r.ba.ua, {
        ea: r,
        aa: a,
        ha: this,
        ga: n
    }) : Jc(r.ba.ua, {
        ea: r,
        aa: a
    })
}, g.getInheritedInstanceCount = function () {
    return Object.keys(Hc).length
}, g.getLiveInheritedInstances = function () {
    var n, t = [];
    for (n in Hc) Hc.hasOwnProperty(n) && t.push(Hc[n]);
    return t
}, g.flushPendingDeletes = tc, g.setDelayFunction = function (n) {
    rc = n, sc.length && rc && rc(tc)
}, Mc = g.UnboundTypeError = fc("UnboundTypeError"), g.count_emval_handles = function () {
    for (var n = 0, t = 5; t < X.length; ++t) void 0 !== X[t] && ++n;
    return n
}, g.get_first_emval = function () {
    for (var n = 5; n < X.length; ++n)
        if (void 0 !== X[n]) return X[n];
    return null
};
var gd = {
    a: function (n, t, e, r) {
        z("Assertion failed: " + xa(n) + ", at: " + [t ? xa(t) : "unknown filename", e, r ? xa(r) :
            "unknown function"])
    },
    i: function (n) {
        return fd(n + 16) + 16
    },
    h: function (n, t, e) {
        throw new Za(n).lb(t, e), $a++, n
    },
    C: function (n, t, e, r, a, i) {
        try {
            n: {
                i <<= 12;
                var o = !1;
                if (0 !== (16 & r) && 0 !== n % 65536) var u = -28;
                else {
                    if (0 !== (32 & r)) {
                        var c = nb(t);
                        if (!c) {
                            u = -48;
                            break n
                        }
                        o = !0
                    } else {
                        var f = M[a];
                        if (!f) {
                            u = -8;
                            break n
                        }
                        var s = i;
                        if (0 !== (2 & e) && 0 === (2 & r) && 2 !== (2097155 & f.flags)) throw new K(2);
                        if (1 === (2097155 & f.flags)) throw new K(2);
                        if (!f.Y.Ea) throw new K(43);
                        var l = f.Y.Ea(f, n, t, s, e, r);
                        c = l.aa, o = l.Ka
                    }
                    Wb[c] = {
                        nb: c,
                        mb: t,
                        Ka: o,
                        fd: a,
                        rb: e,
                        flags: r,
                        offset: i
                    }, u = c
                }
            }
            return u
        }
        catch (d) {
            return "undefined" !== typeof P && d instanceof K || z(d), -d.pa
        }
    },
    B: function (n, t) {
        try {
            var e = Wb[n];
            if (0 !== t && e) {
                if (t === e.mb) {
                    var r = M[e.fd];
                    if (r && 2 & e.rb) {
                        var a = e.flags,
                            i = e.offset,
                            o = C.slice(n, n + t);
                        r && r.Y.Ga && r.Y.Ga(r, o, i, t, a)
                    }
                    Wb[n] = null, e.Ka && W(e.nb)
                }
                var u = 0
            } else u = -28;
            return u
        } catch (c) {
            return "undefined" !== typeof P && c instanceof K || z(c), -c.pa
        }
    },
    p: function (n) {
        var t = Yb[n];
        delete Yb[n];
        var e = t.Pa,
            r = t.oa,
            a = t.Va,
            i = a.map((function (n) {
                return n.jb
            })).concat(a.map((function (n) {
                return n.Db
            })));
        ic([n], i, (function (n) {
            var i = {};
            return a.forEach((function (t, e) {
                var r = n[e],
                    o = t.hb,
                    u = t.ib,
                    c = n[e + a.length],
                    f = t.Cb,
                    s = t.Eb;
                i[t.eb] = {
                    read: function (n) {
                        return r.fromWireType(o(u, n))
                    },
                    write: function (n, t) {
                        var e = [];
                        f(s, n, c.toWireType(e, t)), Zb(e)
                    }
                }
            })), [{
                name: t.name,
                fromWireType: function (n) {
                    var t, e = {};
                    for (t in i) e[t] = i[t].read(n);
                    return r(n), e
                },
                toWireType: function (n, t) {
                    for (var a in i)
                        if (!(a in t)) throw new TypeError('Missing field:  "' + a +
                            '"');
                    var o = e();
                    for (a in i) i[a].write(o, t[a]);
                    return null !== n && n.push(r, o), o
                },
                argPackAdvance: 8,
                readValueFromPointer: $b,
                ja: r
            }]
        }))
    },
    r: function () {},
    F: function (n, t, e, r, a) {
        var i = jc(e);
        t = R(t), Q(n, {
            name: t,
            fromWireType: function (n) {
                return !!n
            },
            toWireType: function (n, t) {
                return t ? r : a
            },
            argPackAdvance: 8,
            readValueFromPointer: function (n) {
                if (1 === e) var r = F;
                else if (2 === e) r = Da;
                else {
                    if (4 !== e) throw new TypeError("Unknown boolean type size: " + t);
                    r = D
                }
                return this.fromWireType(r[n >> i])
            },
            ja: null
        })
    },
    o: function (n, t, e, r, a, i, o, u, c, f, s, l, d) {
        s = R(s), i = V(a, i), u && (u = V(o, u)), f && (f = V(c, f)), d = V(l, d);
        var h = dc(s);
        wc(h, (function () {
            Pc("Cannot construct " + s + " due to unbound types", [r])
        })), ic([n, t, e], r ? [r] : [], (function (t) {
            if (t = t[0], r) var e = t.ba,
                a = e.ua;
            else a = T.prototype;
            t = ec(h, (function () {
                if (Object.getPrototypeOf(this) !== o) throw new lc(
                    "Use 'new' to construct " + s);
                if (void 0 === c.ra) throw new lc(s + " has no accessible constructor");
                var n = c.ra[arguments.length];
                if (void 0 === n) throw new lc("Tried to invoke ctor of " + s +
                    " with invalid number of parameters (" + arguments.length +
                    ") - expected (" + Object.keys(c.ra).toString() +
                    ") parameters instead!");
                return n.apply(this, arguments)
            }));
            var o = Object.create(a, {
                constructor: {
                    value: t
                }
            });
            t.prototype = o;
            var c = new xc(s, t, o, d, e, i, u, f);
            e = new U(s, c, !0, !1), a = new U(s + "*", c, !1, !1);
            var l = new U(s + " const*", c, !1, !0);
            return uc[n] = {
                pointerType: a,
                bb: l
            }, Kc(h, t), [e, a, l]
        }))
    },
    n: function (n, t, e, r, a, i) {
        0 < t || z("Assertion failed: undefined");
        var o = Qc(t, e);
        a = V(r, a);
        var u = [i],
            c = [];
        ic([], [n], (function (n) {
            n = n[0];
            var e = "constructor " + n.name;
            if (void 0 === n.ba.ra && (n.ba.ra = []), void 0 !== n.ba.ra[t - 1]) throw new lc(
                "Cannot register multiple constructors with identical number of parameters (" +
                (t - 1) + ") for class '" + n.name +
                "'! Overload resolution is currently only performed using the parameter count, not actual type info!"
            );
            return n.ba.ra[t - 1] = function () {
                Pc("Cannot construct " + n.name + " due to unbound types", o)
            }, ic([], o, (function (r) {
                return n.ba.ra[t - 1] = function () {
                    arguments.length !== t - 1 && S(e + " called with " + arguments
                            .length + " arguments, expected " + (t - 1)), c.length =
                        0, u.length = t;
                    for (var n = 1; n < t; ++n) u[n] = r[n].toWireType(c, arguments[
                        n - 1]);
                    return n = a.apply(null, u), Zb(c), r[0].fromWireType(n)
                }, []
            })), []
        }))
    },
    e: function (n, t, e, r, a, i, o, u) {
        var c = Qc(e, r);
        t = R(t), i = V(a, i), ic([], [n], (function (n) {
            function r() {
                Pc("Cannot call " + a + " due to unbound types", c)
            }
            n = n[0];
            var a = n.name + "." + t;
            t.startsWith("@@") && (t = Symbol[t.substring(2)]), u && n.ba.sb.push(t);
            var f = n.ba.ua,
                s = f[t];
            return void 0 === s || void 0 === s.la && s.className !== n.name && s.Aa === e - 2 ? (r
                    .Aa = e - 2, r.className = n.name, f[t] = r) : (vc(f, t, a), f[t].la[e - 2] = r),
                ic([], c, (function (r) {
                    var u = a,
                        c = n,
                        s = i,
                        l = r.length;
                    2 > l && S(
                        "argTypes array size mismatch! Must at least get return value and 'this' types!"
                    );
                    var d = null !== r[1] && null !== c,
                        h = !1;
                    for (c = 1; c < r.length; ++c)
                        if (null !== r[c] && void 0 === r[c].ja) {
                            h = !0;
                            break
                        } var p = "void" !== r[0].name,
                        b = "",
                        g = "";
                    for (c = 0; c < l - 2; ++c) b += (0 !== c ? ", " : "") + "arg" + c, g +=
                        (0 !== c ? ", " : "") + "arg" + c + "Wired";
                    u = "return function " + dc(u) + "(" + b +
                        ") {\nif (arguments.length !== " + (l - 2) +
                        ") {\nthrowBindingError('function " + u +
                        " called with ' + arguments.length + ' arguments, expected " + (l -
                            2) + " args!');\n}\n", h && (u += "var destructors = [];\n");
                    var v = h ? "destructors" : "null";
                    for (b =
                        "throwBindingError invoker fn runDestructors retType classParam".split(
                            " "), s = [S, s, o, Zb, r[0], r[1]], d && (u +=
                            "var thisWired = classParam.toWireType(" + v + ", this);\n"), c =
                        0; c < l - 2; ++c) u += "var arg" + c + "Wired = argType" + c +
                        ".toWireType(" + v + ", arg" + c + "); // " + r[c + 2].name + "\n",
                        b.push("argType" + c), s.push(r[c + 2]);
                    if (d && (g = "thisWired" + (0 < g.length ? ", " : "") + g), u += (p ?
                            "var rv = " : "") + "invoker(fn" + (0 < g.length ? ", " : "") +
                        g + ");\n", h) u += "runDestructors(destructors);\n";
                    else
                        for (c = d ? 1 : 2; c < r.length; ++c) l = 1 === c ? "thisWired" :
                            "arg" + (c - 2) + "Wired", null !== r[c].ja && (u += l +
                                "_dtor(" + l + "); // " + r[c].name + "\n", b.push(l +
                                    "_dtor"), s.push(r[c].ja));
                    return p && (u += "var ret = retType.fromWireType(rv);\nreturn ret;\n"),
                        b.push(u + "}\n"), r = Rc(b).apply(null, s), void 0 === f[t].la ? (
                            r.Aa = e - 2, f[t] = r) : f[t].la[e - 2] = r, []
                })), []
        }))
    },
    E: function (n, t) {
        t = R(t), Q(n, {
            name: t,
            fromWireType: function (n) {
                var t = X[n].value;
                return 4 < n && 0 === --X[n].ub && (X[n] = void 0, Sc.push(n)), t
            },
            toWireType: function (n, t) {
                return Ec(t)
            },
            argPackAdvance: 8,
            readValueFromPointer: $b,
            ja: null
        })
    },
    l: function (n, t, e) {
        e = jc(e), t = R(t), Q(n, {
            name: t,
            fromWireType: function (n) {
                return n
            },
            toWireType: function (n, t) {
                if ("number" !== typeof t && "boolean" !== typeof t) throw new TypeError(
                    'Cannot convert "' + Cc(t) + '" to ' + this.name);
                return t
            },
            argPackAdvance: 8,
            readValueFromPointer: Tc(t, e),
            ja: null
        })
    },
    d: function (n, t, e, r, a) {
        function i(n) {
            return n
        }
        t = R(t), -1 === a && (a = 4294967295);
        var o = jc(e);
        if (0 === r) {
            var u = 32 - 8 * e;
            i = function (n) {
                return n << u >>> u
            }
        }
        var c = t.includes("unsigned");
        Q(n, {
            name: t,
            fromWireType: i,
            toWireType: function (n, e) {
                if ("number" !== typeof e && "boolean" !== typeof e) throw new TypeError(
                    'Cannot convert "' + Cc(e) + '" to ' + this.name);
                if (e < r || e > a) throw new TypeError('Passing a number "' + Cc(e) +
                    '" from JS side to C/C++ side to an argument of type "' + t +
                    '", which is outside the valid range [' + r + ", " + a + "]!");
                return c ? e >>> 0 : 0 | e
            },
            argPackAdvance: 8,
            readValueFromPointer: Uc(t, o, 0 !== r),
            ja: null
        })
    },
    c: function (n, t, e) {
        function r(n) {
            n >>= 2;
            var t = G;
            return new a(Ja, t[n + 1], t[n])
        }
        var a = [Int8Array, Uint8Array, Int16Array, Uint16Array, Int32Array, Uint32Array, Float32Array,
            Float64Array][t];
        e = R(e), Q(n, {
            name: e,
            fromWireType: r,
            argPackAdvance: 8,
            readValueFromPointer: r
        }, {
            kb: !0
        })
    },
    m: function (n, t) {
        t = R(t);
        var e = "std::string" === t;
        Q(n, {
            name: t,
            fromWireType: function (n) {
                var t = G[n >> 2];
                if (e)
                    for (var r = n + 4, a = 0; a <= t; ++a) {
                        var i = n + 4 + a;
                        if (a == t || 0 == C[i]) {
                            if (r = xa(r, i - r), void 0 === o) var o = r;
                            else o += String.fromCharCode(0), o += r;
                            r = i + 1
                        }
                    } else {
                        for (o = Array(t), a = 0; a < t; ++a) o[a] = String.fromCharCode(C[n + 4 +
                            a]);
                        o = o.join("")
                    }
                return W(n), o
            },
            toWireType: function (n, t) {
                t instanceof ArrayBuffer && (t = new Uint8Array(t));
                var r = "string" === typeof t;
                r || t instanceof Uint8Array || t instanceof Uint8ClampedArray || t instanceof Int8Array ||
                    S("Cannot pass non-string to std::string");
                var a = (e && r ? function () {
                        return za(t)
                    } : function () {
                        return t.length
                    })(),
                    i = fd(4 + a + 1);
                if (G[i >> 2] = a, e && r) ya(t, C, i + 4, a + 1);
                else if (r)
                    for (r = 0; r < a; ++r) {
                        var o = t.charCodeAt(r);
                        255 < o && (W(i), S(
                            "String has UTF-16 code units that do not fit in 8 bits")), C[i + 4 +
                            r] = o
                    } else
                        for (r = 0; r < a; ++r) C[i + 4 + r] = t[r];
                return null !== n && n.push(W, i), i
            },
            argPackAdvance: 8,
            readValueFromPointer: $b,
            ja: function (n) {
                W(n)
            }
        })
    },
    g: function (n, t, e) {
        if (e = R(e), 2 === t) var r = Ba,
            a = Ea,
            i = Fa,
            o = function () {
                return Ca
            },
            u = 1;
        else 4 === t && (r = Ga, a = Ha, i = Ia, o = function () {
            return G
        }, u = 2);
        Q(n, {
            name: e,
            fromWireType: function (n) {
                for (var e, a = G[n >> 2], i = o(), c = n + 4, f = 0; f <= a; ++f) {
                    var s = n + 4 + f * t;
                    f != a && 0 != i[s >> u] || (c = r(c, s - c), void 0 === e ? e = c : (e +=
                        String.fromCharCode(0), e += c), c = s + t)
                }
                return W(n), e
            },
            toWireType: function (n, r) {
                "string" !== typeof r && S("Cannot pass non-string to C++ string type " + e);
                var o = i(r),
                    c = fd(4 + o + t);
                return G[c >> 2] = o >> u, a(r, c + 4, o + t), null !== n && n.push(W, c), c
            },
            argPackAdvance: 8,
            readValueFromPointer: $b,
            ja: function (n) {
                W(n)
            }
        })
    },
    t: function (n, t, e, r, a, i) {
        Yb[n] = {
            name: R(t),
            Pa: V(e, r),
            oa: V(a, i),
            Va: []
        }
    },
    j: function (n, t, e, r, a, i, o, u, c, f) {
        Yb[n].Va.push({
            eb: R(t),
            jb: e,
            hb: V(r, a),
            ib: i,
            Db: o,
            Cb: V(u, c),
            Eb: f
        })
    },
    G: function (n, t) {
        t = R(t), Q(n, {
            Nb: !0,
            name: t,
            argPackAdvance: 0,
            fromWireType: function () {},
            toWireType: function () {}
        })
    },
    k: function () {
        z()
    },
    D: function () {
        return C.length
    },
    u: function (n, t, e) {
        C.copyWithin(n, t, t + e)
    },
    v: function () {
        z("OOM")
    },
    x: function (n, t) {
        try {
            var e = 0;
            return Wc().forEach((function (r, a) {
                var i = t + e;
                for (a = D[n + 4 * a >> 2] = i, i = 0; i < r.length; ++i) F[a++ >> 0] = r.charCodeAt(
                    i);
                F[a >> 0] = 0, e += r.length + 1
            })), 0
        } catch (r) {
            return "undefined" !== typeof P && r instanceof K || z(r), r.pa
        }
    },
    y: function (n, t) {
        try {
            var e = Wc();
            D[n >> 2] = e.length;
            var r = 0;
            return e.forEach((function (n) {
                r += n.length + 1
            })), D[t >> 2] = r, 0
        } catch (a) {
            return "undefined" !== typeof P && a instanceof K || z(a), a.pa
        }
    },
    b: function (n) {
        noExitRuntime || 0 < oa || (g.onExit && g.onExit(n), ua = !0), ca(n, new na(n))
    },
    A: function (n) {
        try {
            var t = Xb(n);
            if (null === t.fd) throw new K(8);
            t.Ma && (t.Ma = null);
            try {
                t.Y.close && t.Y.close(t)
            } catch (e) {
                throw e
            } finally {
                M[t.fd] = null
            }
            return t.fd = null, 0
        } catch (e) {
            return "undefined" !== typeof P && e instanceof K || z(e), e.pa
        }
    },
    z: function (n, t, e, r) {
        try {
            n: {
                for (var a = Xb(n), i = n = 0; i < e; i++) {
                    var o = D[t + (8 * i + 4) >> 2],
                        u = a,
                        c = D[t + 8 * i >> 2],
                        f = o,
                        s = void 0,
                        l = F;
                    if (0 > f || 0 > s) throw new K(28);
                    if (null === u.fd) throw new K(8);
                    if (1 === (2097155 & u.flags)) throw new K(8);
                    if (16384 === (61440 & u.node.mode)) throw new K(31);
                    if (!u.Y.read) throw new K(28);
                    var d = "undefined" !== typeof s;
                    if (d) {
                        if (!u.seekable) throw new K(70)
                    } else s = u.position;
                    var h = u.Y.read(u, l, c, f, s);
                    d || (u.position += h);
                    var p = h;
                    if (0 > p) {
                        var b = -1;
                        break n
                    }
                    if (n += p, p < o) break
                }
                b = n
            }
            return D[r >> 2] = b,
            0
        }
        catch (g) {
            return "undefined" !== typeof P && g instanceof K || z(g), g.pa
        }
    },
    q: function (n, t, e, r, a) {
        try {
            var i = Xb(n);
            return n = 4294967296 * e + (t >>> 0), -9007199254740992 >= n || 9007199254740992 <= n ? -61 : (Qb(
                    i, n, r), Xa = [i.position >>> 0, (Wa = i.position, 1 <= +Math.abs(Wa) ? 0 < Wa ? (0 |
                    Math.min(+Math.floor(Wa / 4294967296), 4294967295)) >>> 0 : ~~+Math.ceil((Wa -
                    +(~~Wa >>> 0)) / 4294967296) >>> 0 : 0)], D[a >> 2] = Xa[0], D[a + 4 >> 2] = Xa[1], i.Ma &&
                0 === n && 0 === r && (i.Ma = null), 0)
        } catch (o) {
            return "undefined" !== typeof P && o instanceof K || z(o), o.pa
        }
    },
    f: function (n, t, e, r) {
        try {
            n: {
                for (var a = Xb(n), i = n = 0; i < e; i++) {
                    var o = a,
                        u = D[t + 8 * i >> 2],
                        c = D[t + (8 * i + 4) >> 2],
                        f = void 0,
                        s = F;
                    if (0 > c || 0 > f) throw new K(28);
                    if (null === o.fd) throw new K(8);
                    if (0 === (2097155 & o.flags)) throw new K(8);
                    if (16384 === (61440 & o.node.mode)) throw new K(31);
                    if (!o.Y.write) throw new K(28);
                    o.seekable && 1024 & o.flags && Qb(o, 0, 2);
                    var l = "undefined" !== typeof f;
                    if (l) {
                        if (!o.seekable) throw new K(70)
                    } else f = o.position;
                    var d = o.Y.write(o, s, u, c, f, void 0);
                    l || (o.position += d);
                    try {
                        o.path && yb.onWriteToFile && yb.onWriteToFile(o.path)
                    } catch (b) {
                        B("FS.trackingDelegate['onWriteToFile']('" + o.path + "') threw an exception: " + b.message)
                    }
                    var h = d;
                    if (0 > h) {
                        var p = -1;
                        break n
                    }
                    n += h
                }
                p = n
            }
            return D[r >> 2] = p,
            0
        }
        catch (b) {
            return "undefined" !== typeof P && b instanceof K || z(b), b.pa
        }
    },
    s: function () {},
    w: function (n, t, e, r) {
        return cd(n, t, e, r)
    }
};
(function () {
    function n(n) {
        g.asm = n.exports, ta = g.asm.H, Ja = n = ta.buffer, g.HEAP8 = F = new Int8Array(n), g.HEAP16 = Da = new Int16Array(
                n), g.HEAP32 = D = new Int32Array(n), g.HEAPU8 = C = new Uint8Array(n), g.HEAPU16 = Ca = new Uint16Array(
                n), g.HEAPU32 = G = new Uint32Array(n), g.HEAPF32 = Ka = new Float32Array(n), g.HEAPF64 = La = new Float64Array(
                n), H = g.asm.O, Na.unshift(g.asm.I), I--, g.monitorRunDependencies && g.monitorRunDependencies(I),
            0 == I && (null !== Qa && (clearInterval(Qa), Qa = null), Ra && (n = Ra, Ra = null, n()))
    }

    function t(t) {
        n(t.instance)
    }

    function e(n) {
        return Va().then((function (n) {
            return WebAssembly.instantiate(n, r)
        })).then(n, (function (n) {
            B("failed to asynchronously prepare wasm: " + n), z(n)
        }))
    }
    var r = {
        a: gd
    };
    if (I++, g.monitorRunDependencies && g.monitorRunDependencies(I), g.instantiateWasm) try {
        return g.instantiateWasm(r, n)
    } catch (a) {
        return B("Module.instantiateWasm callback failed with error: " + a), !1
    }(function () {
        sa || "function" !== typeof WebAssembly.instantiateStreaming || Sa() || J.startsWith("file://") ||
            "function" !== typeof fetch ? e(t) : fetch(J, {
                credentials: "same-origin"
            }).then((function (n) {
                return WebAssembly.instantiateStreaming(n, r).then(t, (function (n) {
                    return B("wasm streaming compile failed: " + n), B(
                        "falling back to ArrayBuffer instantiation"), e(t)
                }))
            }))
    })()
})(), g.___wasm_call_ctors = function () {
    return (g.___wasm_call_ctors = g.asm.I).apply(null, arguments)
}, g.__malloc = function () {
    return (g.__malloc = g.asm.J).apply(null, arguments)
};
var fd = g._malloc = function () {
    return (fd = g._malloc = g.asm.K).apply(null, arguments)
};
g.__free = function () {
    return (g.__free = g.asm.L).apply(null, arguments)
};
var W = g._free = function () {
    return (W = g._free = g.asm.M).apply(null, arguments)
};
g._EgdeEnhance = function () {
    return (g._EgdeEnhance = g.asm.N).apply(null, arguments)
};
var Oc = g.___getTypeName = function () {
    return (Oc = g.___getTypeName = g.asm.P).apply(null, arguments)
};
g.___embind_register_native_and_builtin_types = function () {
    return (g.___embind_register_native_and_builtin_types = g.asm.Q).apply(null, arguments)
};
var hd, ob = g._memalign = function () {
    return (ob = g._memalign = g.asm.R).apply(null, arguments)
};

function na(n) {
    this.name = "ExitStatus", this.message = "Program terminated with exit(" + n + ")", this.status = n
}

function kd() {
    function n() {
        if (!hd && (hd = !0, g.calledRun = !0, !ua)) {
            if (g.noFSInit || Sb || (Sb = !0, Rb(), g.stdin = g.stdin, g.stdout = g.stdout, g.stderr = g.stderr, g.stdin ?
                    Ub("stdin", g.stdin) : Nb("/dev/tty", "/dev/stdin"), g.stdout ? Ub("stdout", null, g.stdout) : Nb(
                        "/dev/tty", "/dev/stdout"), g.stderr ? Ub("stderr", null, g.stderr) : Nb("/dev/tty1",
                        "/dev/stderr"), Ob("/dev/stdin", 0), Ob("/dev/stdout", 1), Ob("/dev/stderr", 1)), xb = !1, Ya(
                    Na), g.onRuntimeInitialized && g.onRuntimeInitialized(), g.postRun)
                for ("function" == typeof g.postRun && (g.postRun = [g.postRun]); g.postRun.length;) {
                    var n = g.postRun.shift();
                    Oa.unshift(n)
                }
            Ya(Oa)
        }
    }
    if (!(0 < I)) {
        if (g.preRun)
            for ("function" == typeof g.preRun && (g.preRun = [g.preRun]); g.preRun.length;) Pa();
        Ya(Ma), 0 < I || (g.setStatus ? (g.setStatus("Running..."), setTimeout((function () {
            setTimeout((function () {
                g.setStatus("")
            }), 1), n()
        }), 1)) : n())
    }
}
if (g.dynCall_viijii = function () {
        return (g.dynCall_viijii = g.asm.S).apply(null, arguments)
    }, g.dynCall_jiji = function () {
        return (g.dynCall_jiji = g.asm.T).apply(null, arguments)
    }, g.dynCall_iiiiij = function () {
        return (g.dynCall_iiiiij = g.asm.U).apply(null, arguments)
    }, g.dynCall_iiiiijj = function () {
        return (g.dynCall_iiiiijj = g.asm.V).apply(null, arguments)
    }, g.dynCall_iiiiiijj = function () {
        return (g.dynCall_iiiiiijj = g.asm.W).apply(null, arguments)
    }, g.addFunction = function (n, t) {
        if (!ra) {
            ra = new WeakMap;
            for (var e = 0; e < H.length; e++) {
                var r = H.get(e);
                r && ra.set(r, e)
            }
        }
        if (ra.has(n)) n = ra.get(n);
        else {
            if (qa.length) e = qa.pop();
            else {
                try {
                    H.grow(1)
                } catch (u) {
                    if (!(u instanceof RangeError)) throw u;
                    throw "Unable to grow wasm table. Set ALLOW_TABLE_GROWTH."
                }
                e = H.length - 1
            }
            try {
                H.set(e, n)
            } catch (u) {
                if (!(u instanceof TypeError)) throw u;
                if ("function" === typeof WebAssembly.Function) {
                    var a = {
                            i: "i32",
                            j: "i64",
                            f: "f32",
                            d: "f64"
                        },
                        i = {
                            parameters: [],
                            results: "v" == t[0] ? [] : [a[t[0]]]
                        };
                    for (r = 1; r < t.length; ++r) i.parameters.push(a[t[r]]);
                    t = new WebAssembly.Function(i, n)
                } else {
                    a = [1, 0, 1, 96], i = t.slice(0, 1), t = t.slice(1);
                    var o = {
                        i: 127,
                        j: 126,
                        f: 125,
                        d: 124
                    };
                    for (a.push(t.length), r = 0; r < t.length; ++r) a.push(o[t[r]]);
                    "v" == i ? a.push(0) : a = a.concat([1, o[i]]), a[1] = a.length - 2, t = new Uint8Array([0, 97, 115,
                            109, 1, 0, 0, 0].concat(a, [2, 7, 1, 1, 101, 1, 102, 0, 0, 7, 5, 1, 1, 102, 0, 0])), t =
                        new WebAssembly.Module(t), t = new WebAssembly.Instance(t, {
                            e: {
                                f: n
                            }
                        }).exports.f
                }
                H.set(e, t)
            }
            ra.set(n, e), n = e
        }
        return n
    }, Ra = function n() {
        hd || kd(), hd || (Ra = n)
    }, g.run = kd, g.preInit)
    for ("function" == typeof g.preInit && (g.preInit = [g.preInit]); 0 < g.preInit.length;) g.preInit.pop()();
kd();