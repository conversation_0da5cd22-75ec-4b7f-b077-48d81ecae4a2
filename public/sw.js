// This is a service worker that only caches specific TF files
const CACHE_NAME = 'matting-app-v1.0.0';
const TF_FILES_TO_CACHE = [
  '/js/matting/one.wasm',
  '/js/matting/two.wasm',
  '/js/matting/three.wasm',
  '/js/matting/four.wasm',
  '/js/matting/matting.tf',
  '/js/matting/toning.tf',
];

// Install event - cache specific TF files
self.addEventListener('install', (event) => {
  event.waitUntil(
    caches.open(CACHE_NAME)
      .then((cache) => {
        console.log('Opened cache');
        // cache.addAll()方法预缓存TF_FILES_TO_CACHE数组中列出的所有文件
        // cache.addAll()会自动发起网络请求来下载并缓存这些文件
        return cache.addAll(TF_FILES_TO_CACHE);
      })
  );
});

// Activate event - clean up old caches
self.addEventListener('activate', (event) => {
  const cacheWhitelist = [CACHE_NAME];
  event.waitUntil(
    caches.keys().then((cacheNames) => {
      return Promise.all(
        cacheNames.map((cacheName) => {
          if (cacheWhitelist.indexOf(cacheName) === -1) {
            return caches.delete(cacheName);
          }
        })
      );
    })
  );
});

// Fetch event - only intercept and cache the specific TF files
self.addEventListener('fetch', (event) => {
  // Check if this is one of the specific files we want to cache
  const url = new URL(event.request.url);
  const isTFFile = TF_FILES_TO_CACHE.includes(url.pathname);
  // console.log('isTFFile', isTFFile, url.pathname);

  // Only intercept the specific TF file requests
  if (!isTFFile) {
    return; // Let the browser handle all other requests normally
  }

  // Only handle the specific TF files
  event.respondWith(
    caches.match(event.request)
      .then((response) => {
        // Cache hit - return response
        if (response) {
          console.log('Serving from cache:', url.pathname);
          return response;
        }

        console.log('Fetching and caching:', url.pathname);
        return fetch(event.request).then(
          (response) => {
            // Check if we received a valid response
            if (!response || response.status !== 200) {
              return response;
            }

            // Clone the response
            const responseToCache = response.clone();

            caches.open(CACHE_NAME)
              .then((cache) => {
                cache.put(event.request, responseToCache);
                console.log('Cached successfully:', url.pathname);
              });

            return response;
          }
        );
      })
  );
});

// Handle messages from clients
self.addEventListener('message', (event) => {
  if (event.data && event.data.type === 'SKIP_WAITING') {
    self.skipWaiting();
  }
});
