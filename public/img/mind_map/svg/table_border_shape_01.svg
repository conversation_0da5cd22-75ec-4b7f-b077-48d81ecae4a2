<?xml version="1.0" encoding="UTF-8"?>
<svg width="54px" height="20px" viewBox="0 0 54 20" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>table_border_shape_01</title>
    <defs>
        <path d="M1256,186.999707 C1256,185.895299 1255.1053,185 1254.00085,185 L1083.99915,185 C1082.89505,185 1082,185.889893 1082,186.999707 L1082,219.000293 C1082,220.104701 1082.8947,221 1083.99915,221 L1254.00085,221 C1255.10495,221 1256,220.110107 1256,219.000293 L1256,186.999707 Z" id="path-1"></path>
        <filter x="-33.6%" y="-137.5%" width="167.2%" height="425.0%" filterUnits="objectBoundingBox" id="filter-2">
            <feMorphology radius="4" operator="dilate" in="SourceAlpha" result="shadowSpreadOuter1"></feMorphology>
            <feOffset dx="0" dy="9" in="shadowSpreadOuter1" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="14" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0  0 0 0 0.05 0" type="matrix" in="shadowBlurOuter1" result="shadowMatrixOuter1"></feColorMatrix>
            <feOffset dx="0" dy="6" in="SourceAlpha" result="shadowOffsetOuter2"></feOffset>
            <feGaussianBlur stdDeviation="8" in="shadowOffsetOuter2" result="shadowBlurOuter2"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0  0 0 0 0.08 0" type="matrix" in="shadowBlurOuter2" result="shadowMatrixOuter2"></feColorMatrix>
            <feMorphology radius="2" operator="erode" in="SourceAlpha" result="shadowSpreadOuter3"></feMorphology>
            <feOffset dx="0" dy="3" in="shadowSpreadOuter3" result="shadowOffsetOuter3"></feOffset>
            <feGaussianBlur stdDeviation="3" in="shadowOffsetOuter3" result="shadowBlurOuter3"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0  0 0 0 0.12 0" type="matrix" in="shadowBlurOuter3" result="shadowMatrixOuter3"></feColorMatrix>
            <feMerge>
                <feMergeNode in="shadowMatrixOuter1"></feMergeNode>
                <feMergeNode in="shadowMatrixOuter2"></feMergeNode>
                <feMergeNode in="shadowMatrixOuter3"></feMergeNode>
            </feMerge>
        </filter>
    </defs>
    <g id="2.0" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <rect id="矩形" fill="#171717" x="-4368" y="-2621" width="8706" height="3913"></rect>
        <g id="结构/粗细/方式" transform="translate(-1089.000000, -193.000000)">
            <rect fill="#F3F3F3" x="0" y="0" width="1966" height="1782"></rect>
            <g id="形状结合">
                <use fill="black" fill-opacity="1" filter="url(#filter-2)" xlink:href="#path-1"></use>
                <use fill="#FFFFFF" fill-rule="evenodd" xlink:href="#path-1"></use>
            </g>
            <g id="table_border_shape_01" transform="translate(1090.000000, 193.000000)" stroke="#333333" stroke-width="1.8">
                <g id="Rectangle">
                    <rect x="4.78787879" y="2.35294118" width="43.0909091" height="15.2941176" rx="4.8"></rect>
                </g>
            </g>
            <g id="table_border_shape_03" transform="translate(1169.000000, 203.000000) scale(-1, 1) translate(-1169.000000, -203.000000) translate(1142.666667, 193.000000)"></g>
        </g>
    </g>
</svg>