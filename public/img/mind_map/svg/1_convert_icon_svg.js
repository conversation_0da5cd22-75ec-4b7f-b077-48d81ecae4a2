/**
 * 获取svg图标，svg图标进行转换
 * 解决：UI每次修改图标，需要自己手动修改
 * 编译：node ./web/public/img/mind_map/svg/1_convert_icon_svg.js
 * 注意：钥匙.svg 里面，需要手动去除 mask=\"url(#mask-2)\"
 * svg title异常替换：>\d{2}-
 * icon_other_2 替换为错误
 */
const fs = require('fs');
const path = require('path');
const list = require('../../../../../simple-mind-map/src/svg/nodeIconList');
let nodeIconList = list.nodeIconList

// 读取本地 SVG 文件
function readSVGFile(filePath) {
    return new Promise((resolve, reject) => {
        fs.readFile(filePath, 'utf8', (err, data) => {
            if (err) {
                reject(err);
            } else {
                resolve(data);
            }
        });
    });
}

// 获取 nodeIconList 图标数据格式
nodeIconList = nodeIconList.map(item => {
    item.list.map(_ => {
        const titleRegex = /<title>(.*?)<\/title>/;
        const match = _.icon.match(titleRegex);
        const title = (match ? match[1] : "");
        _.icon = title + '.svg'

        return _
    })
    return item
})
// console.log(JSON.stringify(nodeIconList, null, ' '))

// 根据 nodeIconList 图标数据格式，获取需要的svg内容
nodeIconList = nodeIconList.map(item => {
    item.list.map(_ => {
        readSVGFile(
            path.join(__dirname, `./${_.icon.replace('/', ':')}`)
        ).then(res => {
            _.icon = res.replace('<?xml version="1.0" encoding="UTF-8"?>\n', '')
        })

        return _
    })
    return item
})

// 输出目标数据
setTimeout(() => {
    console.log('nodeIconList', JSON.stringify(nodeIconList))
    fs.writeFile('./nodeIconList.js', `
const nodeIconList = ${JSON.stringify(nodeIconList, null, '  ') }
module.exports = {
  nodeIconList,
};
    `, (err) => {
        if (err) {
            console.error('Error writing file:', err);
        } else {
            console.log('File written successfully.');
        }
    });
}, 2000);