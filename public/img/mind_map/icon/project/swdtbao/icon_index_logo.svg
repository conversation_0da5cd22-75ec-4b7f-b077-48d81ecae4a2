<?xml version="1.0" encoding="utf-8"?>
<!-- Generator: Adobe Illustrator 25.0.1, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->
<svg version="1.1" id="图层_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
	 viewBox="0 0 130 32" style="enable-background:new 0 0 130 32;" xml:space="preserve">
<style type="text/css">
	.st0{fill-rule:evenodd;clip-rule:evenodd;fill:url(#Rectangle_2_);}
	.st1{fill-rule:evenodd;clip-rule:evenodd;fill:#FF5959;}
	.st2{fill:#A2D2FF;}
	.st3{fill:#FFFFFF;}
	.st4{enable-background:new    ;}
	.st5{fill:#333333;}
</style>
<linearGradient id="Rectangle_2_" gradientUnits="userSpaceOnUse" x1="-224.6753" y1="424.2131" x2="-223.8701" y2="423.3051" gradientTransform="matrix(32 0 0 -32 7192 13575.4727)">
	<stop  offset="0" style="stop-color:#78BEFF"/>
	<stop  offset="1" style="stop-color:#1890FF"/>
</linearGradient>
<path id="Rectangle" class="st0" d="M7.8,0h16.3C28.5,0,32,3.5,32,7.8v16.3c0,4.3-3.5,7.8-7.8,7.8H7.8C3.5,32,0,28.5,0,24.2V7.8
	C0,3.5,3.5,0,7.8,0z"/>
<path id="Rectangle_1_" class="st1" d="M32,19.2v4.9c0,4.3-3.5,7.8-7.8,7.8H10.7l0,0L32,19.2z"/>
<g id="Group-58" transform="translate(8.000000, 13.000000)">
	<g id="Group-46">
		<g id="Path-13">
			<path class="st2" d="M0,7.9c-0.3,0-0.5-0.1-0.8-0.2C-1.6,7.3-2,6.3-1.5,5.4l3.2-6.2C2-1.3,2.5-1.7,3.1-1.7c0.6,0,1.2,0.2,1.5,0.7
				l2.9,4.1l2.7-4.1c0.3-0.5,0.9-0.7,1.4-0.8c0.6,0,1.1,0.3,1.4,0.8l4,6.2c0.5,0.8,0.3,1.9-0.5,2.4s-1.9,0.3-2.4-0.5l-2.6-4l-2.7,4
				C8.6,7.6,8.1,7.9,7.5,7.9c0,0,0,0,0,0c-0.6,0-1.1-0.3-1.4-0.7L3.4,3.3L1.5,7C1.2,7.6,0.6,7.9,0,7.9z"/>
		</g>
		<g id="Shape">
			<path class="st3" d="M0,7.9c-0.3,0-0.5-0.1-0.8-0.2C-1.6,7.3-2,6.3-1.5,5.4l3.2-6.2C2-1.3,2.5-1.7,3.1-1.7c0.6,0,1.2,0.2,1.5,0.7
				l4.3,6.2C9.4,6,9.3,7.1,8.5,7.6C7.7,8.2,6.6,8,6.1,7.2L3.4,3.3L1.5,7C1.2,7.6,0.6,7.9,0,7.9z M15.6,7.9c-0.6,0-1.1-0.3-1.4-0.8
				l-4-6.2c-0.5-0.8-0.3-1.9,0.5-2.4c0.8-0.5,1.9-0.3,2.4,0.5l4,6.2c0.5,0.8,0.3,1.9-0.5,2.4C16.3,7.8,16,7.9,15.6,7.9z"/>
		</g>
	</g>
</g>
<path id="Mind" class="st3" d="M19.9,28.1l1.8,3.1l0.5-0.3l-1.2-2.2l0,0l2.2,1.6l0.4-0.3l-0.3-2.7l0,0l1.2,2.2l0.5-0.3l-1.8-3.1
	l-0.6,0.3l0.4,3.1l0,0l-2.5-1.8L19.9,28.1z M24.1,25.6c-0.1,0.1-0.2,0.1-0.2,0.2c0,0.1,0,0.2,0,0.3c0.1,0.1,0.1,0.2,0.2,0.2
	c0.1,0,0.2,0,0.3,0c0.1-0.1,0.2-0.1,0.2-0.2c0-0.1,0-0.2,0-0.3c-0.1-0.1-0.1-0.2-0.2-0.2C24.3,25.5,24.2,25.6,24.1,25.6z M24.4,26.7
	l1.3,2.3l0.5-0.3l-1.3-2.3L24.4,26.7z M26.6,25.3c-0.1,0.1-0.2,0.2-0.3,0.3c-0.1,0.1-0.1,0.3-0.2,0.4L26,25.8L25.5,26l1.3,2.3
	l0.5-0.3l-0.8-1.4c-0.1-0.2-0.1-0.4-0.1-0.5c0-0.1,0.1-0.2,0.2-0.3c0.3-0.2,0.6-0.1,0.8,0.3l0.8,1.4l0.5-0.3L28,25.7
	C27.6,25.1,27.2,25,26.6,25.3z M29.1,22.7l0.7,1.2c-0.3-0.1-0.6-0.1-0.9,0c-0.3,0.2-0.5,0.5-0.6,0.8c0,0.3,0,0.6,0.2,1
	c0.2,0.3,0.5,0.6,0.8,0.7c0.3,0.1,0.7,0.1,1-0.1c0.3-0.2,0.5-0.4,0.5-0.7l0.1,0.2l0.5-0.3l-1.8-3.2L29.1,22.7z M29.3,24.3
	c0.2-0.1,0.3-0.1,0.5,0c0.2,0.1,0.3,0.2,0.4,0.4l0.1,0.1c0.1,0.2,0.2,0.4,0.2,0.6c0,0.2-0.1,0.4-0.3,0.5c-0.2,0.1-0.4,0.1-0.6,0
	c-0.2-0.1-0.3-0.2-0.5-0.5c-0.1-0.2-0.2-0.5-0.2-0.6C28.9,24.6,29,24.4,29.3,24.3z"/>
<g class="st4">
	<path class="st5" d="M41.8,18h2.2l-0.7,5.7H41L41.8,18z M42.1,9.3c0-0.2,0.1-0.5,0.2-0.7c0.2-0.2,0.4-0.3,0.7-0.3h11.9
		c0.2,0,0.5,0.1,0.6,0.3c0.2,0.2,0.3,0.4,0.3,0.7v6.6c0,0.2-0.1,0.4-0.3,0.6s-0.4,0.3-0.6,0.3H43c-0.3,0-0.5-0.1-0.7-0.3
		c-0.2-0.2-0.2-0.4-0.2-0.6V9.3z M47.9,9.9h-3.3c-0.1,0-0.2,0-0.2,0.1c-0.1,0.1-0.1,0.2-0.1,0.3v1.6h3.6V9.9z M47.9,15.4v-2.1h-3.6
		V15c0,0.1,0,0.2,0.1,0.2s0.2,0.1,0.2,0.1H47.9z M53.3,23.7h-7.7c-0.3,0-0.5-0.1-0.7-0.3c-0.2-0.2-0.2-0.4-0.2-0.6V18h2.1v3.9
		c0,0.1,0,0.2,0.1,0.2s0.2,0.1,0.2,0.1h6.5L53.3,23.7z M49,18h2.2l0.4,3.6h-2.2L49,18z M53.6,10.2c0-0.1,0-0.2-0.1-0.3
		c-0.1-0.1-0.2-0.1-0.2-0.1H50v1.9h3.6V10.2z M53.6,15v-1.8H50v2.1h3.2c0.1,0,0.2,0,0.2-0.1C53.6,15.2,53.6,15.1,53.6,15z
		 M56.9,23.7h-2.1L54,18h2.2L56.9,23.7z"/>
	<path class="st5" d="M60,14.7c-0.2,0-0.5-0.1-0.6-0.3s-0.3-0.4-0.3-0.6v-0.3c0-0.2,0-0.3,0.1-0.5l2.9-5.2h2.3l-2.8,5
		c0,0.1,0,0.2,0,0.2c0,0.1,0.1,0.1,0.2,0.1h0.5l1.3-2.2h2.3L62,18c0,0.1,0,0.2,0,0.2c0,0.1,0.1,0.1,0.2,0.1h3v1.4h-4.8
		c-0.2,0-0.5-0.1-0.6-0.3s-0.3-0.4-0.3-0.6v-0.3c0-0.2,0-0.3,0.1-0.4l1.9-3.4H60z M65.2,22.6l-6.1,0.7v-1.4l6.1-0.7V22.6z
		 M64.8,14.4l2.5-6.4h2.1l-1.1,2.9h2.2L70,7.9h2.2l0.6,2.9h1.9v1.4h-2.3v2.1h2.1v1.4h-2.1V18h2.1v1.4h-2.1v2.1h2.5V23H68v0.7h-2.1
		v-9.3H64.8z M70.2,12.2H68v2.1h2.2V12.2z M70.2,15.8H68V18h2.2V15.8z M70.2,19.4H68v2.1h2.2V19.4z"/>
	<path class="st5" d="M88.1,18.6h-11v-1.4h11v-0.8h2.1v0.8h2.6v1.4h-2.6v4.2c0,0.3-0.1,0.5-0.3,0.6c-0.2,0.2-0.4,0.3-0.7,0.3h-6.6
		l-0.4-1.4h5.4c0.2,0,0.4-0.1,0.4-0.4V18.6z M79.2,15.9c-0.2,0-0.5-0.1-0.6-0.3c-0.2-0.2-0.3-0.4-0.3-0.7V9.2c0-0.2,0.1-0.5,0.3-0.6
		c0.2-0.2,0.4-0.3,0.6-0.3h11.6c0.2,0,0.5,0.1,0.6,0.3c0.2,0.2,0.3,0.4,0.3,0.6V12c0,0.2-0.1,0.5-0.3,0.6s-0.4,0.3-0.6,0.3H80.5v1.2
		c0,0.2,0.1,0.4,0.4,0.4h11.3l-0.3,1.4H79.2z M82.2,21.6L79,19h2.8l3.2,2.6H82.2z M80.5,11.5h8.7c0.2,0,0.4-0.1,0.4-0.4v-1
		c0-0.2-0.1-0.4-0.4-0.4h-8.3c-0.2,0-0.4,0.1-0.4,0.4V11.5z"/>
	<path class="st5" d="M109.6,8.3c0.2,0,0.5,0.1,0.6,0.3s0.3,0.4,0.3,0.6v13.6c0,0.2-0.1,0.5-0.3,0.6s-0.4,0.3-0.6,0.3H96.3
		c-0.2,0-0.5-0.1-0.6-0.3s-0.3-0.4-0.3-0.6V9.2c0-0.2,0.1-0.5,0.3-0.6s0.4-0.3,0.6-0.3H109.6z M98,9.7c-0.2,0-0.4,0.1-0.4,0.4v11.8
		c0,0.2,0.1,0.4,0.4,0.4H108c0.2,0,0.4-0.1,0.4-0.4V10.1c0-0.2-0.1-0.4-0.4-0.4H98z M99.1,13.6c0.1,0.3,0.3,0.6,0.6,0.8l1.5,0.9
		L98,17.2h3.3l1.7-0.9l1.7,0.9h3.3l-3.3-1.9l1.6-0.9c0.5-0.4,0.7-0.7,0.7-1.2V12c0-0.2-0.1-0.5-0.3-0.6s-0.4-0.3-0.7-0.3h-4.9
		l0.5-1.1h-2.2L98,13.6H99.1z M102.5,19.4l4.4,2.5h-3.3l-4.4-2.5H102.5z M102.9,17.6l3.5,2h-3.3l-3.5-2H102.9z M101.2,12.8h-0.7
		l0.1-0.3h4c0.2,0,0.4,0.1,0.4,0.4V13c0,0.2-0.1,0.3-0.3,0.4l-1.6,0.9l-1.6-0.9c-0.2-0.1-0.3-0.3-0.3-0.5V12.8z"/>
	<path class="st5" d="M122,22.3h6.8v1.4h-15.8v-1.4h6.8v-3.7h-5.4v-1.4h5.4v-2.6H114v-1.4h13.9v1.4H122v2.6h5.4v1.4H122V22.3z
		 M113.4,10.3c0-0.3,0.1-0.5,0.2-0.7c0.2-0.2,0.4-0.3,0.6-0.3h5.7l-0.5-1.4h2.2l0.5,1.4h5.4c0.2,0,0.5,0.1,0.6,0.3
		c0.2,0.2,0.3,0.4,0.3,0.7v2h-2.1v-1.1c0-0.2-0.1-0.4-0.4-0.4h-10c-0.2,0-0.4,0.1-0.4,0.4v1.1h-2.1V10.3z M123.5,19.1h2.2l1.3,2.6
		h-2.2L123.5,19.1z"/>
</g>
</svg>
