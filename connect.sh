#!/bin/bash

# 远程服务器的 IP 地址或域名
REMOTE_HOST="************"

# 远程服务器的用户名
REMOTE_USER="work"

# 执行的命令
# cat /home/<USER>/.pm2/logs/log-out-1.log | grep -E '2024-04-(09|10)'
# cat /home/<USER>/.pm2/logs/log-out-1.log | grep -E "$(date -d '1 day ago' '+%Y-%m-%d').*V"
# cat /home/<USER>/.pm2/logs/log-out-1.log | grep -E "$(date -d 'yesterday' '+%Y-%m-%d').*V"
# cat /home/<USER>/.pm2/logs/log-out-1.log | grep -E "$(date '+%Y-%m-%d')"
# cat /home/<USER>/.pm2/logs/log-out-1.log | grep -E "$(date '+%Y-%m-%d').*V"
# COMMAND="source /etc/profile && pm2 log log"
COMMAND="source /etc/profile && cat /home/<USER>/.pm2/logs/log-out-1.log | grep -E \"$(date '+%Y-%m-%d').*V\""
echo "$COMMAND"

# 使用SSH密钥登录到远程服务器并执行命令
# ssh -i ~/.ssh/id_rsa $REMOTE_USER@$REMOTE_HOST "$COMMAND"
ssh -t -i ~/.ssh/id_rsa $REMOTE_USER@$REMOTE_HOST "$COMMAND; bash"
