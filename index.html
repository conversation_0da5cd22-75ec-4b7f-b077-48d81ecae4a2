<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta http-equiv="Expires" content="0" />
    <meta http-equiv="Pragma" content="no-cache" />
    <meta http-equiv="Cache-control" content="no-cache" />
    <meta http-equiv="Cache" content="no-cache" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <!-- <meta http-equiv="Content-Security-Policy" content="default-src 'self' https:; font-src 'self' data:;"> -->
    <meta name="viewport" content="width=device-width,initial-scale=1.0" />
    <link
      id="favicon"
      rel="icon"
      href="/img/mind_map/icon/project/qnswdt/icon_about.png"
    />
    <title>图片转换器</title>
  </head>
  <body>
    <div id="app"></div>
    <script lang="ts" type="module" src="/src/main.ts"></script>
    <script>
      var _hmt = _hmt || [];
      (function () {
        var hostName = location.hostname
        var traceConfig = {
          'pic.miaoji66.com': '36b990535c0b61c87bbfcd8995f7d753',
          'wnpic.miaoji66.com': '31144c33cfe755d88314803763e3ae6d',
        }
        if (!traceConfig[hostName]) return
        var hm = document.createElement('script');
        hm.src = 'https://hm.baidu.com/hm.js?' + traceConfig[hostName];
        var s = document.getElementsByTagName('script')[0];
        s.parentNode.insertBefore(hm, s);
      })();
      var isLocal = location.hostname === 'localhost'
      function trackEvent(category = '', action = '', label = '', value = '') {
        const info = ['_trackEvent', category, action, label, value]
        _hmt.push(info)
        !isLocal && fetch(`//api.bestmind365.com/api/track?info=${location.host}|${info.filter(item => item).join('|')}`)
      }
    </script>
  </body>
</html>
