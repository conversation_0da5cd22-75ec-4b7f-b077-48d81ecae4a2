declare module '*.vue' {
  import { DefineComponent } from 'vue'
  const component: DefineComponent<{}, {}, any>
  export default component
}

import { ComponentCustomProperties } from 'vue'
declare module 'vue' {
  interface ComponentCustomProperties {
    $oss: any;
    $filter?: {
      tools: {
        debounce: <T extends (...args: any[]) => any>(fn: T, delay: number) => T
      }
    }
  }
}

declare module '*.svg'
declare module '*.png'
declare module '*.jpg'
declare module '*.jpeg'
declare module '*.gif'
declare module '*.bmp'
declare module '*.tiff'
declare module 'ali-oss'
declare module 'lrz'
// declare module 'element-plus'
// declare module 'element-plus/es/locale/lang/zh-cn'
// declare module 'vite'
// declare module 'vite-plugin-mock'
// declare module 'vite-plugin-svg-icons'
// declare module 'vite-plugin-vue-setup-extend'
// declare module '@vitejs/plugin-vue'
// declare module 'unplugin-auto-import/vite'
// declare module 'unplugin-vue-components/vite'
// declare module 'unplugin-vue-components/resolvers'
declare module 'path'
