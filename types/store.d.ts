import { ComponentCustomProperties } from 'vue'
import { RouteRecordRaw } from 'vue-router'
import util from '@haluo/util'

declare interface App {
  device: string
  size: string
  sidebar: {
    opened: boolean
    withoutAnimation: boolean
  }
}

declare interface Permission {
  routes: RouteRecordRaw[]
  addRoutes: any
}

declare interface TagsView {
  visitedViews: any[]
  cachedViews: any[]
}

declare interface User {
  user: string
  status: string | number
  code: string | number
  userInfo: any
  token: string
  roles: string | undefined
  name: string | undefined
  avatar: string | undefined
  favcRoles: array
  menuList: array
  uid: string
  introduction: string
}

declare interface State {
  app: App
  permission: Permission
  tagsView: TagsView
  user: User
}

declare module '@vue/runtime-core' {
  interface ComponentCustomProperties {
    $filter: any
  }
}

interface util {
  [propsName: string]: any
}
