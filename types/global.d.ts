// import util from '@haluo/util'

declare function getCurrentInstance() {
  return {
    proxy: any
  }
}
declare interface Window {
  ipcRendererApi: any
  bridge: any
  execLenovoLogin: any
  Login: any
  GetToken: any
  Logout: any
  loginCallback: any
  logoutCallback: any
  LXOpenSystem: any
  trackEvent: any
  EyeDropper: any
  get360PayUrlCallback: any

  RoomPaasSdk: any
  isLocal: any
  isLenovo: any
  isElectron: any
  isIframe: any
  isWN: any
  isMJ: any
  isMac: any
  isWindow: any
  loadJs: any
  loadCss: any
  hostName: string
  global: any
  Native: any
  eruda: any
}
// declare module '@haluo/util' {
//   interface Iutil {
//     [propsName: string]: any
//     cookie: any
//     filter: any
//     match: any
//     format: any
//     number: any
//     tools: any
//     date: any
//   }
//   const util: Iutil
//   export default util
// }

declare module 'qrcodejs2' {
  const qrcode: any
  export default qrcode
}
declare module 'blueimp-md5' {
  const md5: any
  export default md5
}
